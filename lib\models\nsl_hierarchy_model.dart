import 'package:flutter/material.dart';

@immutable
class BetBreakdown {
  final int gos;
  final int los;
  final int npFunctions;
  final int inputOutputStacks;
  final int subordinateNsl;

  const BetBreakdown({
    required this.gos,
    required this.los,
    required this.npFunctions,
    required this.inputOutputStacks,
    required this.subordinateNsl,
  });

  factory BetBreakdown.fromJson(Map<String, dynamic> json) {
    return BetBreakdown(
      gos: json['gos'] ?? 0,
      los: json['los'] ?? 0,
      npFunctions: json['np_functions'] ?? 0,
      inputOutputStacks: json['input_output_stacks'] ?? 0,
      subordinateNsl: json['subordinate_nsl'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'gos': gos,
      'los': los,
      'np_functions': npFunctions,
      'input_output_stacks': inputOutputStacks,
      'subordinate_nsl': subordinateNsl,
    };
  }
}

@immutable
class FinancialSummary {
  final String revenue;
  final String cost;
  final String margin;

  const FinancialSummary({
    required this.revenue,
    required this.cost,
    required this.margin,
  });

  factory FinancialSummary.fromJson(Map<String, dynamic> json) {
    return FinancialSummary(
      revenue: json['revenue']?.toString() ?? '',
      cost: json['cost']?.toString() ?? '',
      margin: json['margin']?.toString() ?? json['efficiency']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'revenue': revenue,
      'cost': cost,
      'margin': margin,
    };
  }
}

@immutable
class NSLHierarchyData {
  final String id;
  final String title;
  final String type;
  final String? parent;
  final int totalBets;
  final BetBreakdown betBreakdown;
  final FinancialSummary financialSummary;
  final List<String> children;
  final String levelName;
  final String level; // M4, M3, M2, M1
  final bool isExpanded;
  final String? employeeId; // For M1 level
  final List<NSLHierarchyData> childNodes;

  const NSLHierarchyData({
    required this.id,
    required this.title,
    required this.type,
    this.parent,
    required this.totalBets,
    required this.betBreakdown,
    required this.financialSummary,
    this.children = const [],
    required this.levelName,
    required this.level,
    this.isExpanded = true,
    this.employeeId,
    this.childNodes = const [],
  });

  factory NSLHierarchyData.fromJson(Map<String, dynamic> json, String level, String levelName) {
    final childrenJson = json['children'] as List<dynamic>? ?? [];
    final children = childrenJson.map((child) => child.toString()).toList();

    // Set initial expansion state: only M4 should be expanded by default
    bool defaultExpanded = level == 'M4';

    return NSLHierarchyData(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      parent: json['parent']?.toString(),
      totalBets: json['total_bets'] ?? 0,
      betBreakdown: BetBreakdown.fromJson(json['bet_breakdown'] ?? {}),
      financialSummary: FinancialSummary.fromJson(json['financial_summary'] ?? {}),
      children: children,
      levelName: levelName,
      level: level,
      isExpanded: json['isExpanded'] ?? defaultExpanded,
      employeeId: json['employee_id']?.toString(),
      childNodes: const [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'parent': parent,
      'total_bets': totalBets,
      'bet_breakdown': betBreakdown.toJson(),
      'financial_summary': financialSummary.toJson(),
      'children': children,
      'level_name': levelName,
      'level': level,
      'isExpanded': isExpanded,
      'employee_id': employeeId,
      'child_nodes': childNodes.map((child) => child.toJson()).toList(),
    };
  }

  // Helper method to get all nodes in a flat list (for search functionality)
  List<NSLHierarchyData> getAllNodes() {
    List<NSLHierarchyData> allNodes = [this];
    for (var child in childNodes) {
      allNodes.addAll(child.getAllNodes());
    }
    return allNodes;
  }

  // Helper method to update expansion state
  NSLHierarchyData copyWith({
    String? id,
    String? title,
    String? type,
    String? parent,
    int? totalBets,
    BetBreakdown? betBreakdown,
    FinancialSummary? financialSummary,
    List<String>? children,
    String? levelName,
    String? level,
    bool? isExpanded,
    String? employeeId,
    List<NSLHierarchyData>? childNodes,
  }) {
    return NSLHierarchyData(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      parent: parent ?? this.parent,
      totalBets: totalBets ?? this.totalBets,
      betBreakdown: betBreakdown ?? this.betBreakdown,
      financialSummary: financialSummary ?? this.financialSummary,
      children: children ?? this.children,
      levelName: levelName ?? this.levelName,
      level: level ?? this.level,
      isExpanded: isExpanded ?? this.isExpanded,
      employeeId: employeeId ?? this.employeeId,
      childNodes: childNodes ?? this.childNodes,
    );
  }
}

@immutable
class NSLNode {
  final String id;
  final String title;
  final String type;
  final String? parent;
  final int totalBets;
  final BetBreakdown betBreakdown;
  final FinancialSummary financialSummary;
  final String levelName;
  final String level; // M4, M3, M2, M1
  final Color levelColor;
  final List<NSLNode> children;
  final bool isExpanded;
  final String? employeeId;
  final NSLHierarchyData originalData;

  const NSLNode({
    required this.id,
    required this.title,
    required this.type,
    this.parent,
    required this.totalBets,
    required this.betBreakdown,
    required this.financialSummary,
    required this.levelName,
    required this.level,
    this.levelColor = Colors.grey,
    this.children = const [],
    this.isExpanded = true,
    this.employeeId,
    required this.originalData,
  });

  NSLNode copyWith({
    String? id,
    String? title,
    String? type,
    String? parent,
    int? totalBets,
    BetBreakdown? betBreakdown,
    FinancialSummary? financialSummary,
    String? levelName,
    String? level,
    Color? levelColor,
    List<NSLNode>? children,
    bool? isExpanded,
    String? employeeId,
    NSLHierarchyData? originalData,
  }) {
    return NSLNode(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      parent: parent ?? this.parent,
      totalBets: totalBets ?? this.totalBets,
      betBreakdown: betBreakdown ?? this.betBreakdown,
      financialSummary: financialSummary ?? this.financialSummary,
      levelName: levelName ?? this.levelName,
      level: level ?? this.level,
      levelColor: levelColor ?? this.levelColor,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      employeeId: employeeId ?? this.employeeId,
      originalData: originalData ?? this.originalData,
    );
  }

  factory NSLNode.fromNSLData(NSLHierarchyData data) {
    // Convert children recursively
    final childNodes = data.childNodes.map((child) => NSLNode.fromNSLData(child)).toList();
    
    return NSLNode(
      id: data.id,
      title: data.title,
      type: data.type,
      parent: data.parent,
      totalBets: data.totalBets,
      betBreakdown: data.betBreakdown,
      financialSummary: data.financialSummary,
      levelName: data.levelName,
      level: data.level,
      levelColor: getLevelColor(data.level),
      children: childNodes,
      isExpanded: data.isExpanded,
      employeeId: data.employeeId,
      originalData: data,
    );
  }

  static Color getLevelColor(String level) {
    switch (level) {
      case 'M4':
        return Colors.indigo;
      case 'M3':
        return Colors.blue;
      case 'M2':
        return Colors.green;
      case 'M1':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

class NSLHierarchyBuilder {
  static NSLNode? buildHierarchyFromNSLData(Map<String, dynamic> jsonData) {
    try {
      final nslHierarchy = jsonData['nsl_hierarchy'];
      if (nslHierarchy == null) return null;

      // Build the complete hierarchy
      final allNodes = <String, NSLHierarchyData>{};
      
      // Parse M4 (Executive level)
      final m4Data = nslHierarchy['M4'];
      if (m4Data != null) {
        final m4Node = NSLHierarchyData.fromJson(m4Data['node'], 'M4', m4Data['level_name']);
        allNodes[m4Node.id] = m4Node;
      }

      // Parse M3 (Department level)
      final m3Data = nslHierarchy['M3'];
      if (m3Data != null && m3Data['nodes'] != null) {
        for (var nodeJson in m3Data['nodes']) {
          final m3Node = NSLHierarchyData.fromJson(nodeJson, 'M3', m3Data['level_name']);
          allNodes[m3Node.id] = m3Node;
        }
      }

      // Parse M2 (Team level)
      final m2Data = nslHierarchy['M2'];
      if (m2Data != null && m2Data['nodes'] != null) {
        for (var nodeJson in m2Data['nodes']) {
          final m2Node = NSLHierarchyData.fromJson(nodeJson, 'M2', m2Data['level_name']);
          allNodes[m2Node.id] = m2Node;
        }
      }

      // Parse M1 (Individual Employee level)
      final m1Data = nslHierarchy['M1'];
      if (m1Data != null && m1Data['nodes'] != null) {
        for (var nodeJson in m1Data['nodes']) {
          final m1Node = NSLHierarchyData.fromJson(nodeJson, 'M1', m1Data['level_name']);
          allNodes[m1Node.id] = m1Node;
        }
      }

      // Build parent-child relationships
      final Map<String, List<NSLHierarchyData>> childrenMap = {};
      NSLHierarchyData? rootNode;

      // Initialize children map
      for (var node in allNodes.values) {
        childrenMap[node.id] = [];
        if (node.level == 'M4') {
          rootNode = node;
        }
      }

      // Build relationships based on children IDs
      for (var node in allNodes.values) {
        for (var childId in node.children) {
          if (allNodes.containsKey(childId)) {
            childrenMap[node.id]!.add(allNodes[childId]!);
          }
        }
      }

      // Recursively build the tree with child nodes
      NSLHierarchyData buildNodeWithChildren(NSLHierarchyData node) {
        final children = childrenMap[node.id] ?? [];
        final childNodes = children.map((child) => buildNodeWithChildren(child)).toList();
        return node.copyWith(childNodes: childNodes);
      }

      if (rootNode != null) {
        final completeTree = buildNodeWithChildren(rootNode);
        return NSLNode.fromNSLData(completeTree);
      }

      return null;
    } catch (e) {
      print('Error building NSL hierarchy: $e');
      return null;
    }
  }

  static NSLNode? buildFilteredHierarchy(List<NSLHierarchyData> nodes, String rootNodeId) {
    final rootNode = nodes.where((node) => node.id == rootNodeId).firstOrNull;
    if (rootNode == null) return null;

    // Find all subordinates of the root node
    List<NSLHierarchyData> filteredNodes = [rootNode];
    _addSubordinates(nodes, rootNodeId, filteredNodes);

    // Build hierarchy from filtered nodes
    // This is a simplified version - you might need to adapt based on your needs
    return NSLNode.fromNSLData(rootNode);
  }

  static void _addSubordinates(List<NSLHierarchyData> allNodes, String parentId, List<NSLHierarchyData> result) {
    final subordinates = allNodes.where((node) => node.parent == parentId).toList();
    for (var subordinate in subordinates) {
      if (!result.any((node) => node.id == subordinate.id)) {
        result.add(subordinate);
        _addSubordinates(allNodes, subordinate.id, result);
      }
    }
  }
}
