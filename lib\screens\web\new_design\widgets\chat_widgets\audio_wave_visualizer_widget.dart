import 'package:flutter/material.dart';
import 'package:waveform_recorder/waveform_recorder.dart';
import 'package:nsl/utils/logger.dart';

/// A widget that displays a real-time audio wave visualization during recording
class AudioWaveVisualizerWidget extends StatefulWidget {
  /// Callback when recording is complete
  final Function(String? filePath)? onRecordingComplete;

  /// Whether the recording is active
  final bool isActive;

  /// The height of the waveform
  final double height;

  /// The color of the waveform
  final Color color;

  const AudioWaveVisualizerWidget({
    super.key,
    this.onRecordingComplete,
    this.isActive = true,
    this.height = 40,
    this.color = Colors.red,
  });

  @override
  State<AudioWaveVisualizerWidget> createState() =>
      _AudioWaveVisualizerWidgetState();
}

class _AudioWaveVisualizerWidgetState extends State<AudioWaveVisualizerWidget> {
  late final WaveformRecorderController _waveController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _waveController = WaveformRecorderController();
    _initializeRecorder();
  }

  @override
  void didUpdateWidget(AudioWaveVisualizerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Handle changes in isActive state
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startRecording();
      } else {
        _stopRecording();
      }
    }
  }

  Future<void> _initializeRecorder() async {
    try {
      // Initialize the recorder
      setState(() {
        _isInitialized = true;
      });

      // Start recording if widget is active
      if (widget.isActive) {
        await _startRecording();
      }
    } catch (e) {
      Logger.error('Error initializing audio recorder: $e');
      setState(() {
        _isInitialized = false;
      });
    }
  }

  Future<void> _startRecording() async {
    if (!_isInitialized) return;

    try {
      await _waveController.startRecording();
      Logger.info('Audio recording started');
    } catch (e) {
      Logger.error('Error starting audio recording: $e');
    }
  }

  Future<void> _stopRecording() async {
    if (!_isInitialized || !_waveController.isRecording) return;

    try {
      await _waveController.stopRecording();
      Logger.info('Audio recording stopped');

      // Call the callback with the file path
      if (widget.onRecordingComplete != null && _waveController.file != null) {
        widget.onRecordingComplete!(_waveController.file!.path);
      }
    } catch (e) {
      Logger.error('Error stopping audio recording: $e');
    }
  }

  @override
  void dispose() {
    // Stop recording if still active
    if (_waveController.isRecording) {
      _waveController.stopRecording();
    }
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: _isInitialized
          ? WaveformRecorder(
              height: widget.height,
              controller: _waveController,
              // waveStyle: PaintingStyle.fill,
              waveColor: widget.color,
              // backgroundColor: Colors.transparent,
              onRecordingStopped: () {
                if (widget.onRecordingComplete != null &&
                    _waveController.file != null) {
                  widget.onRecordingComplete!(_waveController.file!.path);
                }
              },
            )
          : Center(
              child: Text(
                'Initializing audio...',
                style: TextStyle(color: widget.color),
              ),
            ),
    );
  }
}
