import 'dart:convert';
import 'dart:html' as html;
import '../models/line_selection.dart';
import '../models/solution.dart';
import '../models/mapping.dart';

class ExportServiceImpl {
  // List of common Java keywords to extract
  static final List<String> _javaKeywordsList = [
    'abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char',
    'class', 'const',
    'continue', 'default', 'do', 'double', 'else', 'enum', 'extends', 'final',
    'finally', 'float',
    'for', 'goto', 'if', 'implements', 'import', 'instanceof', 'int',
    'interface', 'long', 'native',
    'new', 'package', 'private', 'protected', 'public', 'return', 'short',
    'static', 'strictfp', 'super',
    'switch', 'synchronized', 'this', 'throw', 'throws', 'transient', 'try',
    'void', 'volatile', 'while',
    // Common Java types and annotations
    'String', 'List', 'Map', 'Set', 'ArrayList', 'HashMap', 'HashSet',
    'LinkedList',
    'Override', 'Deprecated', 'SuppressWarnings', 'FunctionalInterface',
    // Common Java methods
    'equals', 'hashCode', 'toString', 'compareTo', 'clone', 'finalize',
    // Common Java annotations
    '@Override', '@Deprecated', '@SuppressWarnings', '@FunctionalInterface'
  ];

  // Extract Java keywords from a line of Java code
  static List<String> _extractJavaKeywords(dynamic javaLine) {
    // Convert LineSelection to String if needed
    String lineContent =
        javaLine is LineSelection ? javaLine.content : javaLine;
    final List<String> foundKeywords = [];

    // Simple approach: check if each keyword is in the line
    for (final keyword in _javaKeywordsList) {
      // Use regex to match whole words only
      final RegExp regex = RegExp('\\b$keyword\\b');
      if (regex.hasMatch(lineContent)) {
        foundKeywords.add(keyword);
      }
    }

    return foundKeywords;
  }

  /// Export a solution and its mappings as a JSON file
  static Future<String> exportSolutionWithMappings(
      Solution solution, List<Mapping> mappings) async {
    try {
      // Separate line mappings and word mappings
      final lineMappings =
          mappings.where((m) => m.level == MappingLevel.line).toList();
      final wordMappings =
          mappings.where((m) => m.level == MappingLevel.word).toList();

      // Create a hierarchical structure for mappings
      final List<Map<String, dynamic>> hierarchicalMappings = [];

      // Extract Java keywords and their explanations
      final Map<String, String> javaKeywords = {};

      // Process each line mapping
      for (final lineMapping in lineMappings) {
        // Convert the line mapping to JSON
        final Map<String, dynamic> lineMappingJson = lineMapping.toJson();

        // Find all word mappings that have this line mapping as parent
        final childMappings =
            wordMappings.where((m) => m.parentId == lineMapping.id).map((m) {
          // Extract Java keywords from word mappings
          if (m.tag.isNotEmpty && m.javaLines.isNotEmpty) {
            // Use the tag as the explanation for the Java keyword
            for (final javaLine in m.javaLines) {
              // Extract Java keywords from the line
              // This is a simple implementation - in a real app, you'd use a more sophisticated parser
              final keywords = _extractJavaKeywords(javaLine);
              for (final keyword in keywords) {
                javaKeywords[keyword] = m.tag;
              }
            }
          }
          return m.toJson();
        }).toList();

        // Add children to the line mapping
        if (childMappings.isNotEmpty) {
          lineMappingJson['children'] = childMappings;
        }

        // Add the line mapping with its children to the hierarchical mappings
        hierarchicalMappings.add(lineMappingJson);
      }

      // Create a combined data structure
      final Map<String, dynamic> exportData = {
        'solution': solution.toJson(),
        'mappings': hierarchicalMappings,
        'javaKeywords': javaKeywords,
      };

      // Convert to JSON
      final jsonString = jsonEncode(exportData);

      // Create a file name based on the solution title
      final fileName =
          '${solution.title.replaceAll(' ', '_').toLowerCase()}_export.json';

      // Create a Blob containing the JSON data
      final blob = html.Blob([jsonString], 'application/json');

      // Create a URL for the Blob
      final url = html.Url.createObjectUrlFromBlob(blob);

      // Create an anchor element and set its properties
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..style.display = 'none';

      // Add the anchor to the document body
      html.document.body?.children.add(anchor);

      // Trigger a click on the anchor to start the download
      anchor.click();

      // Clean up
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      return fileName;
    } catch (e) {
      print('Error exporting solution: $e');
      return '';
    }
  }
}
