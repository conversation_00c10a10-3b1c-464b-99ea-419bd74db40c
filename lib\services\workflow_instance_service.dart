import 'package:dio/dio.dart';
import '../utils/logger.dart';
import '../utils/environment.dart';
import '../models/workflow/workflow_instance_model.dart';
import '../models/workflow/workflow_start_model.dart';
import '../models/workflow/local_objective_inputs_model.dart';
import '../models/workflow/local_objective_execution_model.dart';
import 'base_api_service.dart';
import 'service_locator.dart';
import 'auth_service.dart';

class WorkflowInstanceService extends BaseApiService {
  // Get auth service instance
  final AuthService _authService = ServiceLocator().authService;
  // Get environment instance
  final Environment _env = Environment.instance;

  /// Create a workflow instance
  /// API Call 1: POST http://10.26.1.52:8000/api/v2/workflow_instances/?tenant_id={tenant_id}
  Future<WorkflowInstanceModel> createWorkflowInstance({
    required String objectiveId,
    required String tenantId,
    required String userId,
  }) async {
    try {
      Logger.info('Creating workflow instance for objective: $objectiveId');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available to create workflow instance');
        throw Exception('Authentication required');
      }

      // Prepare request body
      final requestBody = {
        "go_id": objectiveId,
        "tenant_id": tenantId,
        "user_id": userId,
        "test_mode": false,
      };

      // Prepare URL with tenant_id query parameter
      final url =
          '${_env.transactionApiBaseUrl}/api/v2/workflow_instances/?tenant_id=$tenantId';

      Logger.info('Making API call to: $url');
      Logger.info('Request body: $requestBody');

      final response = await dio.post(
        url,
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Create workflow instance response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        Logger.info('Workflow instance created successfully');
        return WorkflowInstanceModel.fromJson(response.data);
      } else {
        final errorMessage =
            'Failed to create workflow instance: ${response.statusCode}';
        Logger.error(errorMessage);

        // Show alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          throw Exception(response.data['message']);
        } else {
          throw Exception(errorMessage);
        }
      }
    } on DioException catch (e) {
      Logger.error('DioException while creating workflow instance: $e');

      // Handle 401 status code - retry with new token
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to get new token and retry...');
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with new token
          return await createWorkflowInstance(
            objectiveId: objectiveId,
            tenantId: tenantId,
            userId: userId,
          );
        }
      }

      // Extract error message from response
      String errorMessage = 'Failed to create workflow instance';
      if (e.response?.data != null && e.response!.data is Map) {
        final responseData = e.response!.data as Map;
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('detail')) {
          errorMessage = responseData['detail'];
        }
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Exception while creating workflow instance: $e');
      throw Exception('Failed to create workflow instance: $e');
    }
  }

  /// Start a workflow instance
  /// API Call 2: POST http://10.26.1.52:8000/api/v2/workflow_instances/{instance_id}/start?tenant_id={tenant_id}
  Future<WorkflowStartModel> startWorkflowInstance({
    required String instanceId,
    required String tenantId,
    required String userId,
  }) async {
    try {
      Logger.info('Starting workflow instance: $instanceId');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available to start workflow instance');
        throw Exception('Authentication required');
      }

      // Prepare request body
      final requestBody = {
        "user_id": userId,
      };

      // Prepare URL with instance_id and tenant_id query parameter
      final url =
          '${_env.transactionApiBaseUrl}/api/v2/workflow_instances/$instanceId/start?tenant_id=$tenantId';

      Logger.info('Making API call to: $url');
      Logger.info('Request body: $requestBody');

      final response = await dio.post(
        url,
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Start workflow instance response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        Logger.info('Workflow instance started successfully');
        return WorkflowStartModel.fromJson(response.data);
      } else {
        final errorMessage =
            'Failed to start workflow instance: ${response.statusCode}';
        Logger.error(errorMessage);

        // Show alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          throw Exception(response.data['message']);
        } else {
          throw Exception(errorMessage);
        }
      }
    } on DioException catch (e) {
      Logger.error('DioException while starting workflow instance: $e');

      // Handle 401 status code - retry with new token
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to get new token and retry...');
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with new token
          return await startWorkflowInstance(
            instanceId: instanceId,
            tenantId: tenantId,
            userId: userId,
          );
        }
      }

      // Extract error message from response
      String errorMessage = 'Failed to start workflow instance';
      if (e.response?.data != null && e.response!.data is Map) {
        final responseData = e.response!.data as Map;
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('detail')) {
          errorMessage = responseData['detail'];
        }
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Exception while starting workflow instance: $e');
      throw Exception('Failed to start workflow instance: $e');
    }
  }

  /// Fetch local objective inputs
  /// API Call 3: GET http://10.26.1.52:8000/api/v2/local_objectives/instances/{instance_id}/inputs?tenant_id={tenant_id}
  Future<LocalObjectiveInputsModel> fetchLocalObjectiveInputs({
    required String instanceId,
    required String tenantId,
  }) async {
    try {
      Logger.info('Fetching local objective inputs for instance: $instanceId');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error(
            'No valid token available to fetch local objective inputs');
        throw Exception('Authentication required');
      }

      // Prepare URL with instance_id and tenant_id query parameter
      final url =
          '${_env.transactionApiBaseUrl}/api/v2/local_objectives/instances/$instanceId/inputs?tenant_id=$tenantId';

      Logger.info('Making API call to: $url');

      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Fetch local objective inputs response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Local objective inputs fetched successfully');
        return LocalObjectiveInputsModel.fromJson(response.data);
      } else {
        final errorMessage =
            'Failed to fetch local objective inputs: ${response.statusCode}';
        Logger.error(errorMessage);

        // Show alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          throw Exception(response.data['message']);
        } else {
          throw Exception(errorMessage);
        }
      }
    } on DioException catch (e) {
      Logger.error('DioException while fetching local objective inputs: $e');

      // Handle 401 status code - retry with new token
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to get new token and retry...');
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with new token
          return await fetchLocalObjectiveInputs(
            instanceId: instanceId,
            tenantId: tenantId,
          );
        }
      }

      // Extract error message from response
      String errorMessage = 'Failed to fetch local objective inputs';
      if (e.response?.data != null && e.response!.data is Map) {
        final responseData = e.response!.data as Map;
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('detail')) {
          errorMessage = responseData['detail'];
        }
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Exception while fetching local objective inputs: $e');
      throw Exception('Failed to fetch local objective inputs: $e');
    }
  }

  /// Fetch dependent options for a field
  /// API Call: GET http://10.26.1.52:8000/api/v2/local_objectives/instances/{instance_id}/inputs/{input_id}/dependent-options?parent_value={parent_value}&tenant_id={tenant_id}
  Future<List<Map<String, dynamic>>> fetchDependentOptions({
    required String instanceId,
    required String inputId,
    required String parentValue,
    required String tenantId,
  }) async {
    try {
      Logger.info(
          'Fetching dependent options for input: $inputId, parent value: $parentValue');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available to fetch dependent options');
        throw Exception('Authentication required');
      }

      // Prepare URL with parameters
      final url =
          '${_env.transactionApiBaseUrl}/api/v2/local_objectives/instances/$instanceId/inputs/$inputId/dependent-options?parent_value=${Uri.encodeComponent(parentValue)}&tenant_id=$tenantId';

      Logger.info('Making API call to: $url');

      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Fetch dependent options response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        Logger.info('Dependent options fetched successfully');

        // Parse response as list of options
        if (response.data is List) {
          final options = (response.data as List).map((item) {
            if (item is Map<String, dynamic>) {
              return {
                'value': item['value']?.toString() ?? '',
                'label': item['label']?.toString() ??
                    item['value']?.toString() ??
                    '',
              };
            } else {
              // Handle simple string values
              return {
                'value': item.toString(),
                'label': item.toString(),
              };
            }
          }).toList();

          Logger.info('Parsed ${options.length} dependent options');
          return options;
        } else {
          Logger.warning('Unexpected response format for dependent options');
          return [];
        }
      } else {
        final errorMessage =
            'Failed to fetch dependent options: ${response.statusCode}';
        Logger.error(errorMessage);

        // Show alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          throw Exception(response.data['message']);
        } else {
          throw Exception(errorMessage);
        }
      }
    } on DioException catch (e) {
      Logger.error('DioException while fetching dependent options: $e');

      // Handle 401 status code - retry with new token
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to get new token and retry...');
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with new token
          return await fetchDependentOptions(
            instanceId: instanceId,
            inputId: inputId,
            parentValue: parentValue,
            tenantId: tenantId,
          );
        }
      }

      // Extract error message from response
      String errorMessage = 'Failed to fetch dependent options';
      if (e.response?.data != null && e.response!.data is Map) {
        final responseData = e.response!.data as Map;
        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        } else if (responseData.containsKey('detail')) {
          errorMessage = responseData['detail'];
        }
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Exception while fetching dependent options: $e');
      throw Exception('Failed to fetch dependent options: $e');
    }
  }

  /// Execute local objective with input data
  /// API Call: POST http://localhost:8000/api/v2/local_objectives/instances/{instance_id}/execute?tenant_id={tenant_id}
  Future<LocalObjectiveExecutionModel> executeLocalObjective({
    required String instanceId,
    required String tenantId,
    required Map<String, dynamic> inputData,
    required String userId,
  }) async {
    try {
      Logger.info('Executing local objective for instance: $instanceId');

      // Get a valid token
      final token = await _authService.getValidToken();
      if (token == null) {
        Logger.error('No valid token available to execute local objective');
        throw Exception('Authentication required');
      }

      // Prepare request body
      final requestBody = {
        "input_data": inputData,
        "user_id": userId,
      };

      // Prepare URL with instance_id and tenant_id query parameter
      final url =
          '${_env.transactionApiBaseUrl}/api/v2/local_objectives/instances/$instanceId/execute?tenant_id=$tenantId';

      Logger.info('Making API call to: $url');
      Logger.info('Request body: $requestBody');

      final response = await dio.post(
        url,
        data: requestBody,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        ),
      );

      Logger.info(
          'Execute local objective response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        Logger.info('Local objective executed successfully');
        return LocalObjectiveExecutionModel.fromJson(response.data);
      } else {
        final errorMessage =
            'Failed to execute local objective: ${response.statusCode}';
        Logger.error(errorMessage);

        // Show alert with response message for non-200 status codes
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('details') &&
            response.data['details'] is Map &&
            response.data['details'].containsKey('error_details')) {
          throw Exception(response.data['details']['error_details']);
        } else if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          throw Exception(response.data['message']);
        } else {
          throw Exception(errorMessage);
        }
      }
    } on DioException catch (e) {
      Logger.error('DioException while executing local objective: $e');

      // Handle 401 status code - retry with new token
      if (e.response?.statusCode == 401) {
        Logger.info('Received 401, attempting to get new token and retry...');
        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          // Retry the request with new token
          return await executeLocalObjective(
            instanceId: instanceId,
            tenantId: tenantId,
            inputData: inputData,
            userId: userId,
          );
        }
      }

      // Extract error message from response
      String errorMessage = 'Failed to execute local objective';
      if (e.response?.data != null && e.response!.data is Map) {
        final responseData = e.response!.data as Map;
        if (responseData.containsKey('detail')) {
          if (responseData['detail'] is Map &&
              responseData['detail'].containsKey('details')) {
            if (responseData['detail']['details'] is Map &&
                responseData['detail']['details']
                    .containsKey('error_details')) {
              errorMessage = responseData['detail']['details']['error_details'];
            }
          }
        } else if (responseData.containsKey('message')) {
          errorMessage = responseData['message'];
        }
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Exception while executing local objective: $e');
      throw Exception('Failed to execute local objective: $e');
    }
  }
}
