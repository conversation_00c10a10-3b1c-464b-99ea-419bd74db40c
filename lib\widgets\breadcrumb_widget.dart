import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/manual_creation_provider.dart';

class BreadcrumbWidget extends StatelessWidget {
  final WorkflowStep currentStep;
  final VoidCallback onHomeTap;

  const BreadcrumbWidget({
    super.key,
    required this.currentStep,
    required this.onHomeTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/images/arrow-left.svg',
          height: 12,
          width: 12,
          colorFilter: ColorFilter.mode(Colors.black, BlendMode.srcIn),
        ),
        SizedBox(width: 8),
        _buildBreadcrumbText(),
      ],
    );
  }

  Widget _buildBreadcrumbText() {
    List<String> breadcrumbParts = _getBreadcrumbParts();
    
    return Wrap(
      children: [
        for (int i = 0; i < breadcrumbParts.length; i++) ...[
          if (i == 0)
            // Home link (clickable)
            GestureDetector(
              onTap: onHomeTap,
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Text(
                  breadcrumbParts[i],
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xff5D5D5D),
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                  ),
                ),
              ),
            )
          else
            // Other parts (non-clickable for now)
            Text(
              breadcrumbParts[i],
              style: TextStyle(
                fontSize: 12,
                color: Color(0xff5D5D5D),
                fontWeight: FontWeight.w400,
                fontFamily: 'TiemposText',
              ),
            ),
          
          // Add separator if not the last item
          if (i < breadcrumbParts.length - 1)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                '/',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xff5D5D5D),
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
        ],
      ],
    );
  }

  List<String> _getBreadcrumbParts() {
    List<String> parts = ['Home'];
    
    // Add current step specific part directly after Home
    switch (currentStep) {
      case WorkflowStep.agentCreation:
        parts.add('Agents');
        break;
      case WorkflowStep.entityCreation:
        parts.add('Data Sets');
        break;
      case WorkflowStep.workflowCreation:
        parts.add('Work Flows');
        break;
      case WorkflowStep.workflowLoCreation:
        parts.add('Work Flow LO');
        break;
    }
    
    return parts;
  }
}
