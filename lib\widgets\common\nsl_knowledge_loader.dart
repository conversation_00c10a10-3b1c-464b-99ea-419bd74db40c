import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:overlay_loader_with_app_icon/overlay_loader_with_app_icon.dart';

/// NSL Knowledge loader component that shows a loading indicator with "NSL Knowledge..." text
/// This matches the design used in chat screens for API loading states
// class NSLKnowledgeLoader extends StatelessWidget {
//   /// The text to display next to the loading indicator
//   final String text;

//   /// The size of the loading indicator
//   final double indicatorSize;

//   /// The font size of the text
//   final double fontSize;

//   /// The color of the loading indicator and text
//   final Color? color;

//   /// Whether to show the loading indicator
//   final bool showIndicator;

//   /// Custom widget to show instead of the default circular progress indicator
//   final Widget? customIndicator;

//   const NSLKnowledgeLoader({
//     super.key,
//     this.text = 'NSL Knowledge...',
//     this.indicatorSize = 20.0,
//     this.fontSize = 16.0,
//     this.color,
//     this.showIndicator = true,
//     this.customIndicator,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final themeColor = color ?? Theme.of(context).colorScheme.primary;

//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         if (showIndicator) ...[
//           customIndicator ??
//               SizedBox(
//                 width: indicatorSize,
//                 height: indicatorSize,
//                 child: CircularProgressIndicator(
//                   strokeWidth: 2,
//                   valueColor: AlwaysStoppedAnimation<Color>(themeColor),
//                 ),
//               ),
//           SizedBox(width: AppSpacing.sm),
//         ],
//         Text(
//           text,
//           style: TextStyle(
//             fontFamily: 'TiemposText',
//             fontSize: fontSize,
//             color: themeColor,
//           ),
//         ),
//       ],
//     );
//   }
// }

/// A full-screen NSL Knowledge loader overlay (standalone version for backward compatibility)
// class NSLKnowledgeLoaderOverlay extends StatelessWidget {
//   /// The text to display below the loading indicator
//   final String text;

//   /// The size of the loading indicator
//   final double indicatorSize;

//   /// The font size of the text
//   final double fontSize;

//   /// The color of the loading indicator and text
//   final Color? color;

//   /// The opacity of the background overlay (0.0 to 1.0)
//   final double backgroundOpacity;

//   const NSLKnowledgeLoaderOverlay({
//     super.key,
//     this.text = 'NSL Knowledge...',
//     this.indicatorSize = 50.0,
//     this.fontSize = 18.0,
//     this.color,
//     this.backgroundOpacity = 0.7,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final themeColor = color ?? Theme.of(context).colorScheme.primary;

//     return Positioned.fill(
//       child: Container(
//         color: Colors.black.withOpacity(backgroundOpacity),
//         child: Center(
//           child: Container(
//             padding: const EdgeInsets.all(40),
//             decoration: BoxDecoration(
//               color: Colors.white,
//               borderRadius: BorderRadius.circular(16),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.black.withAlpha(70),
//                   blurRadius: 20,
//                   spreadRadius: 5,
//                 ),
//               ],
//             ),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 SizedBox(
//                   height: indicatorSize,
//                   width: indicatorSize,
//                   child: CircularProgressIndicator(
//                     strokeWidth: 4,
//                     valueColor: AlwaysStoppedAnimation<Color>(themeColor),
//                   ),
//                 ),
//                 const SizedBox(height: 30),
//                 Text(
//                   text,
//                   style: TextStyle(
//                     fontFamily: 'TiemposText',
//                     fontWeight: FontWeight.bold,
//                     fontSize: fontSize,
//                     color: themeColor,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

/// A wrapper widget that uses OverlayLoaderWithAppIcon for full-screen loading
///
/// Usage example:
/// ```dart
/// NSLKnowledgeLoaderWrapper(
///   isLoading: _isLoading,
///   child: Scaffold(
///     appBar: AppBar(title: Text("My Screen")),
///     body: MyContent(),
///   ),
/// )
/// ```
class NSLKnowledgeLoaderWrapper extends StatelessWidget {
  /// Whether to show the loading overlay
  final bool isLoading;

  /// The child widget to wrap with the overlay
  final Widget child;

  /// The text to display below the loading indicator
  final String text;

  /// The color of the loading indicator
  final Color? circularProgressColor;

  /// The background color of the overlay
  final Color? overlayBackgroundColor;

  /// The app icon to display with the loader
  final Widget? appIcon;

  const NSLKnowledgeLoaderWrapper({
    super.key,
    required this.isLoading,
    required this.child,
    this.text = 'NSL Knowledge...',
    this.circularProgressColor,
    this.overlayBackgroundColor,
    this.appIcon,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = circularProgressColor ?? const Color(0xff0058FF);
    final bgColor = overlayBackgroundColor ?? Colors.black;
    final defaultAppIcon = appIcon ??
        SvgPicture.asset(
          'assets/images/login_logo.svg',
          width: 50,
          height: 50,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.business,
              size: 50,
              color: themeColor,
            );
          },
        );

    return OverlayLoaderWithAppIcon(
      isLoading: isLoading,
      overlayBackgroundColor: bgColor,
      circularProgressColor: themeColor,
      appIcon: defaultAppIcon,
      child: child,
    );
  }
}

// /// A compact NSL Knowledge loader for inline use
// class NSLKnowledgeLoaderCompact extends StatelessWidget {
//   /// The text to display next to the loading indicator
//   final String text;

//   /// The color of the loading indicator and text
//   final Color? color;

//   const NSLKnowledgeLoaderCompact({
//     super.key,
//     this.text = 'Loading...',
//     this.color,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final themeColor = color ?? Theme.of(context).colorScheme.primary;

//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         SizedBox(
//           width: 16,
//           height: 16,
//           child: CircularProgressIndicator(
//             strokeWidth: 2,
//             valueColor: AlwaysStoppedAnimation<Color>(themeColor),
//           ),
//         ),
//         const SizedBox(width: 8),
//         Text(
//           text,
//           style: TextStyle(
//             fontSize: 14,
//             color: themeColor,
//           ),
//         ),
//       ],
//     );
//   }
// }
