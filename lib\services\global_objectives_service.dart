import 'package:dio/dio.dart';
import '../models/books/all_global_objective_model.dart';
import 'base_api_service.dart';
import '../utils/logger.dart';

/// Service for handling global objectives API operations
class GlobalObjectivesService extends BaseApiService {
  static const String _baseUrl = 'http://10.26.1.52:8000';
  static const String _objectivesEndpoint = '/api/v2/global_objectives/';

  /// Fetch all global objectives for a specific tenant
  Future<AllGlobalObjectiveModel> getAllGlobalObjectives({
    required String tenantId,
    required String authToken,
  }) async {
    try {
      Logger.info('Fetching global objectives for tenant: $tenantId');

      final queryParameters = {
        'tenant_id': tenantId,
      };

      final options = Options(
        headers: {
          'Authorization': 'Bearer $authToken',
        },
      );

      // Override the base URL for this specific request
      final fullUrl = '$_baseUrl$_objectivesEndpoint';

      final response = await dio.get(
        fullUrl,
        queryParameters: queryParameters,
        options: options,
      );

      Logger.info('Global objectives fetched successfully: ${response.statusCode}');

      if (response.data is Map<String, dynamic>) {
        return AllGlobalObjectiveModel.fromJson(response.data);
      } else {
        Logger.error('Unexpected response format: ${response.data}');
        throw Exception('Invalid response format');
      }
    } catch (e) {
      Logger.error('Error fetching global objectives: $e');
      rethrow;
    }
  }

  /// Fetch all global objectives with default tenant and token
  /// This method can be used when tenant and token are stored in preferences
  Future<AllGlobalObjectiveModel> getAllGlobalObjectivesWithDefaults() async {
    try {
      // You can implement logic here to get tenant_id and token from SharedPreferences
      // For now, using the provided values from the curl command
      const defaultTenantId = 't001';
      const defaultToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMzAiLCJ1c2VybmFtZSI6IktpcmFuMTIzIiwicm9sZXMiOlsiVXNlciJdLCJ0ZW5hbnRfaWQiOiJ0MDAxIiwiZXhwIjoxNzQ4MzM5MDcwfQ.hw8WuZpWOkk-JpREJR0hi75ENhHABfDRsuIsGtpvEck';

      return await getAllGlobalObjectives(
        tenantId: defaultTenantId,
        authToken: defaultToken,
      );
    } catch (e) {
      Logger.error('Error fetching global objectives with defaults: $e');
      rethrow;
    }
  }
}
