// To parse this JSON data, do
//
//     final registerModel = registerModelFromJson(jsonString);

import 'dart:convert';

RegisterModel registerModelFromJson(String str) => RegisterModel.fromJson(json.decode(str));

String registerModelToJson(RegisterModel data) => json.encode(data.toJson());

class RegisterModel {
    String? userId;
    String? username;
    String? email;
    String? firstName;
    String? lastName;
    String? status;
    List<String>? roles;
    String? tenantId;
    bool? disabled;
    DateTime? createdAt;

    RegisterModel({
        this.userId,
        this.username,
        this.email,
        this.firstName,
        this.lastName,
        this.status,
        this.roles,
        this.tenantId,
        this.disabled,
        this.createdAt,
    });

    RegisterModel copyWith({
        String? userId,
        String? username,
        String? email,
        String? firstName,
        String? lastName,
        String? status,
        List<String>? roles,
        String? tenantId,
        bool? disabled,
        DateTime? createdAt,
    }) => 
        RegisterModel(
            userId: userId ?? this.userId,
            username: username ?? this.username,
            email: email ?? this.email,
            firstName: firstName ?? this.firstName,
            lastName: lastName ?? this.lastName,
            status: status ?? this.status,
            roles: roles ?? this.roles,
            tenantId: tenantId ?? this.tenantId,
            disabled: disabled ?? this.disabled,
            createdAt: createdAt ?? this.createdAt,
        );

    factory RegisterModel.fromJson(Map<String, dynamic> json) => RegisterModel(
        userId: json["user_id"],
        username: json["username"],
        email: json["email"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        status: json["status"],
        roles: json["roles"] == null ? [] : List<String>.from(json["roles"]!.map((x) => x)),
        tenantId: json["tenant_id"],
        disabled: json["disabled"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    );

    Map<String, dynamic> toJson() => {
        "user_id": userId,
        "username": username,
        "email": email,
        "first_name": firstName,
        "last_name": lastName,
        "status": status,
        "roles": roles == null ? [] : List<dynamic>.from(roles!.map((x) => x)),
        "tenant_id": tenantId,
        "disabled": disabled,
        "created_at": createdAt?.toIso8601String(),
    };
}
