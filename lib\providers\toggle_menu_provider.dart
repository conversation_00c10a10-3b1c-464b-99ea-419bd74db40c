import 'package:flutter/material.dart';

class ToggleMenuProvider extends ChangeNotifier {
  bool _isHovered = false;
  bool _isMenuOpen = false;

  bool get isHovered => _isHovered;
  bool get isMenuOpen => _isMenuOpen;

  void setHovered(bool value) {
    _isHovered = value;
    notifyListeners();
  }

  void setMenuOpen(bool value) {
    _isMenuOpen = value;
    notifyListeners();
  }

  void handleMenuSelection(String value) {
    // Handle menu selection logic here
    print('Menu selection: $value');
  }
}
