import 'package:flutter/material.dart';

class JavaKeywordService {
  // Singleton instance
  static final JavaKeywordService _instance = JavaKeywordService._internal();
  
  factory JavaKeywordService() {
    return _instance;
  }
  
  JavaKeywordService._internal();
  
  // Map of all Java keywords and their descriptions
  final Map<String, String> allKeywords = {
    // Access modifiers
    'public': 'Access modifier that makes a class, method, or field accessible from any other class.',
    'private': 'Access modifier that restricts access to the class only.',
    'protected': 'Access modifier that allows access within the same package and by subclasses.',
    'default': 'Default access modifier (package-private) when no modifier is specified.',
    
    // Class-related keywords
    'class': 'Declares a class.',
    'interface': 'Declares an interface.',
    'enum': 'Declares an enumeration type.',
    'extends': 'Indicates that a class is derived from another class or interface.',
    'implements': 'Indicates that a class implements an interface.',
    'abstract': 'Declares a class that cannot be instantiated or a method that must be implemented by a subclass.',
    'final': 'Indicates that a variable, method, or class cannot be changed or overridden.',
    'static': 'Indicates that a variable or method belongs to the class rather than instances.',
    'this': 'Refers to the current instance of the class.',
    'super': 'Refers to the parent class.',
    'new': 'Creates a new instance of a class.',
    
    // Primitive data types
    'boolean': 'Primitive data type that can be either true or false.',
    'byte': 'Primitive data type that stores 8-bit integer values.',
    'char': 'Primitive data type that stores a single character.',
    'short': 'Primitive data type that stores 16-bit integer values.',
    'int': 'Primitive data type that stores 32-bit integer values.',
    'long': 'Primitive data type that stores 64-bit integer values.',
    'float': 'Primitive data type that stores 32-bit floating-point values.',
    'double': 'Primitive data type that stores 64-bit floating-point values.',
    'void': 'Indicates that a method does not return a value.',
    
    // Common reference types
    'String': 'Class that represents a sequence of characters.',
    'Integer': 'Wrapper class for the primitive type int.',
    'Boolean': 'Wrapper class for the primitive type boolean.',
    'Double': 'Wrapper class for the primitive type double.',
    'Float': 'Wrapper class for the primitive type float.',
    'Long': 'Wrapper class for the primitive type long.',
    'Short': 'Wrapper class for the primitive type short.',
    'Byte': 'Wrapper class for the primitive type byte.',
    'Character': 'Wrapper class for the primitive type char.',
    
    // Collection types
    'List': 'Interface that represents an ordered collection of elements.',
    'ArrayList': 'Implementation of the List interface that uses a dynamic array.',
    'LinkedList': 'Implementation of the List interface that uses a doubly-linked list.',
    'Map': 'Interface that maps keys to values.',
    'HashMap': 'Implementation of the Map interface that uses a hash table.',
    'Set': 'Interface that represents a collection of unique elements.',
    'HashSet': 'Implementation of the Set interface that uses a hash table.',
    
    // Control flow
    'if': 'Conditional statement that executes code if a condition is true.',
    'else': 'Executes code if the condition in an if statement is false.',
    'switch': 'Selects one of many code blocks to be executed.',
    'case': 'A block of code in a switch statement.',
    'default': 'The default block of code in a switch statement.',
    'for': 'Loop that repeats a block of code a specified number of times.',
    'while': 'Loop that repeats a block of code while a condition is true.',
    'do': 'Loop that executes a block of code once, then repeats while a condition is true.',
    'break': 'Terminates a loop or switch statement.',
    'continue': 'Skips the current iteration of a loop.',
    'return': 'Exits a method and optionally returns a value.',
    
    // Exception handling
    'try': 'Defines a block of code to be tested for errors.',
    'catch': 'Catches exceptions that occur in the try block.',
    'finally': 'Defines a block of code that always executes after the try/catch blocks.',
    'throw': 'Throws an exception.',
    'throws': 'Declares exceptions that a method might throw.',
    
    // Other keywords
    'import': 'Imports a package, class, or interface.',
    'package': 'Declares a package for the current file.',
    'instanceof': 'Tests if an object is an instance of a specific class or interface.',
    'assert': 'Tests a condition and throws an error if false.',
    'synchronized': 'Controls access to code by multiple threads.',
    'volatile': 'Indicates that a variable may change asynchronously.',
    'transient': 'Indicates that a field should not be serialized.',
    'native': 'Indicates that a method is implemented in native code.',
    'strictfp': 'Ensures that floating-point calculations are the same on all platforms.',
    
    // Date and time related
    'LocalDate': 'Class that represents a date without a time component.',
    'LocalTime': 'Class that represents a time without a date component.',
    'LocalDateTime': 'Class that represents a date and time.',
    'ZonedDateTime': 'Class that represents a date and time with a time zone.',
    'Instant': 'Class that represents an instant in time.',
    'Duration': 'Class that represents a duration of time.',
    'Period': 'Class that represents a period of time.',
    'DateTimeFormatter': 'Class that formats and parses date-time objects.',
    
    // Common methods
    'equals': 'Method that compares two objects for equality.',
    'hashCode': 'Method that returns a hash code value for an object.',
    'toString': 'Method that returns a string representation of an object.',
    'compareTo': 'Method that compares this object with another object.',
    'clone': 'Method that creates a copy of an object.',
    'finalize': 'Method that is called by the garbage collector before an object is reclaimed.',
    'getClass': 'Method that returns the runtime class of an object.',
    'wait': 'Method that causes the current thread to wait until another thread invokes notify() or notifyAll().',
    'notify': 'Method that wakes up a single thread that is waiting on this object\'s monitor.',
    'notifyAll': 'Method that wakes up all threads that are waiting on this object\'s monitor.',
    
    // Annotations
    '@Override': 'Indicates that a method overrides a method in a superclass.',
    '@Deprecated': 'Indicates that a method is deprecated and should no longer be used.',
    '@SuppressWarnings': 'Suppresses compiler warnings.',
    '@FunctionalInterface': 'Indicates that an interface is a functional interface.',
    
    // Database-related
    'DATE': 'SQL data type that represents a date.',
    'TIMESTAMP': 'SQL data type that represents a date and time.',
    'VARCHAR': 'SQL data type that represents a variable-length string.',
    'INTEGER': 'SQL data type that represents an integer.',
    'BOOLEAN': 'SQL data type that represents a boolean value.',
    'DECIMAL': 'SQL data type that represents a decimal number.',
    'FLOAT': 'SQL data type that represents a floating-point number.',
    'DOUBLE': 'SQL data type that represents a double-precision floating-point number.',
    'CHAR': 'SQL data type that represents a fixed-length string.',
    'TEXT': 'SQL data type that represents a large text value.',
    'BLOB': 'SQL data type that represents a binary large object.',
    'CLOB': 'SQL data type that represents a character large object.',
  };
  
  // Extract Java keywords from a line of Java code
  List<String> extractKeywords(String javaLine) {
    final List<String> foundKeywords = [];
    
    // Check if each keyword is in the line
    for (final keyword in allKeywords.keys) {
      // Use regex to match whole words only
      final RegExp regex = RegExp('\\b$keyword\\b');
      if (regex.hasMatch(javaLine)) {
        foundKeywords.add(keyword);
      }
    }
    
    return foundKeywords;
  }
  
  // Get keywords used in a list of Java lines
  Map<String, String> getKeywordsFromLines(List<String> javaLines) {
    final Map<String, String> usedKeywords = {};
    
    for (final line in javaLines) {
      final keywords = extractKeywords(line);
      for (final keyword in keywords) {
        usedKeywords[keyword] = allKeywords[keyword] ?? '';
      }
    }
    
    return usedKeywords;
  }
  
  // Get a color for a keyword based on its type
  Color getColorForKeyword(String keyword) {
    if (['public', 'private', 'protected'].contains(keyword)) {
      return Colors.blue;
    } else if (['class', 'interface', 'enum', 'extends', 'implements'].contains(keyword)) {
      return Colors.purple;
    } else if (['int', 'boolean', 'byte', 'char', 'short', 'long', 'float', 'double', 'void'].contains(keyword)) {
      return Colors.orange;
    } else if (['String', 'Integer', 'Boolean', 'Double', 'Float', 'Long', 'Short', 'Byte', 'Character'].contains(keyword)) {
      return Colors.green;
    } else if (['if', 'else', 'switch', 'case', 'for', 'while', 'do', 'break', 'continue', 'return'].contains(keyword)) {
      return Colors.red;
    } else if (['try', 'catch', 'finally', 'throw', 'throws'].contains(keyword)) {
      return Colors.deepOrange;
    } else if (['LocalDate', 'LocalTime', 'LocalDateTime', 'ZonedDateTime', 'Instant', 'Duration', 'Period'].contains(keyword)) {
      return Colors.teal;
    } else if (['DATE', 'TIMESTAMP', 'VARCHAR', 'INTEGER', 'BOOLEAN', 'DECIMAL', 'FLOAT', 'DOUBLE', 'CHAR', 'TEXT', 'BLOB', 'CLOB'].contains(keyword)) {
      return Colors.brown;
    } else {
      return Colors.grey;
    }
  }
}
