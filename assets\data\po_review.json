{"purchase_order_review_workflow": {"version": "1.0", "description": "Purchase Order Review & Approval - Step 2: Review Details & Submit", "workflow_step": 2, "implementation_date": "2025-01-15", "user_context": {"current_user": "<PERSON>", "role": "Procurement Manager", "session_id": "SES-********-143520", "request_timestamp": "2025-01-15T14:35:20Z", "department": "Procurement", "po_number": "PO-2024-0145", "workflow_state": "pending_review"}, "spatial_organization": {"layout_structure": {"chat_area": {"width_percentage": "60-70%", "primary_purpose": "PO summary and approval actions", "information_levels": ["Level 0 (hidden)", "Level 1 (primary)", "Level 2 (critical)"]}, "side_panel": {"width_percentage": "30-40%", "primary_purpose": "Approval workflow and compliance checks", "information_levels": ["Level 2 (extended)", "Level 3 (reference)", "Level 4 (analytics)", "Level 5 (system)"]}}}, "level_0_intent_context": {"hierarchy_position": "Foundation layer - invisible to user", "processing_location": "Background system", "cognitive_load": "Zero", "processing_time": "<100ms", "intent_recognition": {"user_input": "Review and submit purchase order for approval", "system_interpretation": {"parsed_intent": "PO_REVIEW_AND_APPROVAL", "confidence_score": 0.96, "entity_extraction": {"action": "review and submit", "object": "purchase order", "stage": "final approval", "approval_path": "manager_approval_required"}}, "context_preparation": {"po_status": "Complete and ready for review", "approval_chain": "<PERSON> (Manager) → Finance Review", "compliance_status": "All checks passed", "system_decisions": ["Load PO summary", "Check approval requirements", "Validate compliance"]}}}, "level_1_primary_actions": {"hierarchy_position": "Essential interaction - always visible", "display_location": "Chat area review interface", "cognitive_load": "High - critical decision making", "visibility": "Prominent above fold", "textual_hierarchy": {"primary_heading": {"text": "✅ PURCHASE ORDER REVIEW", "typography": "18px, font-weight: 600", "purpose": "Review stage identification", "hierarchy_level": "H1"}, "po_summary_card": {"po_number": {"text": "PO-2024-0145", "typography": "16px, font-weight: 600, color: #4A90E2", "hierarchy_level": "H2"}, "summary_sections": [{"section": "Vendor Information", "typography": "14px, font-weight: 500", "hierarchy_level": "H3"}, {"section": "Order Details", "typography": "14px, font-weight: 500", "hierarchy_level": "H3"}, {"section": "Financial Summary", "typography": "14px, font-weight: 500", "hierarchy_level": "H3"}]}, "action_buttons": {"hierarchy_level": "Primary CTA", "primary_action": {"text": "Submit for Approval", "typography": "14px, font-weight: 500", "visual_hierarchy": "Highest prominence, green background"}, "secondary_actions": {"typography": "14px, normal weight", "visual_hierarchy": "Lower prominence, secondary styling", "actions": ["Edit Details", "Save as Draft", "Cancel"]}}}, "essential_elements": {"po_summary_display": {"field_id": "po_summary_section", "data_type": "composite_object", "widget_type": "ComprehensivePOSummary", "widget_config": {"readonly_mode": true, "highlight_changes": false, "expandable_sections": true, "print_friendly": true}, "ui_control_properties": {"control_type": "summary_display_grid", "layout": "two_column_grid", "section_borders": true, "color_coding": true}, "data_structure": {"basic_info": {"vendor_name": {"data_type": "string", "widget_type": "ReadOnlyText", "ui_properties": {"font_weight": "bold", "link_to_vendor": true}}, "delivery_date": {"data_type": "date", "widget_type": "FormattedDate", "ui_properties": {"format": "MMM dd, yyyy", "highlight_urgency": true}}, "payment_terms": {"data_type": "string", "widget_type": "BadgeDisplay", "ui_properties": {"color": "info"}}, "priority_level": {"data_type": "enum", "widget_type": "PriorityIndicator", "ui_properties": {"values": ["Low", "Standard", "High", "<PERSON><PERSON>"], "color_coding": true}}}}, "summary_values": {"vendor": "TechSupply Corp", "total_amount": "$12,500", "line_items_count": 8, "delivery_date": "Feb 1, 2024"}}, "compliance_validation": {"field_id": "compliance_check_results", "data_type": "validation_results_array", "widget_type": "ComplianceValidationDisplay", "widget_config": {"real_time_checks": true, "detailed_explanations": true, "override_capability": false, "audit_trail": true}, "ui_control_properties": {"control_type": "validation_status_list", "layout": "checklist_with_details", "animation": "check_marks", "expandable_details": true}, "validation_checks": [{"check_id": "budget_approval", "check_name": "Budget Approval", "data_type": "validation_result", "widget_type": "ValidationStatusItem", "status": "passed", "ui_properties": {"icon": "✅", "color": "#4CAF50", "detail_text": "Approved (21% of Q1 remaining budget)"}}, {"check_id": "vendor_verification", "check_name": "Vendor Verification", "data_type": "validation_result", "widget_type": "ValidationStatusItem", "status": "passed", "ui_properties": {"icon": "✅", "color": "#4CAF50", "detail_text": "TechSupply Corp verified and active"}}, {"check_id": "policy_compliance", "check_name": "Policy Compliance", "data_type": "validation_result", "widget_type": "ValidationStatusItem", "status": "passed", "ui_properties": {"icon": "✅", "color": "#4CAF50", "detail_text": "All procurement policies satisfied"}}]}, "approval_confirmation": {"field_id": "approval_details", "data_type": "approval_object", "widget_type": "ApprovalConfirmationBox", "widget_config": {"signature_required": false, "comments_optional": true, "delegation_allowed": true, "bulk_approval": false}, "ui_control_properties": {"control_type": "approval_form_section", "layout": "vertical_stack", "validation_on_submit": true, "confirmation_dialog": true}, "form_fields": {"approval_justification": {"data_type": "text", "widget_type": "TextArea", "ui_properties": {"placeholder": "Add justification for this approval...", "max_length": 500, "required": false}}, "cost_center_confirmation": {"data_type": "reference_code", "widget_type": "ReadOnlyDropdown", "ui_properties": {"pre_selected": true, "change_allowed": false}}, "notification_preferences": {"data_type": "boolean_array", "widget_type": "CheckboxGroup", "ui_properties": {"options": ["Email when approved", "Email when delivered", "SMS for urgent updates"]}}}, "required_fields": ["approval_justification", "cost_center"], "compliance_checks": "All passed ✓"}, "final_review_checklist": {"field_id": "review_checklist", "data_type": "checklist_array", "widget_type": "InteractiveReviewChecklist", "widget_config": {"all_required": true, "progressive_enable": false, "save_state": true, "validation_on_submit": true}, "ui_control_properties": {"control_type": "checkbox_list_with_validation", "layout": "vertical_list", "check_animation": true, "completion_indicator": true}, "checklist_items": [{"item_id": "vendor_verified", "label": "Vendor information verified", "data_type": "boolean", "widget_type": "ValidatedCheckbox", "required": true, "ui_properties": {"default_checked": true, "tooltip": "Confirm vendor details are accurate"}}, {"item_id": "items_correct", "label": "Line items and quantities correct", "data_type": "boolean", "widget_type": "ValidatedCheckbox", "required": true, "ui_properties": {"default_checked": true, "tooltip": "Verify all items and quantities"}}, {"item_id": "budget_confirmed", "label": "Budget approval confirmed", "data_type": "boolean", "widget_type": "ValidatedCheckbox", "required": true, "ui_properties": {"default_checked": true, "tooltip": "Budget allocation is approved"}}, {"item_id": "delivery_acceptable", "label": "Delivery terms acceptable", "data_type": "boolean", "widget_type": "ValidatedCheckbox", "required": true, "ui_properties": {"default_checked": true, "tooltip": "Delivery date meets requirements"}}]}, "financial_summary": {"field_id": "financial_totals", "data_type": "financial_summary_object", "widget_type": "FinancialSummaryDisplay", "widget_config": {"currency_format": "USD", "tax_breakdown": true, "highlight_total": true, "comparison_mode": false}, "ui_control_properties": {"control_type": "financial_breakdown_table", "layout": "itemized_with_totals", "color_coding": true, "print_formatting": true}, "line_items_summary": {"data_type": "array_of_financial_items", "widget_type": "LineItemsSummaryTable", "ui_properties": {"show_quantities": true, "show_unit_prices": true, "highlight_totals": true, "readonly": true}}, "totals_calculation": {"subtotal": {"data_type": "currency", "widget_type": "CurrencyDisplay", "calculation": "sum_of_line_totals"}, "tax_amount": {"data_type": "currency", "widget_type": "CalculatedCurrency", "calculation": "subtotal * tax_rate"}, "shipping_cost": {"data_type": "currency", "widget_type": "CurrencyDisplay"}, "grand_total": {"data_type": "currency", "widget_type": "HighlightedTotal", "calculation": "subtotal + tax_amount + shipping_cost", "ui_properties": {"font_size": "large", "font_weight": "bold", "color": "#4CAF50"}}}}}}, "level_2_contextual_information": {"hierarchy_position": "Approval influencing - dual placement", "cognitive_load": "Medium - decision support", "placement_strategy": "Critical validations in chat, workflow in panel", "chat_area_critical": {"compliance_validation": {"textual_hierarchy": {"validation_header": {"text": "🔍 Compliance Validation", "typography": "16px, font-weight: 600", "hierarchy_level": "H2"}, "validation_items": [{"check": "Budget Approval", "status": "✅ Approved", "color": "#4CAF50"}, {"check": "Vendor Verification", "status": "✅ Verified", "color": "#4CAF50"}, {"check": "Policy Compliance", "status": "✅ Compliant", "color": "#4CAF50"}]}}, "financial_impact": {"textual_hierarchy": {"impact_header": {"text": "💰 Financial Impact", "typography": "16px, font-weight: 600", "hierarchy_level": "H2"}, "impact_details": {"total_cost": "$12,500", "budget_utilization": "25% of remaining Q1 budget", "approval_level": "Manager approval required", "typography": "14px, normal weight"}}}}, "side_panel_extended": {"approval_workflow_card": {"data_type": "workflow_status_object", "widget_type": "ApprovalWorkflowCard", "widget_config": {"real_time_updates": true, "estimated_timing": true, "notification_integration": true, "escalation_rules": true}, "ui_control_properties": {"control_type": "workflow_progress_display", "layout": "vertical_stepper", "animation": "progress_indicators", "interactive": false}, "data_structure": {"workflow_metadata": {"workflow_id": "string", "current_step": "integer", "total_steps": "integer", "estimated_completion": "datetime"}, "approval_steps": {"data_type": "array_of_approval_steps", "step_schema": {"step_number": {"data_type": "integer", "widget_type": "StepIndicator"}, "approver_info": {"data_type": "user_object", "widget_type": "UserProfileMini", "ui_properties": {"show_avatar": true, "show_title": true, "show_status": true}}, "step_status": {"data_type": "enum", "widget_type": "StatusBadge", "values": ["Pending", "In Progress", "Approved", "Rejected", "Waiting"], "ui_properties": {"color_coding": true, "status_icons": true}}, "estimated_time": {"data_type": "duration", "widget_type": "TimeEstimate", "ui_properties": {"format": "human_readable", "show_confidence": true}}}}}, "card_structure": {"card_header": {"primary_title": {"text": "🔄 APPROVAL WORKFLOW", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}, "workflow_status": {"text": "Step 1 of 2", "typography": "12px, color: #666", "hierarchy_level": "Card metadata"}}, "approval_chain": {"steps": [{"step": 1, "approver": "<PERSON>", "role": "Department Manager", "status": "Pending", "estimated_time": "Same day", "color": "#FFB300"}, {"step": 2, "approver": "Finance Team", "role": "Budget Validation", "status": "Waiting", "estimated_time": "1-2 days", "color": "#E0E0E0"}]}}}, "risk_assessment_card": {"data_type": "risk_analysis_object", "widget_type": "RiskAssessmentCard", "widget_config": {"automated_scoring": true, "detailed_explanations": true, "mitigation_suggestions": true, "historical_comparison": false}, "ui_control_properties": {"control_type": "risk_matrix_display", "layout": "factor_list_with_levels", "color_coding": true, "expandable_details": true}, "data_structure": {"risk_factors": {"data_type": "array_of_risk_factors", "risk_factor_schema": {"factor_name": {"data_type": "string", "widget_type": "RiskFactorLabel"}, "risk_level": {"data_type": "enum", "widget_type": "RiskLevelIndicator", "values": ["Low", "Medium", "High", "Critical"], "ui_properties": {"color_mapping": {"Low": "#4CAF50", "Medium": "#FF9800", "High": "#F44336", "Critical": "#B71C1C"}, "badge_style": true}}, "risk_description": {"data_type": "text", "widget_type": "RiskExplanation", "ui_properties": {"max_length": 200, "expandable": true}}, "impact_score": {"data_type": "decimal", "widget_type": "NumericScore", "range": [0, 10]}, "probability_score": {"data_type": "decimal", "widget_type": "NumericScore", "range": [0, 1]}}}, "overall_risk_score": {"data_type": "calculated_risk_score", "widget_type": "OverallRiskIndicator", "calculation": "weighted_average_of_factors", "ui_properties": {"display_format": "gauge", "color_coding": true, "show_trend": false}}}, "card_structure": {"card_header": {"primary_title": {"text": "⚠️ RISK ASSESSMENT", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}}, "risk_factors": [{"factor": "Vendor <PERSON>", "level": "Low", "reason": "Preferred supplier with excellent track record", "color": "#4CAF50"}, {"factor": "Budget Risk", "level": "Medium", "reason": "Uses 25% of remaining Q1 budget", "color": "#FFB300"}, {"factor": "Delivery Risk", "level": "Low", "reason": "Standard delivery timeframe", "color": "#4CAF50"}]}}, "cost_breakdown_card": {"data_type": "financial_breakdown_object", "widget_type": "DetailedCostBreakdownCard", "widget_config": {"currency_formatting": true, "tax_calculations": true, "exchange_rate_support": false, "audit_trail": true}, "ui_control_properties": {"control_type": "financial_summary_table", "layout": "itemized_breakdown", "number_formatting": "currency_with_commas", "highlight_total": true}, "data_structure": {"cost_components": {"data_type": "array_of_cost_items", "cost_item_schema": {"item_label": {"data_type": "string", "widget_type": "CostLabel"}, "amount": {"data_type": "currency", "widget_type": "CurrencyDisplay", "ui_properties": {"format": "$#,##0.00", "alignment": "right"}}, "calculation_method": {"data_type": "string", "widget_type": "CalculationNote", "ui_properties": {"show_tooltip": true, "expandable": false}}}}, "payment_terms": {"data_type": "payment_terms_object", "widget_type": "PaymentTermsDisplay", "fields": {"payment_method": {"data_type": "enum", "values": ["Net 30", "Net 15", "COD", "Prepaid"]}, "currency": {"data_type": "currency_code", "widget_type": "CurrencySelector", "default": "USD"}, "due_date": {"data_type": "calculated_date", "widget_type": "DateDisplay", "calculation": "delivery_date + payment_terms"}}}}, "card_structure": {"card_header": {"primary_title": {"text": "📊 COST BREAKDOWN", "typography": "14px, font-weight: 600", "hierarchy_level": "Card H1"}}, "cost_details": {"subtotal": "$11,500", "shipping": "$750", "tax": "$250", "total": "$12,500", "payment_terms": "Net 30", "currency": "USD"}}}}}, "level_3_related_information": {"hierarchy_position": "Reference material - collapsed by default", "cognitive_load": "Low - optional viewing", "display_location": "Side panel collapsible sections", "expandable_sections": [{"section_id": "approval_policy", "header": "📋 Approval Policy", "default_state": "collapsed", "content_preview": "View approval thresholds and delegation rules..."}, {"section_id": "vendor_contract", "header": "📄 Vendor Contract Terms", "default_state": "collapsed", "content_preview": "Review contract terms and conditions..."}, {"section_id": "purchase_history", "header": "📈 Purchase History", "default_state": "collapsed", "content_preview": "Similar purchases and pricing trends..."}]}, "level_4_historical_analytical": {"hierarchy_position": "Deep insights - tab navigation", "cognitive_load": "Variable - analytical review", "display_location": "Side panel analytics tab", "analytics_tabs": [{"tab_id": "approval_analytics", "label": "Approval Analytics", "content_type": "Historical approval patterns and timing"}, {"tab_id": "cost_analysis", "label": "Cost Analysis", "content_type": "Price comparison and cost optimization"}, {"tab_id": "performance_metrics", "label": "Performance Metrics", "content_type": "Procurement KPIs and efficiency metrics"}]}, "level_5_system_meta": {"hierarchy_position": "Technical details - hidden by default", "cognitive_load": "Minimal - rarely accessed", "access_frequency": "<5% of interactions", "display_location": "Hidden/advanced mode", "system_information": {"po_workflow_id": "PO-WF-2024-001234", "workflow_step": "review_and_approval", "workflow_version": "v2.1", "review_timestamp": "2025-01-15T14:35:20Z", "user_session": "SES-********-143520", "approval_engine": "ApprovalSys v4.1.2", "compliance_checks": {"sox_compliance": "Passed", "budget_validation": "Passed", "vendor_verification": "Passed", "policy_check": "Passed"}, "integration_status": {"erp_system": "Connected", "approval_workflow": "Active", "accounting_system": "Synced"}}}, "workflow_transitions": {"next_steps": {"on_approval_submit": {"target_state": "pending_manager_approval", "notification_recipients": ["<EMAIL>"], "expected_duration": "4-8 hours"}, "on_edit": {"target_state": "po_creation_step1", "preserve_data": true, "validation_required": true}, "on_draft_save": {"target_state": "draft_saved", "auto_reminder": "24 hours"}}}}}