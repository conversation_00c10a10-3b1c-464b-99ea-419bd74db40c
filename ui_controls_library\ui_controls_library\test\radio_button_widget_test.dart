import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_controls_library/widgets/radio_button_widget.dart';

void main() {
  group('RadioButtonWidget Tests', () {
    testWidgets('RadioButtonWidget displays options correctly', (WidgetTester tester) async {
      const options = ['Option 1', 'Option 2', 'Option 3'];
      String? selectedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RadioButtonWidget(
              options: options,
              onChanged: (value) {
                selectedValue = value;
              },
            ),
          ),
        ),
      );

      // Verify all options are displayed
      for (final option in options) {
        expect(find.text(option), findsOneWidget);
      }

      // Verify radio buttons are present
      expect(find.byType(Radio<String>), findsNWidgets(options.length));
    });

    testWidgets('RadioButtonWidget handles selection correctly', (WidgetTester tester) async {
      const options = ['Option 1', 'Option 2', 'Option 3'];
      String? selectedValue;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(
              builder: (context, setState) {
                return RadioButtonWidget(
                  options: options,
                  initialValue: selectedValue,
                  onChanged: (value) {
                    setState(() {
                      selectedValue = value;
                    });
                  },
                );
              },
            ),
          ),
        ),
      );

      // Initially no option should be selected
      expect(selectedValue, isNull);

      // Tap on the second option
      await tester.tap(find.byType(Radio<String>).at(1));
      await tester.pump();

      // Verify the selection changed
      expect(selectedValue, equals('Option 2'));
    });

    testWidgets('RadioButtonWidget with custom colors', (WidgetTester tester) async {
      const options = ['Option 1', 'Option 2'];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RadioButtonWidget(
              options: options,
              hoverBorderColor: Colors.blue,
              selectedBorderColor: Colors.red,
              inactiveColor: Colors.grey,
              onChanged: (value) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(RadioButtonWidget), findsOneWidget);
      expect(find.text('Option 1'), findsOneWidget);
      expect(find.text('Option 2'), findsOneWidget);
    });

    testWidgets('RadioButtonWidget with custom shape', (WidgetTester tester) async {
      const options = ['Square 1', 'Square 2'];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RadioButtonWidget(
              options: options,
              useCustomShape: true,
              radioShape: RadioShape.square,
              hoverBorderColor: Colors.orange,
              selectedBorderColor: Colors.red,
              onChanged: (value) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(RadioButtonWidget), findsOneWidget);
      expect(find.text('Square 1'), findsOneWidget);
      expect(find.text('Square 2'), findsOneWidget);
      
      // Verify custom shape containers are present
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('RadioButtonWidget horizontal layout', (WidgetTester tester) async {
      const options = ['Left', 'Center', 'Right'];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RadioButtonWidget(
              options: options,
              isHorizontal: true,
              hoverBorderColor: Colors.purple,
              selectedBorderColor: Colors.red,
              onChanged: (value) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(RadioButtonWidget), findsOneWidget);
      for (final option in options) {
        expect(find.text(option), findsOneWidget);
      }
    });

    testWidgets('RadioButtonWidget fromJson constructor', (WidgetTester tester) async {
      final jsonConfig = {
        'options': ['JSON Option 1', 'JSON Option 2'],
        'hoverBorderColor': '#0000FF', // Blue
        'selectedBorderColor': '#FF0000', // Red
        'inactiveColor': '#808080', // Grey
      };
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RadioButtonWidget.fromJson(jsonConfig),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.byType(RadioButtonWidget), findsOneWidget);
      expect(find.text('JSON Option 1'), findsOneWidget);
      expect(find.text('JSON Option 2'), findsOneWidget);
    });
  });
}
