import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'dart:async';
import 'dart:io';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';

/// Mobile implementation of WebAudioRecorderWidget
/// This provides audio recording functionality for mobile platforms using the record package
class WebAudioRecorderWidgetImpl extends StatefulWidget {
  /// Callback when recording is complete
  final Function(Uint8List audioData, String fileName)? onRecordingComplete;

  /// Text editing controller to update with transcribed text
  final TextEditingController? chatController;

  /// Callback when recording is cancelled
  final VoidCallback? onCancel;

  /// Callback when loading state changes
  final Function(bool isLoading)? onLoadingChanged;

  const WebAudioRecorderWidgetImpl({
    super.key,
    this.onRecordingComplete,
    this.chatController,
    this.onCancel,
    this.onLoadingChanged,
  });

  @override
  State<WebAudioRecorderWidgetImpl> createState() => _WebAudioRecorderWidgetImplState();
}

class _WebAudioRecorderWidgetImplState extends State<WebAudioRecorderWidgetImpl> {
  bool _isRecording = false;
  final AudioRecorder _recorder = AudioRecorder();
  String? _recordingPath;
  int _seconds = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // Start recording immediately
    _startRecording();
  }

  Future<void> _startRecording() async {
    try {
      // Check and request microphone permission
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        Logger.error('Microphone permission not granted');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Microphone permission is required for recording'),
              duration: Duration(seconds: 3),
            ),
          );
          if (widget.onCancel != null) {
            widget.onCancel!();
          }
        }
        return;
      }

      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      final fileName = 'recording_${DateTime.now().millisecondsSinceEpoch}.wav';
      _recordingPath = '${tempDir.path}/$fileName';

      // Check if recorder has permission
      if (await _recorder.hasPermission()) {
        // Start recording
        await _recorder.start(
          const RecordConfig(
            encoder: AudioEncoder.wav,
            sampleRate: 16000,
            bitRate: 128000,
          ),
          path: _recordingPath!,
        );

        setState(() {
          _isRecording = true;
          _seconds = 0;
        });

        // Start timer to update duration
        _startTimer();

        Logger.info('Mobile recording started: $_recordingPath');
      } else {
        throw Exception('Microphone permission not available');
      }
    } catch (e) {
      Logger.error('Error starting mobile recording: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting recording: $e')),
        );
        if (widget.onCancel != null) {
          widget.onCancel!();
        }
      }
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isRecording) {
        setState(() {
          _seconds++;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _stopRecording() async {
    if (_isRecording) {
      try {
        final path = await _recorder.stop();
        setState(() {
          _isRecording = false;
        });

        if (_timer != null) {
          _timer!.cancel();
        }

        _recordingPath = path;
        Logger.info('Mobile recording stopped: $path');
      } catch (e) {
        Logger.error('Error stopping mobile recording: $e');
      }
    }
  }

  /// Call the transcription API using the multimedia service
  Future<void> _callTranscriptionApi(Uint8List audioData, String fileName) async {
    try {
      Logger.info('Starting transcription API call for mobile file: $fileName');

      // Get the multimedia service instance
      final multimediaService = MultimediaService();

      // Determine language hint based on current locale
      String languageHint = 'en'; // Default to English
      try {
        final locale = Localizations.localeOf(context);
        languageHint = locale.languageCode;
      } catch (e) {
        Logger.info('Could not determine locale, using English as default');
      }

      // Call the transcription service through multimedia service
      final transcriptionResult = await multimediaService.transcribeAudio(
        audioData: audioData,
        fileName: fileName,
        languageHint: languageHint,
        enableCommandProcessing: true,
        enableTranslation: false,
      );

      Logger.info('Mobile transcription API response: ${transcriptionResult['success']}');

      // Parse the response
      if (transcriptionResult['success'] == true) {
        final transcript = transcriptionResult['transcription'];

        // Update the chat controller with the transcribed text
        if (widget.chatController != null &&
            transcript != null &&
            transcript.isNotEmpty) {
          // Ensure we're on the main thread for UI updates
          if (mounted) {
            setState(() {
              // Set recording state to false
              _isRecording = false;
            });

            // Update the text controller
            widget.chatController!.text = transcript;
            Logger.info('Updated mobile chat controller with transcribed text: $transcript');

            // Notify parent that recording is complete
            if (widget.onCancel != null) {
              widget.onCancel!();
            }
          }
        } else {
          Logger.error('Mobile transcription returned empty or null text');

          // Show error message to user
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Transcription returned empty text. Please try again.'),
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        final errorMessage = transcriptionResult['message'] ?? 'Unknown transcription error';
        Logger.error('Mobile transcription failed: $errorMessage');

        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Transcription failed: $errorMessage'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error during mobile transcription: $e');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error during transcription. Please try again.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      // Hide the loading state regardless of success or failure
      if (widget.onLoadingChanged != null) {
        widget.onLoadingChanged!(false);
      }
    }
  }

  String _formatDuration() {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(_seconds ~/ 60);
    final seconds = twoDigits(_seconds % 60);
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    if (_isRecording) {
      _stopRecording();
    }

    if (_timer != null) {
      _timer!.cancel();
    }

    _recorder.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Full recording UI with timer and buttons, matching web version
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Row with cancel button, timer, and confirm button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Cancel button
            IconButton(
              icon: const Icon(Icons.close, color: Colors.black),
              onPressed: () {
                Logger.info('Mobile cancel button pressed');

                // Just stop recording - no processing will happen
                _stopRecording();

                // Notify parent about cancellation
                if (widget.onCancel != null) {
                  widget.onCancel!();
                }
              },
            ),

            // Timer display in the center
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.mic, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                Text(
                  _formatDuration(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            // Confirm button
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.check, color: Colors.white),
                onPressed: () async {
                  // Prevent multiple clicks
                  if (!_isRecording) {
                    Logger.info('Ignoring mobile confirm click - not recording');
                    return;
                  }

                  Logger.info("Stopping mobile recording and starting transcription...");

                  // Set loading state immediately
                  if (widget.onLoadingChanged != null) {
                    widget.onLoadingChanged!(true);
                  }

                  try {
                    // First stop the recording and wait for it to complete
                    await _stopRecording();

                    // Skip if there's no recording path
                    if (_recordingPath == null) {
                      Logger.info('No recording path available');
                      if (widget.onLoadingChanged != null) {
                        widget.onLoadingChanged!(false);
                      }
                      return;
                    }

                    Logger.info('Processing mobile recording: $_recordingPath');

                    // Read the audio file as bytes
                    final file = File(_recordingPath!);
                    if (await file.exists()) {
                      final audioData = await file.readAsBytes();
                      final fileName = _recordingPath!.split('/').last;

                      // Call the transcription API directly
                      await _callTranscriptionApi(audioData, fileName);
                    } else {
                      Logger.error('Mobile recording file does not exist: $_recordingPath');
                      if (widget.onLoadingChanged != null) {
                        widget.onLoadingChanged!(false);
                      }
                    }
                  } catch (e) {
                    Logger.error('Error processing mobile recording: $e');
                    // Hide loading state on error
                    if (widget.onLoadingChanged != null) {
                      widget.onLoadingChanged!(false);
                    }
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
