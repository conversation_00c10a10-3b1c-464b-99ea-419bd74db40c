import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';

import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/web_home_provider.dart';
import '../../providers/auth_provider.dart';
import '../../theme/spacing.dart';

class HomeScreenNew extends StatefulWidget {
  const HomeScreenNew({super.key});

  @override
  State<HomeScreenNew> createState() => _HomeScreenNewState();
}

class _HomeScreenNewState extends State<HomeScreenNew> {
  final TextEditingController chatController = TextEditingController();
   bool isAudioLoading = false;

  @override
  void initState() {
    super.initState();

    // Add listener to chat controller to detect text changes
    chatController.addListener(() {
      final provider = Provider.of<WebHomeProvider>(context, listen: false);
      final hasText = chatController.text.trim().isNotEmpty;
      if (hasText != provider.hasTextInChatField) {
        provider.hasTextInChatField = hasText;
      }
    });

    // Load chat history
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadChatHistory();
    });
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed
    chatController.dispose();
    super.dispose();
  }

  // Load chat history
  void _loadChatHistory() {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);
    provider.fetchChatHistoryAndUpdateMessages();
  }

  // Handle sending a message
  void _sendMessage() {
    final text = chatController.text.trim();
    if (text.isEmpty) return;

    // Get the provider
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Add user message
    provider.addMessage(ChatMessage(
      content: text,
      isUser: true,
    ));

    // Clear input field
    chatController.text = '';

    // Show loading indicator
    provider.isLoading = true;

    // Check which quick message is selected
    if (provider.selectedQuickMessage == 'General') {
      // Call the general API
      provider.sendGeneralQuestion(text).then((response) {
        if (!mounted) return;

        provider.isLoading = false;

        if (response['success']) {
          // Add the API response
          provider.addMessage(ChatMessage(
            content: response['data']['answer'] ?? 'No answer provided',
            isUser: false,
          ));
        } else {
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${response['message']}',
            isUser: false,
          ));
        }
      }).catchError((error) {
        if (!mounted) return;

        provider.isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });
    } else if (provider.selectedQuickMessage == 'Internet') {
      // Call the internet API with user ID from AuthService
      provider.sendInternetQuestion(text).then((response) {
        if (!mounted) return;

        provider.isLoading = false;

        if (response['success']) {
          // Add the API response
          provider.addMessage(ChatMessage(
            content: response['data']['answer'] ?? 'No answer provided',
            isUser: false,
          ));
        } else {
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${response['message']}',
            isUser: false,
          ));
        }
      }).catchError((error) {
        if (!mounted) return;

        provider.isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });
    } else if (provider.selectedQuickMessage == 'NSL') {
      // Call the NSL API with user ID from AuthService
      provider.sendNslQuestion(text).then((response) {
        if (!mounted) return;

        provider.isLoading = false;

        if (response['success']) {
          // Add the API response
          provider.addMessage(ChatMessage(
            content: response['data']['answer'] ?? 'No answer provided',
            isUser: false,
          ));
        } else {
          // Add error message
          provider.addMessage(ChatMessage(
            content: 'Error: ${response['message']}',
            isUser: false,
          ));
        }
      }).catchError((error) {
        if (!mounted) return;

        provider.isLoading = false;
        provider.addMessage(ChatMessage(
          content: 'Error: $error',
          isUser: false,
        ));
      });
    } else {
      // Original behavior for other quick messages
      Future.delayed(Duration(seconds: 1), () {
        if (!mounted) return;

        provider.isLoading = false;

        // Generic response for other messages
        provider.addMessage(ChatMessage(
          content:
              'I can help you with that. Would you like me to create a solution for you?',
          isUser: false,
        ));
      });
    }
  }

  Widget chatField(BuildContext context) {
    // Get the provider
    final provider = Provider.of<WebHomeProvider>(context);

    return Container(
      margin: EdgeInsets.symmetric(
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        boxShadow: [
          BoxShadow(
            color:
                Color(0xffD0D0D0).withValues(alpha: 0.14), // 0.14 * 255 = ~36
            blurRadius: 20,
            offset: Offset(0, 3),
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxHeight: 200,
        minHeight: 60,
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: TextField(
              controller: chatController,
              maxLines: 1,
              enabled: !provider.isLoading,
              decoration: InputDecoration(
                focusColor: Colors.transparent,
                hintText: provider.isLoading
                    ? AppLocalizations.of(context)
                        .translate('home.sendingMessage')
                    : AppLocalizations.of(context).translate('home.askNSL'),
                hoverColor: Colors.transparent,
                border: OutlineInputBorder(borderSide: BorderSide.none),
                enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
                focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
                errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Add button is always shown
              hoverButtons(icon: Icon(Icons.add), onPressed: () {}),

              // Show either mic button or arrow button based on text state
              if (provider.hasTextInChatField)
                // Show arrow button when there's text
                hoverButtons(
                  icon: provider.isLoading
                      ? SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Icon(Icons.arrow_upward),
                  onPressed: provider.isLoading ? null : _sendMessage,
                )
              else
                // Show mic button when there's no text
                hoverButtons(icon: Icon(Icons.mic_none), onPressed: () {}),
            ],
          )
        ],
      ),
    );
  }

  // Helper method for hover buttons
  Widget hoverButtons({
    required Widget icon,
    required VoidCallback? onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: icon,
        onPressed: onPressed,
        color: Colors.white,
        iconSize: 18,
        padding: EdgeInsets.all(8),
        constraints: BoxConstraints(
          minWidth: 36,
          minHeight: 36,
        ),
      ),
    );
  }

  // Get image path for quick message type
  String getQuickMessageImagePath(String text) {
    switch (text) {
      case 'NSL':
        return 'assets/images/book_nsl.svg';
      case 'Solution':
        return 'assets/images/bulb_solution.svg';
      case 'General':
        return 'assets/images/loader_general.svg';
      case 'Internet':
        return 'assets/images/internet.svg';
      default:
        return 'assets/images/quickmessage/chat_icon.png';
    }
  }

  Widget quickMessage({required String text}) {
    // Get the provider
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Create a custom stateful widget for hover effect with selection state
    return _QuickMessageButton(
      text: text,
      imagePath: getQuickMessageImagePath(text),
      isSelected: provider.selectedQuickMessage == text,
      onTap: () {
        // Toggle selection if already selected, otherwise select this message
        provider.selectedQuickMessage =
            provider.selectedQuickMessage == text ? null : text;

        if (provider.selectedQuickMessage != null) {
          chatController.text = ''; // Clear any existing text
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProvider>(
      builder: (context, webHomeProvider, _) {
        // Check if we need to show mobile library screens
        final currentScreenIndex = webHomeProvider.currentScreenIndex;

        // Handle only CreateBookMobile through provider (for drawer navigation)
        if (currentScreenIndex == ScreenConstants.createBookMobile) {
          return const CreateBookMobile();
        }

        // Default mobile chat interface
        return _buildChatInterface();
      },
    );
  }

  // Build the default chat interface
  Widget _buildChatInterface() {
    return NSLKnowledgeLoaderWrapper(
       isLoading: isAudioLoading,
      child: Scaffold(
        backgroundColor: Color(0xfff6f6f6),
        appBar: AppBar(
          leading: Builder(
            builder: (context) => IconButton(
              icon: Icon(Icons.menu),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            ),
          ),
          title: Text(''),
          backgroundColor: Color(0xfff6f6f6),
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.black),
        ),
        drawer: CustomDrawer(),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              // Chat messages
              Expanded(
                child: Consumer<WebHomeProvider>(
                  builder: (context, provider, _) {
                    return provider.messages.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Consumer<AuthProvider>(
                                  builder: (context, authProvider, _) {
                                    // Get the username from the user profile
                                    final String firstName =
                                        authProvider.user?.username ?? 'Sam';
      
                                    return Column(
                                      children: [
                                        Text(
                                          'Hi $firstName,',
                                          style: TextStyle(
                                            fontSize: 24,
                                            fontFamily: 'TiemposText',
                                            fontWeight: FontWeight.w500,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                        SizedBox(height: 8),
                                        Text(
                                          'How I can help you?',
                                          style: TextStyle(
                                            fontSize: 24,
                                            fontFamily: 'TiemposText',
                                            fontWeight: FontWeight.w500,
                                            color: Colors.black,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: EdgeInsets.only(top: 16),
                            itemCount: provider.messages.length,
                            reverse: false,
                            itemBuilder: (context, index) {
                              final message = provider.messages[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: message.customContent ??
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: message.isUser ? 16 : 0,
                                        vertical: 12,
                                      ),
                                      decoration: BoxDecoration(
                                        color: message.isUser
                                            ? Color(0xffCBDDFF) // 0.1 * 255 = ~26
                                            : Colors.transparent,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: Colors.transparent,
                                          width: 1,
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            message.content,
                                            style: TextStyle(
                                                fontSize: 16,
                                                color: message.isUser
                                                    ? Colors.black87
                                                    : Colors.black,
                                                fontWeight: message.isUser
                                                    ? FontWeight.w400
                                                    : FontWeight.w500),
                                          ),
                                          // SizedBox(height: 4),
                                          // Text(
                                          //   '${message.timestamp.hour}:${message.timestamp.minute.toString().padLeft(2, '0')}',
                                          //   style: TextStyle(
                                          //     fontSize: 12,
                                          //     color: Colors.grey[600],
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    ),
                              );
                            },
                          );
                  },
                ),
              ),
      
              // Loading indicator
              Consumer<WebHomeProvider>(
                builder: (context, provider, _) {
                  return provider.isLoading
                      ? Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).primaryColor,
                                  ),
                                ),
                              ),
                              SizedBox(width: 8),
                              Text('NSL is thinking...'),
                            ],
                          ),
                        )
                      : SizedBox();
                },
              ),
      
              // Quick message buttons in a grid (2x2) - only shown when no messages
              Consumer<WebHomeProvider>(
                builder: (context, provider, _) {
                  return provider.messages.isEmpty
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildQuickMessageButton(
                                      text: 'NSL',
                                      image: 'assets/images/book_nsl.svg',
                                      isSelected:
                                          provider.selectedQuickMessage == 'NSL',
                                      isPrimary:
                                          provider.selectedQuickMessage == 'NSL',
                                      onTap: () {
                                        provider.selectedQuickMessage =
                                            provider.selectedQuickMessage == 'NSL'
                                                ? null
                                                : 'NSL';
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: _buildQuickMessageButton(
                                      text: 'Solution',
                                      image: 'assets/images/bulb_solution.svg',
                                      isSelected: provider.selectedQuickMessage ==
                                          'Solution',
                                      isPrimary: false,
                                      onTap: () {
                                        provider.selectedQuickMessage =
                                            provider.selectedQuickMessage ==
                                                    'Solution'
                                                ? null
                                                : 'Solution';
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildQuickMessageButton(
                                      text: 'General',
                                      image: 'assets/images/loader_general.svg',
                                      isSelected: provider.selectedQuickMessage ==
                                          'General',
                                      isPrimary: false,
                                      onTap: () {
                                        provider.selectedQuickMessage =
                                            provider.selectedQuickMessage ==
                                                    'General'
                                                ? null
                                                : 'General';
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: _buildQuickMessageButton(
                                      text: 'Internet',
                                      image: 'assets/images/internet.svg',
                                      isSelected: provider.selectedQuickMessage ==
                                          'Internet',
                                      isPrimary: false,
                                      onTap: () {
                                        provider.selectedQuickMessage =
                                            provider.selectedQuickMessage ==
                                                    'Internet'
                                                ? null
                                                : 'Internet';
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        )
                      : SizedBox();
                },
              ),
      
              // Chat field at the bottom
              Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: ChatInputField(
                     parentState: this,
                      chatController: chatController, sendMessage: _sendMessage)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickMessageButton({
    required String text,
    required String image,
    required bool isSelected,
    required bool isPrimary,
    required VoidCallback onTap,
  }) {
    return Material(
      color: isPrimary || isSelected ? Color(0xff0058FF) : Colors.white,
      borderRadius: BorderRadius.circular(4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: isPrimary || isSelected
                ? null
                : Border.all(color: Color(0xff707070)),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                image,
                colorFilter: ColorFilter.mode(
                  isPrimary || isSelected ? Colors.white : Colors.black87,
                  BlendMode.srcIn,
                ),
                height: 14,
                width: 14,
              ),
              SizedBox(width: 8),
              Text(
                text,
                style: TextStyle(
                  color:
                      isPrimary || isSelected ? Colors.white : Colors.black87,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'TiemposText',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _QuickMessageButton extends StatefulWidget {
  final String text;
  final String imagePath;
  final VoidCallback onTap;
  final bool isSelected;

  const _QuickMessageButton({
    required this.text,
    required this.imagePath,
    required this.onTap,
    required this.isSelected,
  });

  @override
  State<_QuickMessageButton> createState() => _QuickMessageButtonState();
}

class _QuickMessageButtonState extends State<_QuickMessageButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Determine colors based on selection state
    final backgroundColor = widget.isSelected
        ? Theme.of(context).primaryColor
        : isHovered
            ? Theme.of(context).primaryColor.withAlpha(26) // 0.1 * 255 = ~26
            : Colors.grey[100];

    final textColor = widget.isSelected
        ? Colors.white
        : isHovered
            ? Theme.of(context).primaryColor
            : Colors.black87;

    final borderColor = widget.isSelected || isHovered
        ? Theme.of(context).primaryColor
        : Colors.grey[300];

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: borderColor!,
              width: 1,
            ),
            boxShadow: widget.isSelected || isHovered
                ? [
                    BoxShadow(
                      color: Theme.of(context)
                          .primaryColor
                          .withAlpha(51), // 0.2 * 255 = ~51
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.imagePath.endsWith('.svg'))
                SvgPicture.asset(
                  widget.imagePath,
                  height: 16,
                  width: 16,
                  colorFilter: ColorFilter.mode(
                    textColor,
                    BlendMode.srcIn,
                  ),
                )
              else
                Image.asset(
                  widget.imagePath,
                  height: 16,
                  width: 16,
                  color: textColor,
                ),
              SizedBox(width: 6),
              Text(
                widget.text,
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
