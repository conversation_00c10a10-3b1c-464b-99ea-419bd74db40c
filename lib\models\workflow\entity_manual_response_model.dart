// Main Response Class
class EntityModelResponseModel {
    final bool success;
    final List<String> messages;
    final ParsedData parsedData;

    EntityModelResponseModel({
        required this.success,
        required this.messages,
        required this.parsedData,
    });

    factory EntityModelResponseModel.fromJson(Map<String, dynamic> json) {
        return EntityModelResponseModel(
            success: json['success'] ?? false,
            messages: List<String>.from(json['messages'] ?? []),
            parsedData: ParsedData.fromJson(json['parsed_data'] ?? {}),
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'success': success,
            'messages': messages,
            'parsed_data': parsedData.toJson(),
        };
    }
}

// Parsed Data Class
class ParsedData {
    final Map<String, dynamic> entities;
    final Map<String, dynamic>? validationErrors;
    final Map<String, dynamic> parsedEntities;
    final Map<String, dynamic> parsedAttributes;
    final Map<String, dynamic> parsedRelationships;

    ParsedData({
        required this.entities,
        this.validationErrors,
        required this.parsedEntities,
        required this.parsedAttributes,
        required this.parsedRelationships,
    });

    factory ParsedData.fromJson(Map<String, dynamic> json) {
        return ParsedData(
            entities: json['entities'] ?? {},
            validationErrors: json['validation_errors'],
            parsedEntities: json['parsed_entities'] ?? {},
            parsedAttributes: json['parsed_attributes'] ?? {},
            parsedRelationships: json['parsed_relationships'] ?? {},
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'entities': entities,
            'validation_errors': validationErrors,
            'parsed_entities': parsedEntities,
            'parsed_attributes': parsedAttributes,
            'parsed_relationships': parsedRelationships,
        };
    }

    // Helper methods to get typed entities when needed
    Entity? getEntityByKey(String key) {
        final entityData = entities[key];
        return entityData != null ? Entity.fromJson(entityData) : null;
    }

    List<Entity> getAllEntitiesAsObjects() {
        return entities.values
            .map((entityData) => Entity.fromJson(entityData))
            .toList();
    }

    Map<String, Entity> getEntitiesAsTypedMap() {
        return entities.map((key, value) => MapEntry(key, Entity.fromJson(value)));
    }
}

// Entity Class
class Entity {
    final String entityId;
    final String name;
    final Map<String, dynamic> attributes;
    final Map<String, dynamic> relationships;
    final Map<String, dynamic> businessRules;
    final Map<String, dynamic> calculatedFields;
    final Map<String, dynamic> validations;
    final Map<String, dynamic> constraints;
    final Map<String, dynamic> attributeMetadata;
    final Map<String, dynamic> relationshipProperties;
    final List<dynamic> syntheticData;
    final List<String> confidentialAttributes;
    final List<String> internalAttributes;
    final List<String> publicAttributes;
    final Map<String, dynamic> loadingStrategies;
    final Map<String, dynamic> archiveStrategy;
    final Map<String, dynamic> historyTracking;
    final Map<String, dynamic> workflow;
    final Map<String, dynamic> businessRulePlacement;
    final Map<String, dynamic> workflowPlacement;
    final Map<String, dynamic> entityPlacement;
    final String displayName;
    final String type;
    final String description;
    final Map<String, dynamic> enumAttributes;
    final List<String> enumValuesList;
    final String entityName;

    Entity({
        required this.entityId,
        required this.name,
        required this.attributes,
        required this.relationships,
        required this.businessRules,
        required this.calculatedFields,
        required this.validations,
        required this.constraints,
        required this.attributeMetadata,
        required this.relationshipProperties,
        required this.syntheticData,
        required this.confidentialAttributes,
        required this.internalAttributes,
        required this.publicAttributes,
        required this.loadingStrategies,
        required this.archiveStrategy,
        required this.historyTracking,
        required this.workflow,
        required this.businessRulePlacement,
        required this.workflowPlacement,
        required this.entityPlacement,
        required this.displayName,
        required this.type,
        required this.description,
        required this.enumAttributes,
        required this.enumValuesList,
        required this.entityName,
    });

    factory Entity.fromJson(Map<String, dynamic> json) {
        return Entity(
            entityId: json['entity_id'] ?? '',
            name: json['name'] ?? '',
            attributes: json['attributes'] ?? {},
            relationships: json['relationships'] ?? {},
            businessRules: json['business_rules'] ?? {},
            calculatedFields: json['calculated_fields'] ?? {},
            validations: json['validations'] ?? {},
            constraints: json['constraints'] ?? {},
            attributeMetadata: json['attribute_metadata'] ?? {},
            relationshipProperties: json['relationship_properties'] ?? {},
            syntheticData: List<dynamic>.from(json['synthetic_data'] ?? []),
            confidentialAttributes: List<String>.from(json['confidential_attributes'] ?? []),
            internalAttributes: List<String>.from(json['internal_attributes'] ?? []),
            publicAttributes: List<String>.from(json['public_attributes'] ?? []),
            loadingStrategies: json['loading_strategies'] ?? {},
            archiveStrategy: json['archive_strategy'] ?? {},
            historyTracking: json['history_tracking'] ?? {},
            workflow: json['workflow'] ?? {},
            businessRulePlacement: json['business_rule_placement'] ?? {},
            workflowPlacement: json['workflow_placement'] ?? {},
            entityPlacement: json['entity_placement'] ?? {},
            displayName: json['display_name'] ?? '',
            type: json['type'] ?? '',
            description: json['description'] ?? '',
            enumAttributes: json['enum_attributes'] ?? {},
            enumValuesList: List<String>.from(json['enum_values_list'] ?? []),
            entityName: json['entity_name'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'entity_id': entityId,
            'name': name,
            'attributes': attributes,
            'relationships': relationships,
            'business_rules': businessRules,
            'calculated_fields': calculatedFields,
            'validations': validations,
            'constraints': constraints,
            'attribute_metadata': attributeMetadata,
            'relationship_properties': relationshipProperties,
            'synthetic_data': syntheticData,
            'confidential_attributes': confidentialAttributes,
            'internal_attributes': internalAttributes,
            'public_attributes': publicAttributes,
            'loading_strategies': loadingStrategies,
            'archive_strategy': archiveStrategy,
            'history_tracking': historyTracking,
            'workflow': workflow,
            'business_rule_placement': businessRulePlacement,
            'workflow_placement': workflowPlacement,
            'entity_placement': entityPlacement,
            'display_name': displayName,
            'type': type,
            'description': description,
            'enum_attributes': enumAttributes,
            'enum_values_list': enumValuesList,
            'entity_name': entityName,
        };
    }

    // Helper methods to get typed data when needed
    Attribute? getAttributeByKey(String key) {
        final attributeData = attributes[key];
        return attributeData != null ? Attribute.fromJson(attributeData) : null;
    }

    Map<String, Attribute> getAttributesAsTypedMap() {
        return attributes.map((key, value) => MapEntry(key, Attribute.fromJson(value)));
    }

    Relationship? getRelationshipByKey(String key) {
        final relationshipData = relationships[key];
        return relationshipData != null ? Relationship.fromJson(relationshipData) : null;
    }

    Map<String, Relationship> getRelationshipsAsTypedMap() {
        return relationships.map((key, value) => MapEntry(key, Relationship.fromJson(value)));
    }

    Validation? getValidationByKey(String key) {
        final validationData = validations[key];
        return validationData != null ? Validation.fromJson(validationData) : null;
    }

    Map<String, Validation> getValidationsAsTypedMap() {
        return validations.map((key, value) => MapEntry(key, Validation.fromJson(value)));
    }

    AttributeMetadata? getAttributeMetadataByKey(String key) {
        final metadataData = attributeMetadata[key];
        return metadataData != null ? AttributeMetadata.fromJson(metadataData) : null;
    }

    Map<String, AttributeMetadata> getAttributeMetadataAsTypedMap() {
        return attributeMetadata.map((key, value) => MapEntry(key, AttributeMetadata.fromJson(value)));
    }

    EnumAttribute? getEnumAttributeByKey(String key) {
        final enumData = enumAttributes[key];
        return enumData != null ? EnumAttribute.fromJson(enumData) : null;
    }

    Map<String, EnumAttribute> getEnumAttributesAsTypedMap() {
        return enumAttributes.map((key, value) => MapEntry(key, EnumAttribute.fromJson(value)));
    }
}

// Attribute Class
class Attribute {
    final String name;
    final bool primaryKey;
    final bool foreignKey;
    final bool calculated;
    final String dataType;
    final List<dynamic> validations;
    final String defaultValue;
    final List<String>? enumValues;

    Attribute({
        required this.name,
        required this.primaryKey,
        required this.foreignKey,
        required this.calculated,
        required this.dataType,
        required this.validations,
        required this.defaultValue,
        this.enumValues,
    });

    factory Attribute.fromJson(Map<String, dynamic> json) {
        return Attribute(
            name: json['name'] ?? '',
            primaryKey: json['primary_key'] ?? false,
            foreignKey: json['foreign_key'] ?? false,
            calculated: json['calculated'] ?? false,
            dataType: json['data_type'] ?? '',
            validations: List<dynamic>.from(json['validations'] ?? []),
            defaultValue: json['default_value'] ?? '',
            enumValues: json['enum_values'] != null
                ? List<String>.from(json['enum_values'])
                : null,
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'name': name,
            'primary_key': primaryKey,
            'foreign_key': foreignKey,
            'calculated': calculated,
            'data_type': dataType,
            'validations': validations,
            'default_value': defaultValue,
            if (enumValues != null) 'enum_values': enumValues,
        };
    }
}

// Validation Constraint Class
class ValidationConstraint {
    final String constraint;
    final String id;

    ValidationConstraint({
        required this.constraint,
        required this.id,
    });

    factory ValidationConstraint.fromJson(Map<String, dynamic> json) {
        return ValidationConstraint(
            constraint: json['constraint'] ?? '',
            id: json['id'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'constraint': constraint,
            'id': id,
        };
    }
}

// Relationship Class
class Relationship {
    final String entity;
    final String type;
    final String sourceAttribute;
    final String targetAttribute;
    final String sourceEntity;
    final String targetEntity;
    final Map<String, dynamic> cardinality;
    final String joinCondition;
    final Map<String, dynamic> cascadeOperations;

    Relationship({
        required this.entity,
        required this.type,
        required this.sourceAttribute,
        required this.targetAttribute,
        required this.sourceEntity,
        required this.targetEntity,
        required this.cardinality,
        required this.joinCondition,
        required this.cascadeOperations,
    });

    factory Relationship.fromJson(Map<String, dynamic> json) {
        return Relationship(
            entity: json['entity'] ?? '',
            type: json['type'] ?? '',
            sourceAttribute: json['source_attribute'] ?? '',
            targetAttribute: json['target_attribute'] ?? '',
            sourceEntity: json['source_entity'] ?? '',
            targetEntity: json['target_entity'] ?? '',
            cardinality: json['cardinality'] ?? {},
            joinCondition: json['join_condition'] ?? '',
            cascadeOperations: json['cascade_operations'] ?? {},
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'entity': entity,
            'type': type,
            'source_attribute': sourceAttribute,
            'target_attribute': targetAttribute,
            'source_entity': sourceEntity,
            'target_entity': targetEntity,
            'cardinality': cardinality,
            'join_condition': joinCondition,
            'cascade_operations': cascadeOperations,
        };
    }

    // Helper methods for typed access
    Cardinality? getCardinalityAsTyped() {
        return cardinality.isNotEmpty ? Cardinality.fromJson(cardinality) : null;
    }

    CascadeOperations? getCascadeOperationsAsTyped() {
        return cascadeOperations.isNotEmpty ? CascadeOperations.fromJson(cascadeOperations) : null;
    }
}

// Cardinality Class
class Cardinality {
    final String source;
    final String target;

    Cardinality({
        required this.source,
        required this.target,
    });

    factory Cardinality.fromJson(Map<String, dynamic> json) {
        return Cardinality(
            source: json['source'] ?? '',
            target: json['target'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'source': source,
            'target': target,
        };
    }
}

// Cascade Operations Class
class CascadeOperations {
    final String delete;
    final String update;

    CascadeOperations({
        required this.delete,
        required this.update,
    });

    factory CascadeOperations.fromJson(Map<String, dynamic> json) {
        return CascadeOperations(
            delete: json['delete'] ?? '',
            update: json['update'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'delete': delete,
            'update': update,
        };
    }
}

// Validation Class
class Validation {
    final String attribute;
    final String constraintText;
    final String entity;
    final String validationType;
    final Map<String, dynamic> parameters;
    final String executableRule;

    Validation({
        required this.attribute,
        required this.constraintText,
        required this.entity,
        required this.validationType,
        required this.parameters,
        required this.executableRule,
    });

    factory Validation.fromJson(Map<String, dynamic> json) {
        return Validation(
            attribute: json['attribute'] ?? '',
            constraintText: json['constraint_text'] ?? '',
            entity: json['entity'] ?? '',
            validationType: json['validation_type'] ?? '',
            parameters: json['parameters'] ?? {},
            executableRule: json['executable_rule'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'attribute': attribute,
            'constraint_text': constraintText,
            'entity': entity,
            'validation_type': validationType,
            'parameters': parameters,
            'executable_rule': executableRule,
        };
    }
}

// Attribute Metadata Class
class AttributeMetadata {
    final String keyType;
    final String displayName;
    final String dataType;
    final String dataTypeFull;
    final String type;
    final String required;
    final String format;
    final String values;
    final String defaultValue;
    final String validation;
    final String? errorMessage;
    final String description;

    AttributeMetadata({
        required this.keyType,
        required this.displayName,
        required this.dataType,
        required this.dataTypeFull,
        required this.type,
        required this.required,
        required this.format,
        required this.values,
        required this.defaultValue,
        required this.validation,
        this.errorMessage,
        required this.description,
    });

    factory AttributeMetadata.fromJson(Map<String, dynamic> json) {
        return AttributeMetadata(
            keyType: json['key_type'] ?? '',
            displayName: json['display_name'] ?? '',
            dataType: json['data_type'] ?? '',
            dataTypeFull: json['data_type_full'] ?? '',
            type: json['type'] ?? '',
            required: json['required'] ?? '',
            format: json['format'] ?? '',
            values: json['values'] ?? '',
            defaultValue: json['default'] ?? '',
            validation: json['validation'] ?? '',
            errorMessage: json['error_message'],
            description: json['description'] ?? '',
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'key_type': keyType,
            'display_name': displayName,
            'data_type': dataType,
            'data_type_full': dataTypeFull,
            'type': type,
            'required': required,
            'format': format,
            'values': values,
            'default': defaultValue,
            'validation': validation,
            if (errorMessage != null) 'error_message': errorMessage,
            'description': description,
        };
    }
}

// Enum Attribute Class
class EnumAttribute {
    final String name;
    final List<String> values;

    EnumAttribute({
        required this.name,
        required this.values,
    });

    factory EnumAttribute.fromJson(Map<String, dynamic> json) {
        return EnumAttribute(
            name: json['name'] ?? '',
            values: List<String>.from(json['values'] ?? []),
        );
    }

    Map<String, dynamic> toJson() {
        return {
            'name': name,
            'values': values,
        };
    }
}

// Usage Example and Helper Methods
extension EntityValidationResponseExtensions on EntityModelResponseModel {
    // Get all loans
    List<Entity> getAllLoans() {
        return parsedData.getEntitiesAsTypedMap()
            .values
            .where((entity) => entity.entityName.toLowerCase() == 'loan')
            .toList();
    }

    // Get all customers
    List<Entity> getAllCustomers() {
        return parsedData.getEntitiesAsTypedMap()
            .values
            .where((entity) => entity.entityName.toLowerCase() == 'customer')
            .toList();
    }

    // Get entity by name
    Entity? getEntityByName(String name) {
        return parsedData.getEntityByKey(name);
    }

    // Get all enum values from all entities
    Map<String, List<String>> getAllEnumValues() {
        Map<String, List<String>> allEnums = {};

        for (var entity in parsedData.getAllEntitiesAsObjects()) {
            for (var enumAttr in entity.getEnumAttributesAsTypedMap().values) {
                allEnums[enumAttr.name] = enumAttr.values;
            }
        }

        return allEnums;
    }

    // Access entities as raw map
    Map<String, dynamic> getEntitiesAsMap() {
        return parsedData.entities;
    }

    // Get specific entity data as map
    Map<String, dynamic>? getEntityDataAsMap(String entityName) {
        return parsedData.entities[entityName];
    }

    // Get parsed entities as raw map
    Map<String, dynamic> getParsedEntitiesAsMap() {
        return parsedData.parsedEntities;
    }

    // Get specific parsed entity data as map
    Map<String, dynamic>? getParsedEntityDataAsMap(String entityName) {
        return parsedData.parsedEntities[entityName];
    }
}