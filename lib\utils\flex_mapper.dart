
/// A utility class for mapping UI controls to flex values
/// This helps in creating responsive layouts based on the type of UI control
class FlexMapper {
  // Private constructor to prevent instantiation
  FlexMapper._();

  /// Total flex value for a row (default: 4)
  /// This represents the total flex value that can be used in a single row
  /// For example, with totalRowFlex = 4:
  /// - 4 small controls (flex: 1) can fit in a row
  /// - 2 medium controls (flex: 2) can fit in a row
  /// - 1 large control (flex: 4) takes up the entire row
  static int totalRowFlex = 4;

  /// Sets the total row flex value
  /// This can be used to change the number of widgets per row
  /// For example, setting totalRowFlex = 5 would allow 5 small controls per row
  static void setTotalRowFlex(int value) {
    if (value > 0) {
      totalRowFlex = value;
    }
  }

  /// Gets the flex value for a UI control type
  /// Returns:
  /// - 1 for small controls (checkboxes, switches, etc.)
  /// - 2 for medium controls (text inputs, dropdowns, etc.)
  /// - 4 for large controls (text areas, rich text editors, etc.)
  static int getFlexValueForControl(String uiControlType) {
    // Small controls (flex: 1)
    if (_smallControls.contains(uiControlType)) {
      return 1;
    }

    // Large controls (flex: 4)
    if (_largeControls.contains(uiControlType)) {
      return 4;
    }

    // Medium controls (flex: 2) - default
    return 2;
  }

  /// Gets the maximum number of items per row for a specific UI control type
  /// This is calculated based on the flex value and totalRowFlex
  static int getMaxItemsPerRow(String uiControlType) {
    final flex = getFlexValueForControl(uiControlType);
    return (totalRowFlex / flex).floor();
  }

  /// List of UI control types that are considered small (flex: 1)
  static const List<String> _smallControls = [
    'oj-checkbox',
    'oj-switch',
    'oj-radio',
    'oj-button',
    'oj-icon',
  ];

  /// List of UI control types that are considered large (flex: 4)
  static const List<String> _largeControls = [
    'oj-text-area',
    'oj-rich-text',
    'oj-file-picker',
    'oj-image-picker',
    'oj-map',
    'oj-chart',
    'oj-table',
  ];
}
