import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'web_audio_widget_mobile.dart'
    if (dart.library.html) 'web_audio_widget_web.dart';

/// A widget for web audio recording that shows a timer
/// This widget is designed to match the UI of SpeechRecognitionWidget
/// Uses platform-specific implementations to avoid web-only imports on mobile
class WebAudioRecorderWidget extends StatelessWidget {
  /// Callback when recording is complete
  final Function(Uint8List audioData, String fileName)? onRecordingComplete;

  /// Text editing controller to update with transcribed text
  final TextEditingController? chatController;

  /// Callback when recording is cancelled
  final VoidCallback? onCancel;

  /// Callback when loading state changes
  final Function(bool isLoading)? onLoadingChanged;

  const WebAudioRecorderWidget({
    super.key,
    this.onRecordingComplete,
    this.chatController,
    this.onCancel,
    this.onLoadingChanged,
  });

  @override
  Widget build(BuildContext context) {
    // Delegate to platform-specific implementation
    return WebAudioRecorderWidgetImpl(
      onRecordingComplete: onRecordingComplete,
      chatController: chatController,
      onCancel: onCancel,
      onLoadingChanged: onLoadingChanged,
    );
  }
}
