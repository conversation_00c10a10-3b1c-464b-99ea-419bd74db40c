import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'dart:math' as math;

// Model class for module items
class ModuleItem {
  final String name;
  bool isExpanded;
  bool isSubModuleInputExpanded;
  List<String> subModules;

  ModuleItem({
    required this.name,
    this.isExpanded = false,
    this.isSubModuleInputExpanded = false,
    List<String>? subModules,
  }) : subModules = subModules ?? [];
}

class AddModulesMobileView extends StatefulWidget {
  const AddModulesMobileView({super.key});

  @override
  State<AddModulesMobileView> createState() => _AddModulesMobileViewState();
}

class _AddModulesMobileViewState extends State<AddModulesMobileView> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _chatController = TextEditingController();
  final TextEditingController _moduleController = TextEditingController();
  bool isHovered = false;
  bool showBookModulesDetail = false;
  bool showSearchOverlay = false;
  bool isModulesExpanded = false;

  // List to store created modules
  List<ModuleItem> createdModules = [];

  // Map to store text controllers for each module's sub-module input
  Map<int, TextEditingController> subModuleControllers = {};

  // Focus nodes for input fields
  final FocusNode _moduleFocusNode = FocusNode();
  Map<int, FocusNode> subModuleFocusNodes = {};

  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _searchController.addListener(() {
      setState(() {
        // Rebuild when search text changes
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleSpacing: 0, // Remove default spacing between leading and title
        leading: IconButton(
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          icon: const Icon(Icons.chevron_left, color: Colors.black, size: 24),
          onPressed: () {
            if (showBookModulesDetail) {
              // Go back to the main view
              setState(() {
                showBookModulesDetail = false;
              });
            } else {
              // Use Navigator.pop() to go back to the previous screen
              Navigator.pop(context);
            }
          },
        ),
        title: Text(
          showBookModulesDetail ? 'Book Name' : 'Ecommerce (B2C)',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: MouseRegion(
              onEnter: (_) => setState(() => isHovered = true),
              onExit: (_) => setState(() => isHovered = false),
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..scale(-1.0, 1.0, 1.0), // Flip horizontally
                  child: Icon(
                    Icons.login,
                    size: 24,
                    color: isHovered
                        ? const Color(0xff0058FF)
                        : Colors.grey.shade600, // Blue icon on hover
                  ),
                ),
                onPressed: () {},
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Content area
          Expanded(
            child: showBookModulesDetail
                ? _buildBookModulesDetailContent()
                : SingleChildScrollView(
                    child: _buildMainContent(),
                  ),
          ),
          // Chat input field at the bottom
          Padding(
            padding: const EdgeInsets.fromLTRB(
                AppSpacing.md, 0, AppSpacing.md, AppSpacing.md),
            child: ChatInputField(
              chatController: _chatController,
              sendMessage: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        // Add Modules Section
        GestureDetector(
          onTap: () {
            setState(() {
              showBookModulesDetail = true;
            });
          },
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    child: SvgPicture.asset(
                      'assets/images/book_nsl.svg',
                      width: 16,
                      height: 16,
                      color: Colors.grey[600],
                    ),
                    // decoration: BoxDecoration(
                    //   border: Border.all(color: Colors.grey[400]!, width: 1),
                    //   borderRadius: BorderRadius.circular(2),
                    // ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Add Modules',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward,
                    color: Colors.blue,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
        // Stats Section
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: const Color(0xFFE5E7EB), width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    child: _buildStatItem('Agent', '3'),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: const Color(0xFFE5E7EB), width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    child: _buildStatItem('Objects', '12'),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: const Color(0xFFE5E7EB), width: 1),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    child: _buildStatItem('Solutions', '15'),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),

        // Create Solution Section
        Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create a solution of a Product management',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Last Message 16 hours ago',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      child: SvgPicture.asset(
                        'assets/images/folder.svg',
                        width: 16,
                        height: 16,
                        color: Colors.grey[600],
                      ),
                      // decoration: BoxDecoration(
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'V00012',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '22/04/2025',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBookModulesDetailContent() {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title, count, and search
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: _buildDragSolutionHeader(),
            ),
            // Content area
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 12),
                  // Solution Cards - Natural height container
                  Column(
                    children: [
                      _buildSolutionContent(),
                      const SizedBox(
                          height: 16), // 16px gap between grid and pagination
                      // Carousel Indicators
                      _buildCarouselIndicators(),
                      const SizedBox(height: 18),
                    ],
                  ),
                  // Horizontal Divider
                  Container(
                    height: 1,
                    color: const Color(0xFFE5E7EB),
                  ),
                  const SizedBox(height: 18),
                  // Modules Section - Now uses intrinsic height
                  _buildModulesSection(),
                  const SizedBox(height: 18), // Bottom padding
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDragSolutionHeader() {
    List<String> allSolutions = _getAllSolutions();
    int solutionCount = allSolutions.length;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: showSearchOverlay ? 48 : 24,
      child: Stack(
        children: [
          // Default header (title + count + search icon)
          if (!showSearchOverlay)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Drag Your Solution',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$solutionCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      showSearchOverlay = true;
                    });
                  },
                  child: Icon(
                    Icons.search,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          // Search overlay
          if (showSearchOverlay)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: TextField(
                        controller: _searchController,
                        autofocus: true,
                        decoration: InputDecoration(
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          hintText: 'Search solutions...',
                          border: InputBorder.none,
                          hintStyle:
                              TextStyle(fontSize: 14, color: Colors.grey[500]),
                          isDense: true,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                    onPressed: () {
                      setState(() {
                        showSearchOverlay = false;
                        _searchController.clear();
                      });
                    },
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Extract solution data into a separate method for reusability
  List<String> _getAllSolutions() {
    // Simulate API data - single flat list of all solution cards
    // This can return any number of cards from API
    return [
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
    ];
  }

  Widget _buildSolutionContent() {
    List<String> allSolutions = _getAllSolutions();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // If 4 or fewer cards, show simple grid without carousel
    if (allSolutions.length <= 4) {
      return _buildSimpleSolutionGrid(allSolutions);
    }

    // If more than 4 cards, show carousel
    return _buildSolutionCarousel(allSolutions);
  }

  Widget _buildSimpleSolutionGrid(List<String> solutions) {
    if (solutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate card dimensions with responsive width
        double availableWidth = constraints.maxWidth;
        double spacing = 12.0;
        double cardWidth = 148.0; // Target width for larger screens

        // Responsive card width calculation for different screen sizes
        if (availableWidth <= 375) {
          // Small screens (iPhone SE, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 414) {
          // Medium screens (iPhone 6/7/8 Plus, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 768) {
          // Tablet screens (iPad Mini, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else {
          // Large tablet screens - use target width or adjust if needed
          cardWidth = math.min(200.0, (availableWidth - spacing) / 2);
        }

        return _buildSolutionGrid(solutions, cardWidth: cardWidth);
      },
    );
  }

  Widget _buildSolutionCarousel(List<String> allSolutions) {
    if (allSolutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Divide the flat list into pages of 4 cards each
    List<List<String>> solutionPages = [];
    for (int i = 0; i < allSolutions.length; i += 4) {
      int end = (i + 4 < allSolutions.length) ? i + 4 : allSolutions.length;
      solutionPages.add(allSolutions.sublist(i, end));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate card dimensions with responsive width
        double availableWidth = constraints.maxWidth;
        double spacing = 12.0;
        double cardWidth = 148.0; // Target width for larger screens

        // Responsive card width calculation for different screen sizes
        if (availableWidth <= 375) {
          // Small screens (iPhone SE, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 414) {
          // Medium screens (iPhone 6/7/8 Plus, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 768) {
          // Tablet screens (iPad Mini, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else {
          // Large tablet screens - use target width or adjust if needed
          cardWidth = math.min(200.0, (availableWidth - spacing) / 2);
        }

        // Calculate height based on grid content
        double aspectRatio = 2.5; // Width to height ratio
        double cardHeight = cardWidth / aspectRatio;
        double gridHeight =
            (cardHeight * 2) + spacing; // 2 rows + spacing between them

        return SizedBox(
          height: gridHeight,
          child: PageView.builder(
            controller: _pageController,
            itemCount: solutionPages.length,
            itemBuilder: (context, pageIndex) {
              return _buildSolutionGrid(solutionPages[pageIndex],
                  cardWidth: cardWidth);
            },
          ),
        );
      },
    );
  }

  Widget _buildCarouselIndicators() {
    // Get filtered solutions
    List<String> allSolutions = _getAllSolutions();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // Only show indicators if there are more than 4 cards (carousel mode)
    if (allSolutions.length <= 4) {
      return const SizedBox(height: 8); // Minimal spacing when no indicators
    }

    int totalPages = (allSolutions.length / 4).ceil(); // 4 cards per page

    return Center(
      child: SmoothPageIndicator(
        controller: _pageController,
        count: math.max(1, totalPages),
        effect: WormEffect(
            dotHeight: 8,
            dotWidth: 8,
            spacing: 4,
            activeDotColor: Colors.grey.shade600,
            dotColor: Colors.grey.shade600,
            strokeWidth: 1,
            paintStyle: PaintingStyle.stroke),
      ),
    );
  }

  Widget _buildSolutionGrid(List<String> solutions, {double? cardWidth}) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double spacing = 12.0;

        // Fixed aspect ratio for consistent card sizing
        double aspectRatio = 2.5; // Width to height ratio

        return GridView.builder(
          padding: EdgeInsets.zero, // Remove default padding
          shrinkWrap: true, // Important: allows grid to size itself
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: aspectRatio, // Use fixed aspect ratio
          ),
          itemCount: solutions.length,
          itemBuilder: (context, index) => _buildSolutionCard(solutions[index]),
        );
      },
    );
  }

  Widget _buildSolutionCard(String title) {
    return GestureDetector(
      // onLongPress: () => _showSolutionPopupMenu(context, title),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 11.5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Solution icon placeholder
              // Container(
              //   width: 32,
              //   height: 32,
              //   decoration: BoxDecoration(
              //     color: Colors.grey.shade100,
              //     borderRadius: BorderRadius.circular(4),
              //   ),
              //   child: Icon(
              //     Icons.description_outlined,
              //     size: 20,
              //     color: Colors.grey.shade600,
              //   ),
              // ),
              // const SizedBox(height: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    height: 1.17,
                    color: Color(0xff242424),
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // void _showSolutionPopupMenu(BuildContext context, String title) {
  //   showDialog(
  //     context: context,
  //     barrierColor: Colors.transparent,
  //     useSafeArea: false,
  //     builder: (BuildContext context) {
  //       return _SolutionModalView(
  //         title: title,
  //         onClose: () => Navigator.of(context).pop(),
  //         createdModules: createdModules,
  //         onModuleAdded: (ModuleItem module) {
  //           setState(() {
  //             createdModules.add(module);
  //           });
  //         },
  //         onModuleToggled: (int index) {
  //           setState(() {
  //             createdModules[index].isExpanded =
  //                 !createdModules[index].isExpanded;
  //           });
  //         },
  //       );
  //     },
  //   );
  // }

  void _sendMessage() {
    if (_chatController.text.trim().isNotEmpty) {
      // Handle sending message
      debugPrint('Sending message: ${_chatController.text}');
      _chatController.clear();
    }
  }

  Widget _buildStatItem(String label, String value) {
    IconData iconData;
    switch (label) {
      case 'Agent':
        iconData = Icons.group_outlined;
        break;
      case 'Objects':
        iconData = Icons.language_outlined;
        break;
      case 'Solutions':
        iconData = Icons.account_tree_outlined;
        break;
      default:
        iconData = Icons.circle_outlined;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 14,
              color: Colors.black87,
            ),
            const SizedBox(width: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward,
              color: Colors.blue,
              size: 14,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModulesSection() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Always visible "Modules" heading
          const Text(
            'Modules',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          // Animated container with border containing button and input
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: const Color(0xFFD0D0D0),
                width: 0.5,
              ),
            ),
            child: AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Column(
                children: [
                  // + Modules button (always visible)
                  _buildAddModulesButton(),
                  // Expandable input section with smooth animation
                  AnimatedCrossFade(
                    duration: const Duration(milliseconds: 300),
                    crossFadeState: isModulesExpanded
                        ? CrossFadeState.showSecond
                        : CrossFadeState.showFirst,
                    firstChild: const SizedBox.shrink(),
                    secondChild: _buildExpandedModulesInput(),
                  ),
                ],
              ),
            ),
          ),
          // Display created modules
          if (createdModules.isNotEmpty) ...[
            const SizedBox(height: 10),
            ...createdModules.asMap().entries.map((entry) {
              int index = entry.key;
              ModuleItem module = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _buildCreatedModuleItem(module, index),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildAddModulesButton() {
    return InkWell(
      onTap: () {
        if (!isModulesExpanded) {
          setState(() {
            isModulesExpanded = true;
          });
          // Request focus after the animation completes
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _moduleFocusNode.requestFocus();
          });
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 11.5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              size: 16,
              color: Colors.black,
            ),
            const SizedBox(width: 8),
            const Text(
              'Modules',
              style: TextStyle(
                fontSize: 14,
                height: 1.357,
                color: Color(0xff242424),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedModulesInput() {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: const Color(0xFFD0D0D0),
            width: 0.5,
          ),
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Input field
            TextField(
              controller: _moduleController,
              focusNode: _moduleFocusNode,
              decoration: InputDecoration(
                hintText: 'Type here',
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: Color(0xffBEBEBE),
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 10.5),
            // Divider line
            Container(
              height: 1,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Cancel button
                GestureDetector(
                  onTap: () {
                    setState(() {
                      isModulesExpanded = false;
                      _moduleController.clear();
                    });
                  },
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Confirm button
                GestureDetector(
                  onTap: () {
                    if (_moduleController.text.trim().isNotEmpty) {
                      // Add the new module to the list
                      setState(() {
                        createdModules.add(ModuleItem(
                          name: _moduleController.text.trim(),
                          isExpanded: false,
                        ));
                        isModulesExpanded = false;
                        _moduleController.clear();
                      });
                    }
                  },
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreatedModuleItem(ModuleItem module, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xffF5F5F5),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFE5E7EB),
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          // Main module row
          InkWell(
            onTap: () {
              setState(() {
                module.isExpanded = !module.isExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Dropdown arrow
                  AnimatedRotation(
                    turns: module.isExpanded ? 0.25 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.chevron_right,
                      size: 18,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Module name
                  Expanded(
                    child: Text(
                      module.name,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xff242424),
                      ),
                    ),
                  ),
                  // Plus icon
                  Icon(
                    Icons.add,
                    size: 18,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
          ),
          // Expandable content (if expanded)
          if (module.isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  // Sub-module input section
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: const Color(0xFFD0D0D0),
                        width: 0.5,
                      ),
                    ),
                    child: AnimatedSize(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      child: Column(
                        children: [
                          // + Sub-module button (always visible)
                          _buildAddSubModuleButton(module, index),
                          // Expandable input section with smooth animation
                          AnimatedCrossFade(
                            duration: const Duration(milliseconds: 300),
                            crossFadeState: module.isSubModuleInputExpanded
                                ? CrossFadeState.showSecond
                                : CrossFadeState.showFirst,
                            firstChild: const SizedBox.shrink(),
                            secondChild: _buildExpandedSubModuleInput(index),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Display sub-modules
                  if (module.subModules.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    ...module.subModules.map((subModule) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: _buildSubModuleItem(subModule),
                      );
                    }),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAddSubModuleButton(ModuleItem module, int index) {
    return InkWell(
      onTap: () {
        setState(() {
          module.isSubModuleInputExpanded = !module.isSubModuleInputExpanded;
        });
        // Request focus after the animation completes if expanding
        if (module.isSubModuleInputExpanded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Ensure we have a focus node for this module
            if (!subModuleFocusNodes.containsKey(index)) {
              subModuleFocusNodes[index] = FocusNode();
            }
            subModuleFocusNodes[index]?.requestFocus();
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 11.5),
        child: Row(
          children: [
            Icon(
              Icons.add,
              size: 16,
              color: Colors.black,
            ),
            const SizedBox(width: 8),
            const Text(
              'Add Sub-module',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xff242424),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedSubModuleInput(int moduleIndex) {
    // Ensure we have a controller for this module
    if (!subModuleControllers.containsKey(moduleIndex)) {
      subModuleControllers[moduleIndex] = TextEditingController();
    }

    // Ensure we have a focus node for this module
    if (!subModuleFocusNodes.containsKey(moduleIndex)) {
      subModuleFocusNodes[moduleIndex] = FocusNode();
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: const Color(0xFFD0D0D0),
            width: 0.5,
          ),
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Input field
            TextField(
              controller: subModuleControllers[moduleIndex],
              focusNode: subModuleFocusNodes[moduleIndex],
              decoration: InputDecoration(
                hintText: 'Type here',
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: Color(0xffBEBEBE),
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 10.5),
            // Divider line
            Container(
              height: 1,
              color: Colors.grey[300],
            ),
            const SizedBox(height: 12),
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Cancel button
                GestureDetector(
                  onTap: () {
                    setState(() {
                      createdModules[moduleIndex].isSubModuleInputExpanded =
                          false;
                      subModuleControllers[moduleIndex]?.clear();
                    });
                  },
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Confirm button
                GestureDetector(
                  onTap: () {
                    final controller = subModuleControllers[moduleIndex];
                    if (controller != null &&
                        controller.text.trim().isNotEmpty) {
                      setState(() {
                        createdModules[moduleIndex]
                            .subModules
                            .add(controller.text.trim());
                        createdModules[moduleIndex].isSubModuleInputExpanded =
                            false;
                        controller.clear();
                      });
                    }
                  },
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubModuleItem(String subModuleName) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFD0D0D0),
          width: 0.5,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Dropdown arrow
            Icon(
              Icons.chevron_right,
              size: 18,
              color: Colors.black,
            ),
            const SizedBox(width: 8),
            // Sub-module name
            Expanded(
              child: Text(
                subModuleName,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
            // Plus icon
            Icon(
              Icons.add,
              size: 18,
              color: Colors.black,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _chatController.dispose();
    _moduleController.dispose();
    _pageController.dispose();
    _moduleFocusNode.dispose();
    // Dispose all sub-module controllers
    for (var controller in subModuleControllers.values) {
      controller.dispose();
    }
    // Dispose all sub-module focus nodes
    for (var focusNode in subModuleFocusNodes.values) {
      focusNode.dispose();
    }
    super.dispose();
  }
}

class _SolutionModalView extends StatefulWidget {
  final String title;
  final VoidCallback onClose;
  final List<ModuleItem> createdModules;
  final Function(ModuleItem) onModuleAdded;
  final Function(int) onModuleToggled;

  const _SolutionModalView({
    required this.title,
    required this.onClose,
    required this.createdModules,
    required this.onModuleAdded,
    required this.onModuleToggled,
  });

  @override
  State<_SolutionModalView> createState() => _SolutionModalViewState();
}

class _SolutionModalViewState extends State<_SolutionModalView> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _moduleController = TextEditingController();
  late PageController _pageController;
  bool showSearchOverlay = false;
  bool isModulesExpanded = false;

  // Map to store text controllers for each module's sub-module input
  Map<int, TextEditingController> subModuleControllers = {};

  // Focus nodes for input fields
  final FocusNode _moduleFocusNode = FocusNode();
  Map<int, FocusNode> subModuleFocusNodes = {};

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _searchController.addListener(() {
      setState(() {
        // Rebuild when search text changes
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _moduleController.dispose();
    _pageController.dispose();
    _moduleFocusNode.dispose();
    // Dispose all sub-module controllers
    for (var controller in subModuleControllers.values) {
      controller.dispose();
    }
    // Dispose all sub-module focus nodes
    for (var focusNode in subModuleFocusNodes.values) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFD0D0D0).withValues(alpha: 0.8),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.chevron_left,
                        color: Colors.black, size: 24),
                    onPressed: widget.onClose,
                  ),
                  Expanded(
                    child: Text(
                      'Book Name',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  IconButton(
                    icon: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
                      child:
                          const Icon(Icons.login, size: 24, color: Colors.grey),
                    ),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
            // Main content area
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    // Header with title, count, and search
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                      child: _buildDragSolutionHeader(),
                    ),
                    // Content area
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 12),
                            // Solution Cards - Fixed height container
                            SizedBox(
                              height: 150, // Fixed height for modal
                              child: Column(
                                children: [
                                  Expanded(child: _buildSolutionContent()),
                                  const SizedBox(
                                      height:
                                          16), // 16px gap between grid and pagination
                                  // Carousel Indicators
                                  _buildCarouselIndicators(),
                                  const SizedBox(height: 8),
                                ],
                              ),
                            ),
                            // Horizontal Divider
                            Container(
                              height: 1,
                              color: const Color(0xFFE5E7EB),
                            ),
                            const SizedBox(height: 16),
                            // Modules Section
                            Expanded(
                              child: _buildModulesSection(),
                            ),
                            const SizedBox(height: 16), // Bottom padding
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Positioned(
        bottom: 32,
        right: 32,
        child: GestureDetector(
          onTap: widget.onClose,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.close,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  // Extract solution data into a separate method for reusability
  List<String> _getAllSolutions() {
    // Simulate API data - single flat list of all solution cards
    return [
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
      'Solutions PM with V01/V02',
    ];
  }

  Widget _buildDragSolutionHeader() {
    List<String> allSolutions = _getAllSolutions();
    int solutionCount = allSolutions.length;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: showSearchOverlay ? 48 : 24,
      child: Stack(
        children: [
          // Default header (title + count + search icon)
          if (!showSearchOverlay)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Drag Your Solution',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$solutionCount',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      showSearchOverlay = true;
                    });
                  },
                  child: Icon(
                    Icons.search,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          // Search overlay
          if (showSearchOverlay)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: TextField(
                        controller: _searchController,
                        autofocus: true,
                        decoration: InputDecoration(
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          hintText: 'Search solutions...',
                          border: InputBorder.none,
                          hintStyle:
                              TextStyle(fontSize: 14, color: Colors.grey[500]),
                          isDense: true,
                          contentPadding:
                              const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                    onPressed: () {
                      setState(() {
                        showSearchOverlay = false;
                        _searchController.clear();
                      });
                    },
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSolutionContent() {
    List<String> allSolutions = _getAllSolutions();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // If 4 or fewer cards, show simple grid without carousel
    if (allSolutions.length <= 4) {
      return _buildSimpleSolutionGrid(allSolutions);
    }

    // If more than 4 cards, show carousel
    return _buildSolutionCarousel(allSolutions);
  }

  Widget _buildSimpleSolutionGrid(List<String> solutions) {
    if (solutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate card dimensions with responsive width
        double availableWidth = constraints.maxWidth;
        double spacing = 12.0;
        double cardWidth = 148.0; // Target width for larger screens

        // Responsive card width calculation for different screen sizes
        if (availableWidth <= 375) {
          // Small screens (iPhone SE, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 414) {
          // Medium screens (iPhone 6/7/8 Plus, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 768) {
          // Tablet screens (iPad Mini, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else {
          // Large tablet screens - use target width or adjust if needed
          cardWidth = math.min(200.0, (availableWidth - spacing) / 2);
        }

        return _buildSolutionGrid(solutions, cardWidth: cardWidth);
      },
    );
  }

  Widget _buildSolutionCarousel(List<String> allSolutions) {
    if (allSolutions.isEmpty) {
      return const Center(
        child: Text(
          'No solutions found',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    // Divide the flat list into pages of 4 cards each
    List<List<String>> solutionPages = [];
    for (int i = 0; i < allSolutions.length; i += 4) {
      int end = (i + 4 < allSolutions.length) ? i + 4 : allSolutions.length;
      solutionPages.add(allSolutions.sublist(i, end));
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate card dimensions with responsive width
        double availableWidth = constraints.maxWidth;
        double spacing = 12.0;
        double cardWidth = 148.0; // Target width for larger screens

        // Responsive card width calculation for different screen sizes
        if (availableWidth <= 375) {
          // Small screens (iPhone SE, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 414) {
          // Medium screens (iPhone 6/7/8 Plus, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else if (availableWidth <= 768) {
          // Tablet screens (iPad Mini, etc.)
          cardWidth = (availableWidth - spacing) / 2;
        } else {
          // Large tablet screens - use target width or adjust if needed
          cardWidth = math.min(200.0, (availableWidth - spacing) / 2);
        }

        // Calculate height based on grid content
        double aspectRatio = 2.5; // Width to height ratio
        double cardHeight = cardWidth / aspectRatio;
        double gridHeight =
            (cardHeight * 2) + spacing; // 2 rows + spacing between them

        return SizedBox(
          height: gridHeight,
          child: PageView.builder(
            controller: _pageController,
            itemCount: solutionPages.length,
            itemBuilder: (context, pageIndex) {
              return _buildSolutionGrid(solutionPages[pageIndex],
                  cardWidth: cardWidth);
            },
          ),
        );
      },
    );
  }

  Widget _buildCarouselIndicators() {
    // Get filtered solutions
    List<String> allSolutions = _getAllSolutions();

    // Filter solutions based on search query
    if (_searchController.text.isNotEmpty) {
      allSolutions = allSolutions
          .where((solution) => solution
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    }

    // Only show indicators if there are more than 4 cards (carousel mode)
    if (allSolutions.length <= 4) {
      return const SizedBox(height: 8); // Minimal spacing when no indicators
    }

    int totalPages = (allSolutions.length / 4).ceil(); // 4 cards per page

    return Center(
      child: SmoothPageIndicator(
        controller: _pageController,
        count: math.max(1, totalPages),
        effect: WormEffect(
          dotHeight: 6,
          dotWidth: 6,
          spacing: 8,
          activeDotColor: Colors.black,
          dotColor: Colors.grey.shade300,
        ),
      ),
    );
  }

  Widget _buildSolutionGrid(List<String> solutions, {double? cardWidth}) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double spacing = 12.0;

        // Fixed aspect ratio for consistent card sizing
        double aspectRatio = 2.5; // Width to height ratio

        return GridView.count(
          shrinkWrap: true, // Important: allows grid to size itself
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: aspectRatio, // Use fixed aspect ratio
          children:
              solutions.map((title) => _buildSolutionCard(title)).toList(),
        );
      },
    );
  }

  Widget _buildSolutionCard(String title) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: const Color(0xFFD0D0D0), width: 0.5),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 11.5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  height: 1.17,
                  color: Colors.black87,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModulesSection() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Always visible "Modules" heading
          const Text(
            'Modules',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          // Animated container with border containing button and input
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            child: AnimatedSize(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Column(
                children: [
                  // + Modules button (always visible)
                  _buildAddModulesButton(),
                  // Expandable input section with smooth animation
                  AnimatedCrossFade(
                    duration: const Duration(milliseconds: 300),
                    crossFadeState: isModulesExpanded
                        ? CrossFadeState.showSecond
                        : CrossFadeState.showFirst,
                    firstChild: const SizedBox.shrink(),
                    secondChild: _buildExpandedModulesInput(),
                  ),
                ],
              ),
            ),
          ),
          // Display created modules
          if (widget.createdModules.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...widget.createdModules.asMap().entries.map((entry) {
              int index = entry.key;
              ModuleItem module = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: _buildCreatedModuleItem(module, index),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildAddModulesButton() {
    return InkWell(
      onTap: () {
        setState(() {
          isModulesExpanded = !isModulesExpanded;
        });
        // Request focus after the animation completes if expanding
        if (isModulesExpanded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _moduleFocusNode.requestFocus();
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              Icons.add,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            const Text(
              'Modules',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedModulesInput() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          // Text input field
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: const Color(0xFFE5E7EB),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _moduleController,
                focusNode: _moduleFocusNode,
                decoration: const InputDecoration(
                  hintText: 'Enter module name...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Confirm button
          GestureDetector(
            onTap: () {
              if (_moduleController.text.trim().isNotEmpty) {
                // Add the new module to the list
                widget.onModuleAdded(ModuleItem(
                  name: _moduleController.text.trim(),
                  isExpanded: false,
                ));
                setState(() {
                  isModulesExpanded = false;
                  _moduleController.clear();
                });
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreatedModuleItem(ModuleItem module, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Main module row
          InkWell(
            onTap: () {
              widget.onModuleToggled(index);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Dropdown arrow
                  AnimatedRotation(
                    turns: module.isExpanded ? 0.25 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Module name
                  Expanded(
                    child: Text(
                      module.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  // Plus icon
                  Icon(
                    Icons.add,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),
          // Expandable content (if expanded)
          if (module.isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: [
                  // Sub-module input section
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFFE5E7EB),
                        width: 1,
                      ),
                    ),
                    child: AnimatedSize(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      child: Column(
                        children: [
                          // + Sub-module button (always visible)
                          _buildAddSubModuleButton(module, index),
                          // Expandable input section with smooth animation
                          AnimatedCrossFade(
                            duration: const Duration(milliseconds: 300),
                            crossFadeState: module.isSubModuleInputExpanded
                                ? CrossFadeState.showSecond
                                : CrossFadeState.showFirst,
                            firstChild: const SizedBox.shrink(),
                            secondChild: _buildExpandedSubModuleInput(index),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Display sub-modules
                  if (module.subModules.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    ...module.subModules.map((subModule) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: _buildSubModuleItem(subModule),
                      );
                    }),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAddSubModuleButton(ModuleItem module, int index) {
    return InkWell(
      onTap: () {
        setState(() {
          module.isSubModuleInputExpanded = !module.isSubModuleInputExpanded;
        });
        // Request focus after the animation completes if expanding
        if (module.isSubModuleInputExpanded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Ensure we have a focus node for this module
            if (!subModuleFocusNodes.containsKey(index)) {
              subModuleFocusNodes[index] = FocusNode();
            }
            subModuleFocusNodes[index]?.requestFocus();
          });
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              Icons.add,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            const Text(
              'Add Sub-module',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedSubModuleInput(int moduleIndex) {
    // Ensure we have a controller for this module
    if (!subModuleControllers.containsKey(moduleIndex)) {
      subModuleControllers[moduleIndex] = TextEditingController();
    }

    // Ensure we have a focus node for this module
    if (!subModuleFocusNodes.containsKey(moduleIndex)) {
      subModuleFocusNodes[moduleIndex] = FocusNode();
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          // Text input field
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: const Color(0xFFE5E7EB),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: subModuleControllers[moduleIndex],
                focusNode: subModuleFocusNodes[moduleIndex],
                decoration: const InputDecoration(
                  hintText: 'Enter sub-module name...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Confirm button
          GestureDetector(
            onTap: () {
              final controller = subModuleControllers[moduleIndex];
              if (controller != null && controller.text.trim().isNotEmpty) {
                setState(() {
                  widget.createdModules[moduleIndex].subModules
                      .add(controller.text.trim());
                  widget.createdModules[moduleIndex].isSubModuleInputExpanded =
                      false;
                  controller.clear();
                });
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubModuleItem(String subModuleName) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Dropdown arrow
            Icon(
              Icons.chevron_right,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            // Sub-module name
            Expanded(
              child: Text(
                subModuleName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            // Plus icon
            Icon(
              Icons.add,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }
}
