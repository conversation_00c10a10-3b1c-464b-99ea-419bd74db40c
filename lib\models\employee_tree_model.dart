import 'package:flutter/material.dart';

@immutable
class EmployeeData {
  final String id;
  final String name;
  final String email;
  final String position;
  final String department;
  final String? departmentColor;
  final String? managerId;
  final String? phone;
  final String? location;
  final String? team;
  final String? level;
  final DateTime? joinDate;
  final String? profileImage;
  final bool isExpanded;
  final List<EmployeeData> children;

  const EmployeeData({
    required this.id,
    required this.name,
    required this.email,
    required this.position,
    required this.department,
    this.departmentColor,
    this.managerId,
    this.phone,
    this.location,
    this.team,
    this.level,
    this.joinDate,
    this.profileImage,
    this.isExpanded = false,
    this.children = const [],
  });

  factory EmployeeData.fromJson(Map<String, dynamic> json) {
    // Handle both 'role' and 'position' fields for backward compatibility
    final position = json['role'] ?? json['position'] ?? '';
    
    // Parse children recursively
    final childrenJson = json['children'] as List<dynamic>? ?? [];
    final children = childrenJson.map((child) => EmployeeData.fromJson(child as Map<String, dynamic>)).toList();
    
    return EmployeeData(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      position: position,
      department: json['department'] ?? '',
      departmentColor: json['departmentColor'],
      managerId: json['managerId']?.toString(),
      phone: json['phone'],
      location: json['location'],
      team: json['team'],
      level: json['level']?.toString(),
      joinDate: json['joinDate'] != null 
          ? DateTime.tryParse(json['joinDate']) 
          : null,
      profileImage: json['profileImage'],
      isExpanded: json['isExpanded'] ?? false,
      children: children,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': position, // Use 'role' for new format
      'position': position, // Keep 'position' for backward compatibility
      'department': department,
      'departmentColor': departmentColor,
      'managerId': managerId,
      'phone': phone,
      'location': location,
      'team': team,
      'level': level,
      'joinDate': joinDate?.toIso8601String(),
      'profileImage': profileImage,
      'isExpanded': isExpanded,
      'children': children.map((child) => child.toJson()).toList(),
    };
  }

  // Helper method to get all employees in a flat list (for search functionality)
  List<EmployeeData> getAllEmployees() {
    List<EmployeeData> allEmployees = [this];
    for (var child in children) {
      allEmployees.addAll(child.getAllEmployees());
    }
    return allEmployees;
  }

  // Helper method to update expansion state
  EmployeeData copyWith({
    String? id,
    String? name,
    String? email,
    String? position,
    String? department,
    String? departmentColor,
    String? managerId,
    String? phone,
    String? location,
    String? team,
    String? level,
    DateTime? joinDate,
    String? profileImage,
    bool? isExpanded,
    List<EmployeeData>? children,
  }) {
    return EmployeeData(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      position: position ?? this.position,
      department: department ?? this.department,
      departmentColor: departmentColor ?? this.departmentColor,
      managerId: managerId ?? this.managerId,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      team: team ?? this.team,
      level: level ?? this.level,
      joinDate: joinDate ?? this.joinDate,
      profileImage: profileImage ?? this.profileImage,
      isExpanded: isExpanded ?? this.isExpanded,
      children: children ?? this.children,
    );
  }
}

@immutable
class EmployeeNode {
  final String id;
  final String name;
  final String email;
  final String position;
  final String department;
  final Color departmentColor;
  final List<EmployeeNode> children;
  final bool isExpanded;
  final String? managerId;
  final String? phone;
  final String? location;
  final String? team;
  final String? level;
  final DateTime? joinDate;
  final String? profileImage;
  final EmployeeData originalEmployee;

  const EmployeeNode({
    required this.id,
    required this.name,
    required this.email,
    required this.position,
    required this.department,
    this.departmentColor = Colors.grey,
    this.children = const [],
    this.isExpanded = true,
    this.managerId,
    this.phone,
    this.location,
    this.team,
    this.level,
    this.joinDate,
    this.profileImage,
    required this.originalEmployee,
  });

  EmployeeNode copyWith({
    String? id,
    String? name,
    String? email,
    String? position,
    String? department,
    Color? departmentColor,
    List<EmployeeNode>? children,
    bool? isExpanded,
    String? managerId,
    String? phone,
    String? location,
    String? team,
    String? level,
    DateTime? joinDate,
    String? profileImage,
    EmployeeData? originalEmployee,
  }) {
    return EmployeeNode(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      position: position ?? this.position,
      department: department ?? this.department,
      departmentColor: departmentColor ?? this.departmentColor,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      managerId: managerId ?? this.managerId,
      phone: phone ?? this.phone,
      location: location ?? this.location,
      team: team ?? this.team,
      level: level ?? this.level,
      joinDate: joinDate ?? this.joinDate,
      profileImage: profileImage ?? this.profileImage,
      originalEmployee: originalEmployee ?? this.originalEmployee,
    );
  }

  factory EmployeeNode.fromEmployee(EmployeeData employee) {
    // Convert children recursively
    final childNodes = employee.children.map((child) => EmployeeNode.fromEmployee(child)).toList();
    
    return EmployeeNode(
      id: employee.id,
      name: employee.name,
      email: employee.email,
      position: employee.position,
      department: employee.department,
      departmentColor: _parseColor(employee.departmentColor) ?? getDepartmentColor(employee.department),
      children: childNodes,
      isExpanded: employee.isExpanded,
      managerId: employee.managerId,
      phone: employee.phone,
      location: employee.location,
      team: employee.team,
      level: employee.level,
      joinDate: employee.joinDate,
      profileImage: employee.profileImage,
      originalEmployee: employee,
    );
  }

  // Parse hex color string to Color object
  static Color? _parseColor(String? hexColor) {
    if (hexColor == null || hexColor.isEmpty) return null;
    
    try {
      // Remove # if present and ensure it's 8 characters (ARGB)
      String cleanHex = hexColor.replaceAll('#', '');
      if (cleanHex.length == 6) {
        cleanHex = 'ff$cleanHex'; // Add alpha if missing
      }
      if (cleanHex.length == 8) {
        return Color(int.parse(cleanHex, radix: 16));
      }
    } catch (e) {
      print('Error parsing color: $hexColor - $e');
    }
    return null;
  }

  static Color getDepartmentColor(String department) {
    switch (department.toLowerCase()) {
      case 'engineering':
      case 'development':
      case 'tech':
        return Colors.blue;
      case 'design':
      case 'ui':
      case 'ux':
        return Colors.orange;
      case 'marketing':
        return Colors.green;
      case 'sales':
        return Colors.purple;
      case 'hr':
      case 'human resources':
        return Colors.pink;
      case 'finance':
        return Colors.teal;
      case 'operations':
        return Colors.brown;
      case 'leadership':
      case 'management':
      case 'executive':
        return Colors.indigo;
      case 'product':
        return Colors.deepOrange;
      case 'qa':
      case 'quality':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }
}

class EmployeeHierarchyBuilder {
  // New method to build hierarchy directly from nested JSON structure
  static EmployeeNode? buildHierarchyFromNestedData(EmployeeData rootEmployee) {
    return EmployeeNode.fromEmployee(rootEmployee);
  }

  // Legacy method for flat array structure (kept for backward compatibility)
  static EmployeeNode? buildHierarchy(List<EmployeeData> employees) {
    if (employees.isEmpty) return null;

    // If there's only one employee, return it as a single node
    if (employees.length == 1) {
      return EmployeeNode.fromEmployee(employees.first);
    }

    // Convert employees to employee nodes
    Map<String, EmployeeNode> nodeMap = {};
    for (var employee in employees) {
      nodeMap[employee.id] = EmployeeNode.fromEmployee(employee);
    }

    // Find root nodes and build relationships
    List<EmployeeNode> rootCandidates = [];
    Map<String, List<EmployeeNode>> childrenMap = {};

    // Initialize children map
    for (var node in nodeMap.values) {
      childrenMap[node.id] = [];
    }

    // Build parent-child relationships
    Set<String> nodesWithManagers = {};
    
    for (var node in nodeMap.values) {
      print('🌳 Processing ${node.name}: managerId="${node.managerId}"');
      
      if (node.managerId != null && 
          node.managerId!.isNotEmpty && 
          node.managerId!.toLowerCase() != 'null') {
        // Check if the manager exists in our node map
        if (nodeMap.containsKey(node.managerId!)) {
          childrenMap[node.managerId!]!.add(node);
          nodesWithManagers.add(node.id);
          print('🌳 ✅ ${node.name} reports to ${nodeMap[node.managerId!]!.name}');
        } else {
          print('🌳 ${node.name} reports to ${node.managerId} (not in system)');
        }
      } else {
        print('🌳 ${node.name} has no manager relationship');
      }
    }
    
    // Add all nodes that don't have managers as root candidates
    for (var node in nodeMap.values) {
      if (!nodesWithManagers.contains(node.id)) {
        rootCandidates.add(node);
        print('🌳 Adding as root candidate: ${node.name}');
      }
    }

    // Build the tree recursively
    EmployeeNode buildNodeWithChildren(EmployeeNode node) {
      var children = childrenMap[node.id] ?? [];
      var childNodes = children.map((child) => buildNodeWithChildren(child)).toList();
      return node.copyWith(children: childNodes);
    }

    // If we have multiple root candidates, create a virtual root
    if (rootCandidates.length > 1) {
      // Create a virtual organization root
      var virtualRoot = EmployeeNode(
        id: 'org_root',
        name: 'Organization',
        email: '<EMAIL>',
        position: 'Organization',
        department: 'Leadership',
        departmentColor: Colors.indigo,
        children: [],
        originalEmployee: EmployeeData(
          id: 'org_root',
          name: 'Organization',
          email: '<EMAIL>',
          position: 'Organization',
          department: 'Leadership',
        ),
      );
      
      // Build all root candidates with their children
      var rootNodesWithChildren = rootCandidates.map((root) => buildNodeWithChildren(root)).toList();
      
      return virtualRoot.copyWith(children: rootNodesWithChildren);
    }

    // If we have exactly one root, build its tree
    if (rootCandidates.length == 1) {
      return buildNodeWithChildren(rootCandidates.first);
    }

    // Fallback: if no roots found, try to find the most senior employee
    var seniorNode = nodeMap.values.firstWhere(
      (n) => n.position.toLowerCase().contains('ceo') ||
             n.position.toLowerCase().contains('director') ||
             n.position.toLowerCase().contains('manager') ||
             n.position.toLowerCase().contains('lead') ||
             n.level == '1',
      orElse: () => nodeMap.values.first,
    );

    return buildNodeWithChildren(seniorNode);
  }

  static EmployeeNode? buildFilteredHierarchy(List<EmployeeData> employees, String rootEmployeeId) {
    final rootEmployee = employees.where((emp) => emp.id == rootEmployeeId).firstOrNull;
    if (rootEmployee == null) return null;

    // Find all subordinates of the root employee
    List<EmployeeData> filteredEmployees = [rootEmployee];
    _addSubordinates(employees, rootEmployeeId, filteredEmployees);

    return buildHierarchy(filteredEmployees);
  }

  static void _addSubordinates(List<EmployeeData> allEmployees, String managerId, List<EmployeeData> result) {
    final subordinates = allEmployees.where((emp) => emp.managerId == managerId).toList();
    for (var subordinate in subordinates) {
      if (!result.any((emp) => emp.id == subordinate.id)) {
        result.add(subordinate);
        _addSubordinates(allEmployees, subordinate.id, result);
      }
    }
  }
}
