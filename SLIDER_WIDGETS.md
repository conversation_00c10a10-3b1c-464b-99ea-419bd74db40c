# Slider Widgets

This document describes the slider widgets available in the project, specifically designed to match your UI preferences with pink/red thumb styling.

## Available Slider Widgets

### 1. SimplePinkSlider

A clean, minimal slider widget that matches the provided UI image with a pink/red thumb on a gray track.

**Location**: `lib/widgets/simple_pink_slider.dart`

**Features**:
- Clean pink/red thumb styling
- Customizable colors and sizes
- Minimal design matching your UI image
- No value indicators or tick marks for clean appearance

**Usage**:
```dart
SimplePinkSlider(
  value: _sliderValue,
  min: 0.0,
  max: 100.0,
  onChanged: (value) {
    setState(() {
      _sliderValue = value;
    });
  },
)
```

**Customization Options**:
```dart
SimplePinkSlider(
  value: _sliderValue,
  min: 0.0,
  max: 100.0,
  thumbColor: Colors.red,                    // Custom thumb color
  activeTrackColor: Colors.red,              // Custom active track color
  inactiveTrackColor: Colors.grey.shade300,  // Custom inactive track color
  trackHeight: 4.0,                          // Track thickness
  thumbRadius: 10.0,                         // Thumb size
  onChanged: (value) {
    setState(() {
      _sliderValue = value;
    });
  },
)
```

### 2. InputSliderWidget (Enhanced)

The existing InputSliderWidget has been updated with improved pink/red thumb styling by default.

**Location**: `ui_controls_library/ui_controls_library/lib/widgets/input_slider_widget.dart`

**Features**:
- Combines slider with text input field
- Increment/decrement buttons
- JSON configuration support
- Animation support
- Extensive customization options
- Now defaults to pink/red thumb styling

**Usage**:
```dart
InputSliderWidget(
  min: 0.0,
  max: 100.0,
  initial: 25.0,
  activeColor: const Color(0xFFE91E63),  // Pink/red
  thumbColor: const Color(0xFFE91E63),   // Pink/red thumb
  showButtons: false,                     // Hide +/- buttons for clean look
  showValueIndicator: false,              // Hide value indicator
  onChanged: (value) {
    // Handle value change
  },
)
```

## Demo Applications

### 1. Simple Slider Demo

**File**: `lib/main_slider_demo.dart`

Run this demo to see both slider widgets in action:

```bash
flutter run lib/main_slider_demo.dart
```

This demo includes:
- Navigation between different slider implementations
- Live preview of the SimplePinkSlider
- Interactive examples

### 2. Detailed Examples

**File**: `lib/examples/simple_slider_demo.dart`

Contains detailed examples showing:
- InputSliderWidget with pink/red styling
- Comparison with standard Flutter Slider
- Usage instructions and best practices

## Color Scheme

The sliders use the following default color scheme to match your preferences:

- **Thumb Color**: `Color(0xFFE91E63)` (Pink/Red)
- **Active Track**: `Color(0xFFE91E63)` (Pink/Red)
- **Inactive Track**: `Colors.grey.shade300` (Light Gray)
- **Overlay**: Pink/Red with 50% opacity

## Customization

Both slider widgets support extensive customization:

### Colors
- `thumbColor`: Color of the slider thumb
- `activeTrackColor`: Color of the active portion of the track
- `inactiveTrackColor`: Color of the inactive portion of the track
- `overlayColor`: Color when the slider is pressed

### Dimensions
- `trackHeight`: Thickness of the slider track
- `thumbRadius`: Size of the slider thumb
- `overlayRadius`: Size of the press overlay

### Behavior
- `min`/`max`: Value range
- `onChanged`: Callback for value changes
- `onChangeEnd`: Callback when sliding ends

## Best Practices

1. **Use SimplePinkSlider** for clean, minimal slider interfaces
2. **Use InputSliderWidget** when you need text input or advanced features
3. **Customize colors** to match your app's theme while maintaining the pink/red preference
4. **Set appropriate min/max values** for your use case
5. **Handle state management** properly in your parent widgets

## Integration

To use these widgets in your app:

1. Import the widget:
```dart
import 'package:your_app/widgets/simple_pink_slider.dart';
// or
import 'package:ui_controls_library/widgets/input_slider_widget.dart';
```

2. Add to your widget tree:
```dart
SimplePinkSlider(
  value: _value,
  onChanged: (newValue) => setState(() => _value = newValue),
)
```

3. Manage state in your parent widget using `setState()` or your preferred state management solution.
