// To parse this JSON data, do
//
//     final booksModel = booksModelFromJson(jsonString);

import 'dart:convert';

List<BooksModel> booksModelFromJson(String str) => List<BooksModel>.from(json.decode(str).map((x) => BooksModel.fromJson(x)));

String booksModelToJson(List<BooksModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BooksModel {
    String? bookId;
    String? bookName;
    int? objectiveCount;
    String? image;

    BooksModel({
        this.bookId,
        this.bookName,
        this.objectiveCount,
        this.image,
    });

    BooksModel copyWith({
        String? bookId,
        String? bookName,
        int? objectiveCount,
        String? image,
    }) => 
        BooksModel(
            bookId: bookId ?? this.bookId,
            bookName: bookName ?? this.bookName,
            objectiveCount: objectiveCount ?? this.objectiveCount,
            image: image ?? this.image,
        );

    factory BooksModel.fromJson(Map<String, dynamic> json) => BooksModel(
        bookId: json["book_id"],
        bookName: json["book_name"],
        objectiveCount: json["objective_count"],
        image: json["image"],
    );

    Map<String, dynamic> toJson() => {
        "book_id": bookId,
        "book_name": bookName,
        "objective_count": objectiveCount,
        "image": image,
    };
}
