import 'dart:math';
import 'package:flutter/material.dart';

// Custom painter for vertical dotted lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      // Draw a small dash
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      // Move past the dash and gap
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(DottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

// Custom painter for horizontal dotted lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    required this.dashLength,
    required this.dashGap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      // Draw a small dash
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      // Move past the dash and gap
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(HorizontalDottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashLength != dashLength ||
        oldDelegate.dashGap != dashGap;
  }
}

class WorkflowTreeBuilder {
  // Build the workflow tree visualization
  static Widget buildWorkflowTree(List<dynamic> treeData) {
    // Filter top-level steps (level 0)
    final topLevelSteps = treeData.where((step) => step['level'] == 0).toList();

    // Sort top-level steps by sequence if available
    topLevelSteps.sort((a, b) =>
      (a['sequence'] ?? 0).compareTo(b['sequence'] ?? 0)
    );

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First row - display first three top level steps (horizontal) without borders
          if (topLevelSteps.length >= 3)
            Padding(
              padding: EdgeInsets.only(bottom: 24),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    for (int i = 0; i < min(3, topLevelSteps.length); i++) ...[
                      if (i > 0)
                        Text(
                          "  ",
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: 'TiemposText',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      Text(
                        topLevelSteps[i]['text'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

          // Main workflow visualization
          _buildWorkflowVisualization(treeData),
        ],
      ),
    );
  }

  // Build the workflow visualization
  static Widget _buildWorkflowVisualization(List<dynamic> treeData) {
    // Create a map of nodes by level for easier access
    Map<int, List<Map<String, dynamic>>> nodesByLevel = {};

    // Convert all nodes to Map<String, dynamic> for easier handling
    for (var node in treeData) {
      final level = node['level'] ?? 0;
      if (!nodesByLevel.containsKey(level)) {
        nodesByLevel[level] = [];
      }
      nodesByLevel[level]!.add(Map<String, dynamic>.from(node));
    }

    // Get the main workflow path (level 0 nodes starting from index 3)
    final mainPathNodes = nodesByLevel[0]?.where((node) =>
      (node['sequence'] ?? 0) >= 3
    ).toList() ?? [];

    // Sort by sequence
    mainPathNodes.sort((a, b) =>
      (a['sequence'] ?? 0).compareTo(b['sequence'] ?? 0)
    );

    return Stack(
      children: [
        // Main vertical connector line
        Positioned(
          left: 3,
          top: 15,
          bottom: 0,
          child: CustomPaint(
            size: Size(1, double.infinity),
            painter: DottedLinePainter(
              color: Colors.grey.shade400,
              dashLength: 3,
              dashGap: 3,
            ),
          ),
        ),

        // Main workflow content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Build each main path node
            for (int i = 0; i < mainPathNodes.length; i++) ...[
              if (i > 0) SizedBox(height: 16),

              // Node row
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Circle indicator
                  Container(
                    margin: EdgeInsets.only(right: 8),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade400, width: 0.5),
                      color: Colors.white,
                    ),
                  ),

                  // Node text
                  Text(
                    mainPathNodes[i]['text'] ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),

                  // X icon for rejection nodes
                  if (mainPathNodes[i]['isRejection'] == true || mainPathNodes[i]['hasX'] == true)
                    Padding(
                      padding: EdgeInsets.only(left: 8),
                      child: Container(
                        width: 15,
                        height: 15,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          shape: BoxShape.rectangle,
                          color: Colors.red.shade50,
                          border: Border.all(color: Color(0xffFF0000)),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 12,
                          color: Color(0xffFF0000),
                        ),
                      ),
                    ),
                ],
              ),

              // Build alternative paths for this node
              if (mainPathNodes[i].containsKey('children') &&
                  mainPathNodes[i]['children'] != null &&
                  (mainPathNodes[i]['children'] as List).isNotEmpty) ...[
                SizedBox(height: 16),
                _buildAlternativePaths(mainPathNodes[i]['children'] as List),
              ],
            ],
          ],
        ),
      ],
    );
  }

  // Build alternative paths
  static Widget _buildAlternativePaths(List<dynamic> children) {
    // Convert children to Map<String, dynamic> for easier handling
    final List<Map<String, dynamic>> childNodes = children
        .map((child) => Map<String, dynamic>.from(child))
        .toList();

    // Sort by sequence if available
    childNodes.sort((a, b) =>
      (a['sequence'] ?? 0).compareTo(b['sequence'] ?? 0)
    );

    return Padding(
      padding: EdgeInsets.only(left: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < childNodes.length; i++) ...[
            if (i > 0) SizedBox(height: 16),

            // Alternative path node
            Stack(
              children: [
                // Vertical connector for this alternative path
                if (childNodes[i].containsKey('children') &&
                    childNodes[i]['children'] != null &&
                    (childNodes[i]['children'] as List).isNotEmpty)
                  Positioned(
                    left: 47,
                    top: 16,
                    bottom: 0,
                    child: CustomPaint(
                      size: Size(1, double.infinity),
                      painter: DottedLinePainter(
                        color: Colors.grey.shade400,
                        dashLength: 3,
                        dashGap: 3,
                      ),
                    ),
                  ),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Alternative path row
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Alt label, horizontal connector, and circle
                        Stack(
                          children: [
                            // Horizontal dotted connector
                            Positioned(
                              left: 3,
                              top: 11,
                              width: 12,
                              height: 1,
                              child: CustomPaint(
                                size: Size(44, 1),
                                painter: HorizontalDottedLinePainter(
                                  color: Colors.grey.shade400,
                                  dashLength: 3,
                                  dashGap: 3,
                                ),
                              ),
                            ),

                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(left: 15, top: 4),
                                  width: 44,
                                  child: Text(
                                    "Alt. ${i + 1}",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ),

                                // Circle indicator
                                Container(
                                  margin: EdgeInsets.only(right: 8, top: 3),
                                  width: 6,
                                  height: 6,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.grey.shade400,
                                      width: 0.5
                                    ),
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Node text
                        Text(
                          childNodes[i]['text'] ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                            color: Colors.black,
                          ),
                        ),

                        // X icon for rejection nodes
                        if (childNodes[i]['isRejection'] == true || childNodes[i]['hasX'] == true)
                          Padding(
                            padding: EdgeInsets.only(left: 8),
                            child: Container(
                              width: 15,
                              height: 15,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                shape: BoxShape.rectangle,
                                color: Colors.red.shade50,
                                border: Border.all(color: Color(0xffFF0000)),
                              ),
                              child: Icon(
                                Icons.close,
                                size: 12,
                                color: Color(0xffFF0000),
                              ),
                            ),
                          ),
                      ],
                    ),

                    // Nested children
                    if (childNodes[i].containsKey('children') &&
                        childNodes[i]['children'] != null &&
                        (childNodes[i]['children'] as List).isNotEmpty) ...[
                      SizedBox(height: 16),
                      _buildNestedChildren(childNodes[i]['children'] as List),
                    ],
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Build nested children
  static Widget _buildNestedChildren(List<dynamic> children) {
    // Convert children to Map<String, dynamic> for easier handling
    final List<Map<String, dynamic>> childNodes = children
        .map((child) => Map<String, dynamic>.from(child))
        .toList();

    // Sort by sequence if available
    childNodes.sort((a, b) =>
      (a['sequence'] ?? 0).compareTo(b['sequence'] ?? 0)
    );

    return Padding(
      padding: EdgeInsets.only(left: 44),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (int i = 0; i < childNodes.length; i++) ...[
            if (i > 0) SizedBox(height: 16),

            // Nested node row
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Horizontal connector and circle
                Stack(
                  children: [
                    // Horizontal dotted connector
                    Positioned(
                      left: -46,
                      top: 8,
                      width: 55,
                      height: 1,
                      child: CustomPaint(
                        size: Size(44, 1),
                        painter: HorizontalDottedLinePainter(
                          color: Colors.grey.shade400,
                          dashLength: 3,
                          dashGap: 3,
                        ),
                      ),
                    ),

                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 16),
                          child: Text(
                            'Alt. ${i + 1}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                        SizedBox(width: 10),

                        // Circle indicator
                        Container(
                          margin: EdgeInsets.only(right: 8),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.grey.shade400,
                              width: 0.5
                            ),
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                // Node text
                Text(
                  childNodes[i]['text'] ?? '',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),

                // X icon for rejection nodes
                if (childNodes[i]['isRejection'] == true || childNodes[i]['hasX'] == true)
                  Padding(
                    padding: EdgeInsets.only(left: 8, right: 8),
                    child: Container(
                      width: 15,
                      height: 15,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        shape: BoxShape.rectangle,
                        color: Colors.red.shade50,
                        border: Border.all(color: Color(0xffFF0000)),
                      ),
                      child: Icon(
                        Icons.close,
                        size: 12,
                        color: Color(0xffFF0000),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}