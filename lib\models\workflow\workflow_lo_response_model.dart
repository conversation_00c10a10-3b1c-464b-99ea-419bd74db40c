// To parse this JSON data, do
//
//     final workFlowLoResponseModel = workFlowLoResponseModelFromJson(jsonString);

import 'dart:convert';

WorkFlowLoResponseModel workFlowLoResponseModelFromJson(String str) =>
    WorkFlowLoResponseModel.fromJson(json.decode(str));

String workFlowLoResponseModelToJson(WorkFlowLoResponseModel data) =>
    json.encode(data.toJson());

class WorkFlowLoResponseModel {
  bool? success;
  List<String>? messages;
  ParsedData? parsedData;
  dynamic validationErrors;
  ParsedGos? parsedGos;
  ParsedLos? parsedLos;

  WorkFlowLoResponseModel({
    this.success,
    this.messages,
    this.parsedData,
    this.validationErrors,
    this.parsedGos,
    this.parsedLos,
  });

  WorkFlowLoResponseModel copyWith({
    bool? success,
    List<String>? messages,
    ParsedData? parsedData,
    dynamic validationErrors,
    ParsedGos? parsedGos,
    ParsedLos? parsedLos,
  }) =>
      WorkFlowLoResponseModel(
        success: success ?? this.success,
        messages: messages ?? this.messages,
        parsedData: parsedData ?? this.parsedData,
        validationErrors: validationErrors ?? this.validationErrors,
        parsedGos: parsedGos ?? this.parsedGos,
        parsedLos: parsedLos ?? this.parsedLos,
      );

  factory WorkFlowLoResponseModel.fromJson(Map<String, dynamic> json) =>
      WorkFlowLoResponseModel(
        success: json["success"],
        messages: json["messages"] == null
            ? []
            : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"],
        parsedGos: json["parsed_gos"] == null
            ? null
            : ParsedGos.fromJson(json["parsed_gos"]),
        parsedLos: json["parsed_los"] == null
            ? null
            : ParsedLos.fromJson(json["parsed_los"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "messages":
            messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors,
        "parsed_gos": parsedGos?.toJson(),
        "parsed_los": parsedLos?.toJson(),
      };
}

class ParsedData {
  ParsedLos? localObjectives;
  LoPutStack? loInputStack;
  List<LoInputItem>? loInputItems;
  LoPutStack? loOutputStack;
  List<LoOutputItem>? loOutputItems;
  List<LoEntityValidation>? loEntityValidations;
  LoUiStack? loUiStack;
  List<LoUiEntityAttributeStack>? loUiEntityAttributeStack;
  LoDataMappingStack? loDataMappingStack;
  List<dynamic>? loDataMappings;
  List<LoNestedFunction>? loNestedFunctions;
  List<dynamic>? executionVariance;
  List<LoExecutionDatum>? loExecutionData;
  List<dynamic>? loInputExecution;
  List<dynamic>? terminalPathways;
  List<dynamic>? loAttributeMappings;
  List<dynamic>? loEntityMappings;
  List<LoPutMapping>? loInputMappings;
  List<dynamic>? loInputValidations;
  List<LoPutMapping>? loOutputMappings;
  List<dynamic>? loNestedFunctionInputItems;
  List<dynamic>? loNestedFunctionInputStacks;
  List<dynamic>? loNestedFunctionMappings;
  List<dynamic>? loNestedFunctionOutputItems;
  List<dynamic>? loNestedFunctionOutputStacks;

  ParsedData({
    this.localObjectives,
    this.loInputStack,
    this.loInputItems,
    this.loOutputStack,
    this.loOutputItems,
    this.loEntityValidations,
    this.loUiStack,
    this.loUiEntityAttributeStack,
    this.loDataMappingStack,
    this.loDataMappings,
    this.loNestedFunctions,
    this.executionVariance,
    this.loExecutionData,
    this.loInputExecution,
    this.terminalPathways,
    this.loAttributeMappings,
    this.loEntityMappings,
    this.loInputMappings,
    this.loInputValidations,
    this.loOutputMappings,
    this.loNestedFunctionInputItems,
    this.loNestedFunctionInputStacks,
    this.loNestedFunctionMappings,
    this.loNestedFunctionOutputItems,
    this.loNestedFunctionOutputStacks,
  });

  ParsedData copyWith({
    ParsedLos? localObjectives,
    LoPutStack? loInputStack,
    List<LoInputItem>? loInputItems,
    LoPutStack? loOutputStack,
    List<LoOutputItem>? loOutputItems,
    List<LoEntityValidation>? loEntityValidations,
    LoUiStack? loUiStack,
    List<LoUiEntityAttributeStack>? loUiEntityAttributeStack,
    LoDataMappingStack? loDataMappingStack,
    List<dynamic>? loDataMappings,
    List<LoNestedFunction>? loNestedFunctions,
    List<dynamic>? executionVariance,
    List<LoExecutionDatum>? loExecutionData,
    List<dynamic>? loInputExecution,
    List<dynamic>? terminalPathways,
    List<dynamic>? loAttributeMappings,
    List<dynamic>? loEntityMappings,
    List<LoPutMapping>? loInputMappings,
    List<dynamic>? loInputValidations,
    List<LoPutMapping>? loOutputMappings,
    List<dynamic>? loNestedFunctionInputItems,
    List<dynamic>? loNestedFunctionInputStacks,
    List<dynamic>? loNestedFunctionMappings,
    List<dynamic>? loNestedFunctionOutputItems,
    List<dynamic>? loNestedFunctionOutputStacks,
  }) =>
      ParsedData(
        localObjectives: localObjectives ?? this.localObjectives,
        loInputStack: loInputStack ?? this.loInputStack,
        loInputItems: loInputItems ?? this.loInputItems,
        loOutputStack: loOutputStack ?? this.loOutputStack,
        loOutputItems: loOutputItems ?? this.loOutputItems,
        loEntityValidations: loEntityValidations ?? this.loEntityValidations,
        loUiStack: loUiStack ?? this.loUiStack,
        loUiEntityAttributeStack:
            loUiEntityAttributeStack ?? this.loUiEntityAttributeStack,
        loDataMappingStack: loDataMappingStack ?? this.loDataMappingStack,
        loDataMappings: loDataMappings ?? this.loDataMappings,
        loNestedFunctions: loNestedFunctions ?? this.loNestedFunctions,
        executionVariance: executionVariance ?? this.executionVariance,
        loExecutionData: loExecutionData ?? this.loExecutionData,
        loInputExecution: loInputExecution ?? this.loInputExecution,
        terminalPathways: terminalPathways ?? this.terminalPathways,
        loAttributeMappings: loAttributeMappings ?? this.loAttributeMappings,
        loEntityMappings: loEntityMappings ?? this.loEntityMappings,
        loInputMappings: loInputMappings ?? this.loInputMappings,
        loInputValidations: loInputValidations ?? this.loInputValidations,
        loOutputMappings: loOutputMappings ?? this.loOutputMappings,
        loNestedFunctionInputItems:
            loNestedFunctionInputItems ?? this.loNestedFunctionInputItems,
        loNestedFunctionInputStacks:
            loNestedFunctionInputStacks ?? this.loNestedFunctionInputStacks,
        loNestedFunctionMappings:
            loNestedFunctionMappings ?? this.loNestedFunctionMappings,
        loNestedFunctionOutputItems:
            loNestedFunctionOutputItems ?? this.loNestedFunctionOutputItems,
        loNestedFunctionOutputStacks:
            loNestedFunctionOutputStacks ?? this.loNestedFunctionOutputStacks,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        localObjectives: json["local_objectives"] == null
            ? null
            : ParsedLos.fromJson(json["local_objectives"]),
        loInputStack: json["lo_input_stack"] == null
            ? null
            : LoPutStack.fromJson(json["lo_input_stack"]),
        loInputItems: json["lo_input_items"] == null
            ? []
            : List<LoInputItem>.from(
                json["lo_input_items"]!.map((x) => LoInputItem.fromJson(x))),
        loOutputStack: json["lo_output_stack"] == null
            ? null
            : LoPutStack.fromJson(json["lo_output_stack"]),
        loOutputItems: json["lo_output_items"] == null
            ? []
            : List<LoOutputItem>.from(
                json["lo_output_items"]!.map((x) => LoOutputItem.fromJson(x))),
        loEntityValidations: json["lo_entity_validations"] == null
            ? []
            : List<LoEntityValidation>.from(json["lo_entity_validations"]!
                .map((x) => LoEntityValidation.fromJson(x))),
        loUiStack: json["lo_ui_stack"] == null
            ? null
            : LoUiStack.fromJson(json["lo_ui_stack"]),
        loUiEntityAttributeStack: json["lo_ui_entity_attribute_stack"] == null
            ? []
            : List<LoUiEntityAttributeStack>.from(
                json["lo_ui_entity_attribute_stack"]!
                    .map((x) => LoUiEntityAttributeStack.fromJson(x))),
        loDataMappingStack: json["lo_data_mapping_stack"] == null
            ? null
            : LoDataMappingStack.fromJson(json["lo_data_mapping_stack"]),
        loDataMappings: json["lo_data_mappings"] == null
            ? []
            : List<dynamic>.from(json["lo_data_mappings"]!.map((x) => x)),
        loNestedFunctions: json["lo_nested_functions"] == null
            ? []
            : List<LoNestedFunction>.from(json["lo_nested_functions"]!
                .map((x) => LoNestedFunction.fromJson(x))),
        executionVariance: json["execution_variance"] == null
            ? []
            : List<dynamic>.from(json["execution_variance"]!.map((x) => x)),
        loExecutionData: json["lo_execution_data"] == null
            ? []
            : List<LoExecutionDatum>.from(json["lo_execution_data"]!
                .map((x) => LoExecutionDatum.fromJson(x))),
        loInputExecution: json["lo_input_execution"] == null
            ? []
            : List<dynamic>.from(json["lo_input_execution"]!.map((x) => x)),
        terminalPathways: json["terminal_pathways"] == null
            ? []
            : List<dynamic>.from(json["terminal_pathways"]!.map((x) => x)),
        loAttributeMappings: json["lo_attribute_mappings"] == null
            ? []
            : List<dynamic>.from(json["lo_attribute_mappings"]!.map((x) => x)),
        loEntityMappings: json["lo_entity_mappings"] == null
            ? []
            : List<dynamic>.from(json["lo_entity_mappings"]!.map((x) => x)),
        loInputMappings: json["lo_input_mappings"] == null
            ? []
            : List<LoPutMapping>.from(json["lo_input_mappings"]!
                .map((x) => LoPutMapping.fromJson(x))),
        loInputValidations: json["lo_input_validations"] == null
            ? []
            : List<dynamic>.from(json["lo_input_validations"]!.map((x) => x)),
        loOutputMappings: json["lo_output_mappings"] == null
            ? []
            : List<LoPutMapping>.from(json["lo_output_mappings"]!
                .map((x) => LoPutMapping.fromJson(x))),
        loNestedFunctionInputItems:
            json["lo_nested_function_input_items"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_input_items"]!.map((x) => x)),
        loNestedFunctionInputStacks:
            json["lo_nested_function_input_stacks"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_input_stacks"]!.map((x) => x)),
        loNestedFunctionMappings: json["lo_nested_function_mappings"] == null
            ? []
            : List<dynamic>.from(
                json["lo_nested_function_mappings"]!.map((x) => x)),
        loNestedFunctionOutputItems:
            json["lo_nested_function_output_items"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_output_items"]!.map((x) => x)),
        loNestedFunctionOutputStacks:
            json["lo_nested_function_output_stacks"] == null
                ? []
                : List<dynamic>.from(
                    json["lo_nested_function_output_stacks"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "local_objectives": localObjectives?.toJson(),
        "lo_input_stack": loInputStack?.toJson(),
        "lo_input_items": loInputItems == null
            ? []
            : List<dynamic>.from(loInputItems!.map((x) => x.toJson())),
        "lo_output_stack": loOutputStack?.toJson(),
        "lo_output_items": loOutputItems == null
            ? []
            : List<dynamic>.from(loOutputItems!.map((x) => x.toJson())),
        "lo_entity_validations": loEntityValidations == null
            ? []
            : List<dynamic>.from(loEntityValidations!.map((x) => x.toJson())),
        "lo_ui_stack": loUiStack?.toJson(),
        "lo_ui_entity_attribute_stack": loUiEntityAttributeStack == null
            ? []
            : List<dynamic>.from(
                loUiEntityAttributeStack!.map((x) => x.toJson())),
        "lo_data_mapping_stack": loDataMappingStack?.toJson(),
        "lo_data_mappings": loDataMappings == null
            ? []
            : List<dynamic>.from(loDataMappings!.map((x) => x)),
        "lo_nested_functions": loNestedFunctions == null
            ? []
            : List<dynamic>.from(loNestedFunctions!.map((x) => x.toJson())),
        "execution_variance": executionVariance == null
            ? []
            : List<dynamic>.from(executionVariance!.map((x) => x)),
        "lo_execution_data": loExecutionData == null
            ? []
            : List<dynamic>.from(loExecutionData!.map((x) => x.toJson())),
        "lo_input_execution": loInputExecution == null
            ? []
            : List<dynamic>.from(loInputExecution!.map((x) => x)),
        "terminal_pathways": terminalPathways == null
            ? []
            : List<dynamic>.from(terminalPathways!.map((x) => x)),
        "lo_attribute_mappings": loAttributeMappings == null
            ? []
            : List<dynamic>.from(loAttributeMappings!.map((x) => x)),
        "lo_entity_mappings": loEntityMappings == null
            ? []
            : List<dynamic>.from(loEntityMappings!.map((x) => x)),
        "lo_input_mappings": loInputMappings == null
            ? []
            : List<dynamic>.from(loInputMappings!.map((x) => x.toJson())),
        "lo_input_validations": loInputValidations == null
            ? []
            : List<dynamic>.from(loInputValidations!.map((x) => x)),
        "lo_output_mappings": loOutputMappings == null
            ? []
            : List<dynamic>.from(loOutputMappings!.map((x) => x.toJson())),
        "lo_nested_function_input_items": loNestedFunctionInputItems == null
            ? []
            : List<dynamic>.from(loNestedFunctionInputItems!.map((x) => x)),
        "lo_nested_function_input_stacks": loNestedFunctionInputStacks == null
            ? []
            : List<dynamic>.from(loNestedFunctionInputStacks!.map((x) => x)),
        "lo_nested_function_mappings": loNestedFunctionMappings == null
            ? []
            : List<dynamic>.from(loNestedFunctionMappings!.map((x) => x)),
        "lo_nested_function_output_items": loNestedFunctionOutputItems == null
            ? []
            : List<dynamic>.from(loNestedFunctionOutputItems!.map((x) => x)),
        "lo_nested_function_output_stacks": loNestedFunctionOutputStacks == null
            ? []
            : List<dynamic>.from(loNestedFunctionOutputStacks!.map((x) => x)),
      };
}

class LoDataMappingStack {
  String? id;
  String? loDataMappingStackId;
  String? loId;
  String? description;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedBy;
  dynamic updatedAt;
  String? naturalLanguage;

  LoDataMappingStack({
    this.id,
    this.loDataMappingStackId,
    this.loId,
    this.description,
    this.createdAt,
    this.createdBy,
    this.updatedBy,
    this.updatedAt,
    this.naturalLanguage,
  });

  LoDataMappingStack copyWith({
    String? id,
    String? loDataMappingStackId,
    String? loId,
    String? description,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedBy,
    dynamic updatedAt,
    String? naturalLanguage,
  }) =>
      LoDataMappingStack(
        id: id ?? this.id,
        loDataMappingStackId: loDataMappingStackId ?? this.loDataMappingStackId,
        loId: loId ?? this.loId,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        updatedAt: updatedAt ?? this.updatedAt,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoDataMappingStack.fromJson(Map<String, dynamic> json) =>
      LoDataMappingStack(
        id: json["id"],
        loDataMappingStackId: json["lo_data_mapping_stack_id"],
        loId: json["lo_id"],
        description: json["description"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        updatedAt: json["updated_at"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_data_mapping_stack_id": loDataMappingStackId,
        "lo_id": loId,
        "description": description,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "updated_at": updatedAt,
        "natural_language": naturalLanguage,
      };
}

class LoEntityValidation {
  String? loEntityValidationId;
  String? loId;
  String? loInputItemsId;
  String? entityId;
  String? attributeId;
  String? validationCondition;
  String? errorMessage;
  String? naturalLanguage;

  LoEntityValidation({
    this.loEntityValidationId,
    this.loId,
    this.loInputItemsId,
    this.entityId,
    this.attributeId,
    this.validationCondition,
    this.errorMessage,
    this.naturalLanguage,
  });

  LoEntityValidation copyWith({
    String? loEntityValidationId,
    String? loId,
    String? loInputItemsId,
    String? entityId,
    String? attributeId,
    String? validationCondition,
    String? errorMessage,
    String? naturalLanguage,
  }) =>
      LoEntityValidation(
        loEntityValidationId: loEntityValidationId ?? this.loEntityValidationId,
        loId: loId ?? this.loId,
        loInputItemsId: loInputItemsId ?? this.loInputItemsId,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        validationCondition: validationCondition ?? this.validationCondition,
        errorMessage: errorMessage ?? this.errorMessage,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoEntityValidation.fromJson(Map<String, dynamic> json) =>
      LoEntityValidation(
        loEntityValidationId: json["lo_entity_validation_id"],
        loId: json["lo_id"],
        loInputItemsId: json["lo_input_items_id"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        validationCondition: json["validation_condition"],
        errorMessage: json["error_message"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "lo_entity_validation_id": loEntityValidationId,
        "lo_id": loId,
        "lo_input_items_id": loInputItemsId,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "validation_condition": validationCondition,
        "error_message": errorMessage,
        "natural_language": naturalLanguage,
      };
}

class LoExecutionDatum {
  String? id;
  String? loId;
  String? dataKey;
  String? dataValue;
  String? dataType;
  String? executionContext;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  String? naturalLanguage;

  LoExecutionDatum({
    this.id,
    this.loId,
    this.dataKey,
    this.dataValue,
    this.dataType,
    this.executionContext,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.naturalLanguage,
  });

  LoExecutionDatum copyWith({
    String? id,
    String? loId,
    String? dataKey,
    String? dataValue,
    String? dataType,
    String? executionContext,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    String? naturalLanguage,
  }) =>
      LoExecutionDatum(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        dataKey: dataKey ?? this.dataKey,
        dataValue: dataValue ?? this.dataValue,
        dataType: dataType ?? this.dataType,
        executionContext: executionContext ?? this.executionContext,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoExecutionDatum.fromJson(Map<String, dynamic> json) =>
      LoExecutionDatum(
        id: json["id"],
        loId: json["lo_id"],
        dataKey: json["data_key"],
        dataValue: json["data_value"],
        dataType: json["data_type"],
        executionContext: json["execution_context"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "data_key": dataKey,
        "data_value": dataValue,
        "data_type": dataType,
        "execution_context": executionContext,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "natural_language": naturalLanguage,
      };
}

class LoInputItem {
  int? id;
  String? itemId;
  String? inputStackId;
  String? slotId;
  String? sourceType;
  String? sourceDescription;
  bool? required;
  String? loId;
  String? dataType;
  dynamic uiControl;
  dynamic nestedFunctionId;
  bool? isVisible;
  String? name;
  String? type;
  dynamic createdAt;
  dynamic updatedAt;
  String? createdBy;
  String? updatedBy;
  bool? readOnly;
  String? agentType;
  bool? dependentAttribute;
  dynamic dependentAttributeValue;
  ParsedGos? enumValues;
  dynamic defaultValue;
  bool? informationField;
  bool? constantField;
  String? entityId;
  String? attributeId;
  String? entityName;
  String? attributeName;
  String? version;
  String? naturalLanguage;

  LoInputItem({
    this.id,
    this.itemId,
    this.inputStackId,
    this.slotId,
    this.sourceType,
    this.sourceDescription,
    this.required,
    this.loId,
    this.dataType,
    this.uiControl,
    this.nestedFunctionId,
    this.isVisible,
    this.name,
    this.type,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.readOnly,
    this.agentType,
    this.dependentAttribute,
    this.dependentAttributeValue,
    this.enumValues,
    this.defaultValue,
    this.informationField,
    this.constantField,
    this.entityId,
    this.attributeId,
    this.entityName,
    this.attributeName,
    this.version,
    this.naturalLanguage,
  });

  LoInputItem copyWith({
    int? id,
    String? itemId,
    String? inputStackId,
    String? slotId,
    String? sourceType,
    String? sourceDescription,
    bool? required,
    String? loId,
    String? dataType,
    dynamic uiControl,
    dynamic nestedFunctionId,
    bool? isVisible,
    String? name,
    String? type,
    dynamic createdAt,
    dynamic updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? readOnly,
    String? agentType,
    bool? dependentAttribute,
    dynamic dependentAttributeValue,
    ParsedGos? enumValues,
    dynamic defaultValue,
    bool? informationField,
    bool? constantField,
    String? entityId,
    String? attributeId,
    String? entityName,
    String? attributeName,
    String? version,
    String? naturalLanguage,
  }) =>
      LoInputItem(
        id: id ?? this.id,
        itemId: itemId ?? this.itemId,
        inputStackId: inputStackId ?? this.inputStackId,
        slotId: slotId ?? this.slotId,
        sourceType: sourceType ?? this.sourceType,
        sourceDescription: sourceDescription ?? this.sourceDescription,
        required: required ?? this.required,
        loId: loId ?? this.loId,
        dataType: dataType ?? this.dataType,
        uiControl: uiControl ?? this.uiControl,
        nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        isVisible: isVisible ?? this.isVisible,
        name: name ?? this.name,
        type: type ?? this.type,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        readOnly: readOnly ?? this.readOnly,
        agentType: agentType ?? this.agentType,
        dependentAttribute: dependentAttribute ?? this.dependentAttribute,
        dependentAttributeValue:
            dependentAttributeValue ?? this.dependentAttributeValue,
        enumValues: enumValues ?? this.enumValues,
        defaultValue: defaultValue ?? this.defaultValue,
        informationField: informationField ?? this.informationField,
        constantField: constantField ?? this.constantField,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
        version: version ?? this.version,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoInputItem.fromJson(Map<String, dynamic> json) => LoInputItem(
        id: json["id"],
        itemId: json["item_id"],
        inputStackId: json["input_stack_id"],
        slotId: json["slot_id"],
        sourceType: json["source_type"],
        sourceDescription: json["source_description"],
        required: json["required"],
        loId: json["lo_id"],
        dataType: json["data_type"],
        uiControl: json["ui_control"],
        nestedFunctionId: json["nested_function_id"],
        isVisible: json["is_visible"],
        name: json["name"],
        type: json["type"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        readOnly: json["read_only"],
        agentType: json["agent_type"],
        dependentAttribute: json["dependent_attribute"],
        dependentAttributeValue: json["dependent_attribute_value"],
        enumValues: json["enum_values"] == null
            ? null
            : ParsedGos.fromJson(json["enum_values"]),
        defaultValue: json["default_value"],
        informationField: json["information_field"],
        constantField: json["constant_field"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        entityName: json["entity_name"],
        attributeName: json["attribute_name"],
        version: json["version"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "item_id": itemId,
        "input_stack_id": inputStackId,
        "slot_id": slotId,
        "source_type": sourceType,
        "source_description": sourceDescription,
        "required": required,
        "lo_id": loId,
        "data_type": dataType,
        "ui_control": uiControl,
        "nested_function_id": nestedFunctionId,
        "is_visible": isVisible,
        "name": name,
        "type": type,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "read_only": readOnly,
        "agent_type": agentType,
        "dependent_attribute": dependentAttribute,
        "dependent_attribute_value": dependentAttributeValue,
        "enum_values": enumValues?.toJson(),
        "default_value": defaultValue,
        "information_field": informationField,
        "constant_field": constantField,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "entity_name": entityName,
        "attribute_name": attributeName,
        "version": version,
        "natural_language": naturalLanguage,
      };
}

class ParsedGos {
  ParsedGos();

  ParsedGos copyWith() => ParsedGos();

  factory ParsedGos.fromJson(Map<String, dynamic> json) => ParsedGos();

  Map<String, dynamic> toJson() => {};
}

class LoPutMapping {
  String? id;
  String? loId;
  String? sourceField;
  String? targetField;
  String? sourceEntity;
  String? targetEntity;
  String? mappingType;
  dynamic validationRule;
  dynamic createdAt;
  dynamic createdBy;
  dynamic updatedAt;
  dynamic updatedBy;
  dynamic transformationRule;

  LoPutMapping({
    this.id,
    this.loId,
    this.sourceField,
    this.targetField,
    this.sourceEntity,
    this.targetEntity,
    this.mappingType,
    this.validationRule,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.transformationRule,
  });

  LoPutMapping copyWith({
    String? id,
    String? loId,
    String? sourceField,
    String? targetField,
    String? sourceEntity,
    String? targetEntity,
    String? mappingType,
    dynamic validationRule,
    dynamic createdAt,
    dynamic createdBy,
    dynamic updatedAt,
    dynamic updatedBy,
    dynamic transformationRule,
  }) =>
      LoPutMapping(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        sourceField: sourceField ?? this.sourceField,
        targetField: targetField ?? this.targetField,
        sourceEntity: sourceEntity ?? this.sourceEntity,
        targetEntity: targetEntity ?? this.targetEntity,
        mappingType: mappingType ?? this.mappingType,
        validationRule: validationRule ?? this.validationRule,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        transformationRule: transformationRule ?? this.transformationRule,
      );

  factory LoPutMapping.fromJson(Map<String, dynamic> json) => LoPutMapping(
        id: json["id"],
        loId: json["lo_id"],
        sourceField: json["source_field"],
        targetField: json["target_field"],
        sourceEntity: json["source_entity"],
        targetEntity: json["target_entity"],
        mappingType: json["mapping_type"],
        validationRule: json["validation_rule"],
        createdAt: json["created_at"],
        createdBy: json["created_by"],
        updatedAt: json["updated_at"],
        updatedBy: json["updated_by"],
        transformationRule: json["transformation_rule"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "source_field": sourceField,
        "target_field": targetField,
        "source_entity": sourceEntity,
        "target_entity": targetEntity,
        "mapping_type": mappingType,
        "validation_rule": validationRule,
        "created_at": createdAt,
        "created_by": createdBy,
        "updated_at": updatedAt,
        "updated_by": updatedBy,
        "transformation_rule": transformationRule,
      };
}

class LoPutStack {
  String? id;
  String? loInputStackId;
  String? loId;
  String? description;
  String? naturalLanguage;

  LoPutStack({
    this.id,
    this.loInputStackId,
    this.loId,
    this.description,
    this.naturalLanguage,
  });

  LoPutStack copyWith({
    String? id,
    String? loInputStackId,
    String? loId,
    String? description,
    String? naturalLanguage,
  }) =>
      LoPutStack(
        id: id ?? this.id,
        loInputStackId: loInputStackId ?? this.loInputStackId,
        loId: loId ?? this.loId,
        description: description ?? this.description,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoPutStack.fromJson(Map<String, dynamic> json) => LoPutStack(
        id: json["id"],
        loInputStackId: json["lo_input_stack_id"],
        loId: json["lo_id"],
        description: json["description"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_input_stack_id": loInputStackId,
        "lo_id": loId,
        "description": description,
        "natural_language": naturalLanguage,
      };
}

class LoNestedFunction {
  String? id;
  String? loId;
  String? functionName;
  String? functionType;
  List<String>? functionParameters;
  String? description;
  String? returns;
  String? outputsTo;
  String? naturalLanguage;

  LoNestedFunction({
    this.id,
    this.loId,
    this.functionName,
    this.functionType,
    this.functionParameters,
    this.description,
    this.returns,
    this.outputsTo,
    this.naturalLanguage,
  });

  LoNestedFunction copyWith({
    String? id,
    String? loId,
    String? functionName,
    String? functionType,
    List<String>? functionParameters,
    String? description,
    String? returns,
    String? outputsTo,
    String? naturalLanguage,
  }) =>
      LoNestedFunction(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        functionName: functionName ?? this.functionName,
        functionType: functionType ?? this.functionType,
        functionParameters: functionParameters ?? this.functionParameters,
        description: description ?? this.description,
        returns: returns ?? this.returns,
        outputsTo: outputsTo ?? this.outputsTo,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoNestedFunction.fromJson(Map<String, dynamic> json) =>
      LoNestedFunction(
        id: json["id"],
        loId: json["lo_id"],
        functionName: json["function_name"],
        functionType: json["function_type"],
        functionParameters: json["function_parameters"] == null
            ? []
            : List<String>.from(json["function_parameters"]!.map((x) => x)),
        description: json["description"],
        returns: json["returns"],
        outputsTo: json["outputs_to"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "function_name": functionName,
        "function_type": functionType,
        "function_parameters": functionParameters == null
            ? []
            : List<dynamic>.from(functionParameters!.map((x) => x)),
        "description": description,
        "returns": returns,
        "outputs_to": outputsTo,
        "natural_language": naturalLanguage,
      };
}

class LoOutputItem {
  int? id;
  String? outputStackId;
  String? slotId;
  String? source;
  dynamic createdAt;
  dynamic updatedAt;
  String? loId;
  String? itemId;
  String? name;
  String? type;
  String? nestedFunctionId;
  String? createdBy;
  String? updatedBy;
  String? entityId;
  String? attributeId;
  String? entityName;
  String? attributeName;
  String? naturalLanguage;

  LoOutputItem({
    this.id,
    this.outputStackId,
    this.slotId,
    this.source,
    this.createdAt,
    this.updatedAt,
    this.loId,
    this.itemId,
    this.name,
    this.type,
    this.nestedFunctionId,
    this.createdBy,
    this.updatedBy,
    this.entityId,
    this.attributeId,
    this.entityName,
    this.attributeName,
    this.naturalLanguage,
  });

  LoOutputItem copyWith({
    int? id,
    String? outputStackId,
    String? slotId,
    String? source,
    dynamic createdAt,
    dynamic updatedAt,
    String? loId,
    String? itemId,
    String? name,
    String? type,
    String? nestedFunctionId,
    String? createdBy,
    String? updatedBy,
    String? entityId,
    String? attributeId,
    String? entityName,
    String? attributeName,
    String? naturalLanguage,
  }) =>
      LoOutputItem(
        id: id ?? this.id,
        outputStackId: outputStackId ?? this.outputStackId,
        slotId: slotId ?? this.slotId,
        source: source ?? this.source,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        loId: loId ?? this.loId,
        itemId: itemId ?? this.itemId,
        name: name ?? this.name,
        type: type ?? this.type,
        nestedFunctionId: nestedFunctionId ?? this.nestedFunctionId,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoOutputItem.fromJson(Map<String, dynamic> json) => LoOutputItem(
        id: json["id"],
        outputStackId: json["output_stack_id"],
        slotId: json["slot_id"],
        source: json["source"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        loId: json["lo_id"],
        itemId: json["item_id"],
        name: json["name"],
        type: json["type"],
        nestedFunctionId: json["nested_function_id"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        entityName: json["entity_name"],
        attributeName: json["attribute_name"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "output_stack_id": outputStackId,
        "slot_id": slotId,
        "source": source,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "lo_id": loId,
        "item_id": itemId,
        "name": name,
        "type": type,
        "nested_function_id": nestedFunctionId,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "entity_name": entityName,
        "attribute_name": attributeName,
        "natural_language": naturalLanguage,
      };
}

class LoUiEntityAttributeStack {
  String? id;
  String? loUiEntityAttributeStackId;
  String? entityId;
  String? attributeId;
  String? uiForm;
  ParsedGos? styleParameters;
  dynamic helperTip;
  bool? readOnly;
  bool? required;
  bool? hidden;
  bool? isVisible;
  String? naturalLanguage;

  LoUiEntityAttributeStack({
    this.id,
    this.loUiEntityAttributeStackId,
    this.entityId,
    this.attributeId,
    this.uiForm,
    this.styleParameters,
    this.helperTip,
    this.readOnly,
    this.required,
    this.hidden,
    this.isVisible,
    this.naturalLanguage,
  });

  LoUiEntityAttributeStack copyWith({
    String? id,
    String? loUiEntityAttributeStackId,
    String? entityId,
    String? attributeId,
    String? uiForm,
    ParsedGos? styleParameters,
    dynamic helperTip,
    bool? readOnly,
    bool? required,
    bool? hidden,
    bool? isVisible,
    String? naturalLanguage,
  }) =>
      LoUiEntityAttributeStack(
        id: id ?? this.id,
        loUiEntityAttributeStackId:
            loUiEntityAttributeStackId ?? this.loUiEntityAttributeStackId,
        entityId: entityId ?? this.entityId,
        attributeId: attributeId ?? this.attributeId,
        uiForm: uiForm ?? this.uiForm,
        styleParameters: styleParameters ?? this.styleParameters,
        helperTip: helperTip ?? this.helperTip,
        readOnly: readOnly ?? this.readOnly,
        required: required ?? this.required,
        hidden: hidden ?? this.hidden,
        isVisible: isVisible ?? this.isVisible,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoUiEntityAttributeStack.fromJson(Map<String, dynamic> json) =>
      LoUiEntityAttributeStack(
        id: json["id"],
        loUiEntityAttributeStackId: json["lo_ui_entity_attribute_stack_id"],
        entityId: json["entity_id"],
        attributeId: json["attribute_id"],
        uiForm: json["ui_form"],
        styleParameters: json["style_parameters"] == null
            ? null
            : ParsedGos.fromJson(json["style_parameters"]),
        helperTip: json["helper_tip"],
        readOnly: json["read_only"],
        required: json["required"],
        hidden: json["hidden"],
        isVisible: json["is_visible"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_ui_entity_attribute_stack_id": loUiEntityAttributeStackId,
        "entity_id": entityId,
        "attribute_id": attributeId,
        "ui_form": uiForm,
        "style_parameters": styleParameters?.toJson(),
        "helper_tip": helperTip,
        "read_only": readOnly,
        "required": required,
        "hidden": hidden,
        "is_visible": isVisible,
        "natural_language": naturalLanguage,
      };
}

class LoUiStack {
  String? stackId;
  String? loId;
  String? uiType;
  String? naturalLanguage;

  LoUiStack({
    this.stackId,
    this.loId,
    this.uiType,
    this.naturalLanguage,
  });

  LoUiStack copyWith({
    String? stackId,
    String? loId,
    String? uiType,
    String? naturalLanguage,
  }) =>
      LoUiStack(
        stackId: stackId ?? this.stackId,
        loId: loId ?? this.loId,
        uiType: uiType ?? this.uiType,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
      );

  factory LoUiStack.fromJson(Map<String, dynamic> json) => LoUiStack(
        stackId: json["stack_id"],
        loId: json["lo_id"],
        uiType: json["ui_type"],
        naturalLanguage: json["natural_language"],
      );

  Map<String, dynamic> toJson() => {
        "stack_id": stackId,
        "lo_id": loId,
        "ui_type": uiType,
        "natural_language": naturalLanguage,
      };
}

class ParsedLos {
  String? name;
  String? version;
  String? status;
  String? workflowSource;
  String? functionType;
  String? agentType;
  String? executionRights;
  String? naturalLanguage;
  String? goId;
  String? loId;

  ParsedLos({
    this.name,
    this.version,
    this.status,
    this.workflowSource,
    this.functionType,
    this.agentType,
    this.executionRights,
    this.naturalLanguage,
    this.goId,
    this.loId,
  });

  ParsedLos copyWith({
    String? name,
    String? version,
    String? status,
    String? workflowSource,
    String? functionType,
    String? agentType,
    String? executionRights,
    String? naturalLanguage,
    String? goId,
    String? loId,
  }) =>
      ParsedLos(
        name: name ?? this.name,
        version: version ?? this.version,
        status: status ?? this.status,
        workflowSource: workflowSource ?? this.workflowSource,
        functionType: functionType ?? this.functionType,
        agentType: agentType ?? this.agentType,
        executionRights: executionRights ?? this.executionRights,
        naturalLanguage: naturalLanguage ?? this.naturalLanguage,
        goId: goId ?? this.goId,
        loId: loId ?? this.loId,
      );

  factory ParsedLos.fromJson(Map<String, dynamic> json) => ParsedLos(
        name: json["name"],
        version: json["version"],
        status: json["status"],
        workflowSource: json["workflow_source"],
        functionType: json["function_type"],
        agentType: json["agent_type"],
        executionRights: json["execution_rights"],
        naturalLanguage: json["natural_language"],
        goId: json["go_id"],
        loId: json["lo_id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "version": version,
        "status": status,
        "workflow_source": workflowSource,
        "function_type": functionType,
        "agent_type": agentType,
        "execution_rights": executionRights,
        "natural_language": naturalLanguage,
        "go_id": goId,
        "lo_id": loId,
      };
}
