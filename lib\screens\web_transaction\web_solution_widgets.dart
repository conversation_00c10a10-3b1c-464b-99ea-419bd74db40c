import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web_transaction/web_po_review_rightpanel.dart';
import 'package:nsl/screens/web_transaction/web_transaction_widgets_demo.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:nsl/theme/spacing.dart';

import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class WebSolutionWidgets extends StatefulWidget {
  WebSolutionWidgets(
      {super.key,
      this.isPadding = false,
      this.isChatEnabled = false,
      this.isExpanded = false,
      this.onExpansionChange});
  bool isPadding;
  bool isChatEnabled;
  bool isExpanded;
  Function? onExpansionChange;

  @override
  State<WebSolutionWidgets> createState() => _WebSolutionWidgetsState();
}

class _WebSolutionWidgetsState extends State<WebSolutionWidgets> {
  final TextEditingController _textController = TextEditingController();
  final List<String> _messages = [];

  // JSON data variables
  Map<String, dynamic>? jsonData;
  Map<String, dynamic>? solutionWidgetsData;
  List<Map<String, dynamic>> optionButtonsData = [];
  List<Map<String, dynamic>> recommendationData = [];
  List<Map<String, dynamic>> actionButtonsData = [];
  Map<String, dynamic>? userInterfaceData;

  // Collection data for the back navigation
  final Map<String, dynamic> _collectionData = {
    "name": "Solution Widgets",
    "backImage": "assets/images/my_business/back_arrow.svg"
  };

  @override
  void initState() {
    super.initState();
    _loadJsonData();
  }

  Future<void> _loadJsonData() async {
    try {
      // Load solution_components.json for main UI
      final String jsonString =
          await rootBundle.loadString('assets/data/solution_components.json');
      final Map<String, dynamic> data = jsonDecode(jsonString);

      // Load solution_widgets.json for side panel
      final String solutionWidgetsJsonString =
          await rootBundle.loadString('assets/data/solution_widgets.json');
      final Map<String, dynamic> solutionWidgetsJsonData = jsonDecode(solutionWidgetsJsonString);

      setState(() {
        jsonData = data;
        solutionWidgetsData = solutionWidgetsJsonData;

        // Extract data from JSON
        final leaveAnalytics = data['leave_analytics_dashboard'];
        final uiComponents = leaveAnalytics?['user_interface_components'];
        final embeddedForm = uiComponents?['chat_interface']?['embedded_form'];

        userInterfaceData = uiComponents;

        // Map quick_actions to optionButtonsData
        final quickActions = embeddedForm?['quick_actions'] as List<dynamic>?;
        if (quickActions != null) {
          optionButtonsData = quickActions
              .map((action) => {
                    'type': 'card',
                    'icon': 'assets/images/my_business/box_add.svg',
                    'label': action.toString(),
                    'onTap': () {
                      print("$action clicked");
                    },
                  })
              .toList();
        }

        // Map info_notifications to recommendationData
        final infoNotifications =
            embeddedForm?['info_notifications'] as List<dynamic>?;
        if (infoNotifications != null) {
          recommendationData = infoNotifications
              .map((notification) => {
                    'type': 'text',
                    'icon': notification['icon'] ?? '',
                    'value': notification['message'] ?? '',
                    'notificationType': notification['type'] ?? 'info',
                  })
              .toList();
        }

        // Map action_buttons to actionButtonsData
        final actionButtons = embeddedForm?['action_buttons'] as List<dynamic>?;
        if (actionButtons != null) {
          actionButtonsData = actionButtons
              .map((button) => {
                    'text': button['text'] ?? '',
                    'type': button['type'] ?? 'secondary',
                    'action': button['action'] ?? '',
                    'image': button['image'] ?? '',
                  })
              .toList();
        }
      });

      print("JSON data loaded successfully");
    } catch (e) {
      print("Error loading JSON data: $e");
      // Fallback to default data
      _setDefaultData();
    }
  }

  void _setDefaultData() {
    optionButtonsData = [
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Check Policy',
        'onTap': () {
          print("Check Policy clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Add to Calendar',
        'onTap': () {
          print("Add to Calendar clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Use AI Suggestion',
        'onTap': () {
          print("AI Suggestion clicked");
        },
      },
    ];

    recommendationData = [
      {'type': 'text', 'value': '2-Day Overlap With John (Lead Developer)'},
      {'type': 'text', 'value': 'Manager Is Available For Approval All Week'},
      {
        'type': 'text',
        'value': 'Youll Have 12 Days Remaining After This Request'
      },
    ];
  }

  void _handleSendMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        _textController.clear();
      });
    }
  }

  // Build back navigation element with arrow and text
  Widget _buildBackNavigation() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          final provider = Provider.of<WebHomeProvider>(context, listen: false);
                        provider.selectedSolutionName="";
          if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
              provider.solutionWidgetsSelectedFrom ==
                  ScreenConstants.myBusinessHome) {
            provider.currentScreenIndex = ScreenConstants.myBusinessHome;
            provider.solutionWidgetsSelectedFrom = "";
          } else if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
              provider.solutionWidgetsSelectedFrom ==
                  ScreenConstants.myBusinessSolutions) {
            provider.currentScreenIndex = ScreenConstants.myBusinessSolutions;
            provider.solutionWidgetsSelectedFrom = "";
          }
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            left: AppSpacing.sm,
            top: AppSpacing.xxs,
            bottom: AppSpacing.xxs,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Back arrow icon from JSON
              CustomImage.asset(
                _collectionData['backImage'],
                width: 12,
                height: 12,
                color: Colors.black,
              ).toWidget(),
              SizedBox(width: AppSpacing.sm),

              // Collection name text from JSON
              Text(
                Provider.of<WebHomeProvider>(context, listen: false)
                        .selectedSolutionName
                        .isNotEmpty
                    ? Provider.of<WebHomeProvider>(context, listen: false)
                        .selectedSolutionName
                    : _collectionData['name'],
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s13,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final String latestMessage = _messages.isNotEmpty ? _messages.last : "";

    return Scaffold(
      backgroundColor: const Color(0xffF7F9FB),
      body: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: AppSpacing.sm,
                          right: AppSpacing.sm,
                          top: AppSpacing.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBackNavigation(),
                          SizedBox(height: AppSpacing.md),
                          Container(
                            color: const Color(0xffF7F9FB),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                constraints: BoxConstraints(
                                  maxWidth:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE9F2F7),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: const Color(0xFF0058FF),
                                      radius: 12,
                                      child: Text(
                                        'D',
                                        style: FontManager.getCustomStyle(
                                          color: Colors.white,
                                          fontSize: MediaQuery.of(context)
                                                      .size
                                                      .width >
                                                  1600
                                              ? FontManager.s16
                                              : FontManager.s14,
                                          fontWeight: FontManager.regular,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        latestMessage.isNotEmpty
                                            ? latestMessage
                                            : "Type a message below...",
                                        style: FontManager.getCustomStyle(
                                          fontSize: MediaQuery.of(context)
                                                      .size
                                                      .width >
                                                  1600
                                              ? FontManager.s16
                                              : FontManager.s14,
                                          fontWeight: FontManager.regular,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          WidgetComponent(
                            uiData: userInterfaceData,
                          ),
                          SizedBox(
                            height: AppSpacing.xxs,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(AppSpacing
                                  .sm), // 👈 Adjust the radius as needed
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(left: AppSpacing.sm),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(bottom: 8),
                                    child: Text(
                                      "Recommendation in Context",
                                      style: FontManager.getCustomStyle(
                                        color: Colors.black,
                                        fontSize:
                                            MediaQuery.of(context).size.width >
                                                    1550
                                                ? FontManager.s14
                                                : FontManager.s12,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        fontWeight: FontManager.semiBold,
                                      ),
                                    ),
                                  ),
                                  Wrap(
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: optionButtonsData.map((data) {
                                      return OptionComponent(data: data);
                                    }).toList(),
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: AppSpacing.sm),
                          RecommendationBox(data: recommendationData),

                     const SizedBox(height: AppSpacing.sm),
                     //PURCHASE ORDER REVIEW
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(AppSpacing
                                  .sm), // 👈 Adjust the radius as needed
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(left: AppSpacing.sm),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(bottom: 8),
                                    child: Text(
                                      "PURCHASE ORDER REVIEW",
                                      style: FontManager.getCustomStyle(
                                        color: Colors.black,
                                        fontSize:
                                            MediaQuery.of(context).size.width >
                                                    1550
                                                ? FontManager.s14
                                                : FontManager.s12,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        fontWeight: FontManager.semiBold,
                                      ),
                                    ),
                                  ),
                                                    
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: AppSpacing.xs,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: AppSpacing.sm,
                    right: AppSpacing.sm,
                  ),
                  child: ChatField(
                    isGeneralLoading: false,
                    onSendMessage: _handleSendMessage,
                    controller: _textController,
                    actionButtonsData: actionButtonsData,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              // padding: EdgeInsets.only( top: !widget.isPadding?AppSpacing.xxl:0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  left: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: RightSectionWithTabs(
                jsonData: jsonData,
                solutionWidgetsData: solutionWidgetsData,
                isChatEnabled: widget.isChatEnabled,
                isExpanded: widget.isExpanded,
                onExpansionChange: widget.onExpansionChange,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class WidgetComponent extends StatelessWidget {
  final Map<String, dynamic>? uiData;

  const WidgetComponent({super.key, this.uiData});

  // Helper method to create a widget from form field data
  Widget _createFieldWidget(
      String fieldKey, Map<String, dynamic> fieldData, BuildContext context) {
    switch (fieldKey) {
      case 'leave_type':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Leave Type',
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontSize: MediaQuery.of(context).size.width > 1550
                    ? FontManager.s14
                    : FontManager.s12,
                fontWeight: FontManager.regular,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                fieldData['selected'] ?? 'Annual Leave',
                style: FontManager.getCustomStyle(
                  fontSize: MediaQuery.of(context).size.width > 1550
                      ? FontManager.s16
                      : FontManager.s14,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ),
          ],
        );

      case 'date_range':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Date Range',
              style: FontManager.getCustomStyle(
                fontSize: MediaQuery.of(context).size.width > 1550
                    ? FontManager.s14
                    : FontManager.s12,
                fontWeight: FontManager.regular,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      fieldData['start_date'] ?? 'Start Date',
                      style: FontManager.getCustomStyle(
                        fontSize: MediaQuery.of(context).size.width > 1550
                            ? FontManager.s16
                            : FontManager.s14,
                        fontFamily: FontManager.fontFamilyInter,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'to',
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s14
                        : FontManager.s12,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      fieldData['end_date'] ?? 'End Date',
                      style: FontManager.getCustomStyle(
                        fontSize: MediaQuery.of(context).size.width > 1550
                            ? FontManager.s16
                            : FontManager.s14,
                        fontFamily: FontManager.fontFamilyInter,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );

      case 'reason':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reason',
              style: FontManager.getCustomStyle(
                fontSize: MediaQuery.of(context).size.width > 1550
                    ? FontManager.s14
                    : FontManager.s12,
                fontWeight: FontManager.regular,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                fieldData['value'] ?? 'Enter reason...',
                style: FontManager.getCustomStyle(
                  fontSize: MediaQuery.of(context).size.width > 1550
                      ? FontManager.s16
                      : FontManager.s14,
                  fontFamily: FontManager.fontFamilyInter,
                  color: fieldData['value'] != null
                      ? Colors.black
                      : Colors.grey.shade500,
                ),
              ),
            ),
          ],
        );

      default:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Unknown field: $fieldKey',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s14,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        );
    }
  }

  // Helper method to get flex value for different field types (similar to WidgetFactory.getFlexValueForControl)
  int _getFlexValueForField(String fieldKey) {
    switch (fieldKey) {
      case 'leave_type':
        return 1; // Single dropdown takes 1 unit
      case 'date_range':
        return 2; // Date range takes 2 units (start and end date)
      case 'reason':
        return 3; // Reason field takes full width (3 units)
      default:
        return 1; // Default to 1 unit
    }
  }

  @override
  Widget build(BuildContext context) {
    // Extract data from user_interface_components
    final header = uiData?['header'];
    final chatInterface = uiData?['chat_interface'];
    final embeddedForm = chatInterface?['embedded_form'];
    final formHeader = embeddedForm?['form_header'];
    final formFields = embeddedForm?['form_fields'];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Form header from JSON
          if (formHeader != null) ...[
            Text(
              formHeader,
              style: FontManager.getCustomStyle(
                fontSize: MediaQuery.of(context).size.width > 1550
                    ? FontManager.s16
                    : FontManager.s14,
                fontWeight: FontManager.semiBold,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Form fields from JSON arranged in rows of 3
          if (formFields != null) ...[
            _buildFieldRows(formFields, context),
          ],

          // Fallback content if no JSON data
          if (uiData == null) ...[
            Text("LEAVE REQUEST",
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontWeight: FontManager.semiBold,
                )),
            const SizedBox(height: 12),
            Text(
              "Form fields will be populated from JSON data",
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Build field rows with 3 widgets per row (similar to workflow_transaction_screen.dart)
  Widget _buildFieldRows(
      Map<String, dynamic> formFields, BuildContext context) {
    // Check if we're on web platform
    final isWeb = MediaQuery.of(context).size.width > 600;

    // Get all field entries
    final fieldEntries = formFields.entries.toList();

    if (!isWeb) {
      // For mobile, display fields vertically
      return Column(
        children: fieldEntries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: _createFieldWidget(entry.key, entry.value, context),
          );
        }).toList(),
      );
    }

    // For web, arrange in rows of 3 widgets (similar to WidgetFactory.createFlexibleWidget approach)
    List<List<MapEntry<String, dynamic>>> rows = [];
    List<MapEntry<String, dynamic>> currentRow = [];
    int currentRowFlex = 0;
    const int maxRowFlex = 3; // Total row flex of 3 (3 widgets per row)

    for (final entry in fieldEntries) {
      // Get the flex value for this field
      final fieldFlex = _getFlexValueForField(entry.key);

      // If adding this field would exceed the max row flex, start a new row
      if (currentRowFlex + fieldFlex > maxRowFlex && currentRow.isNotEmpty) {
        rows.add(List.from(currentRow));
        currentRow = [];
        currentRowFlex = 0;
      }

      // Add the field to the current row
      currentRow.add(entry);
      currentRowFlex += fieldFlex;

      // If we've exactly filled a row, start a new one
      if (currentRowFlex == maxRowFlex) {
        rows.add(List.from(currentRow));
        currentRow = [];
        currentRowFlex = 0;
      }
    }

    // Add any remaining fields in the last row
    if (currentRow.isNotEmpty) {
      rows.add(currentRow);
    }

    // Build each row
    List<Widget> rowWidgets = [];
    for (int rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      final rowFields = rows[rowIndex];
      final rowChildren = <Widget>[];

      for (int i = 0; i < rowFields.length; i++) {
        final entry = rowFields[i];
        final fieldFlex = _getFlexValueForField(entry.key);

        // Create the field widget wrapped in a Flexible with appropriate flex value
        rowChildren.add(
          Flexible(
            flex: fieldFlex,
            child: Padding(
              padding: EdgeInsets.only(
                right: i < rowFields.length - 1
                    ? 16.0
                    : 0.0, // Add spacing between widgets
              ),
              child: _createFieldWidget(entry.key, entry.value, context),
            ),
          ),
        );
      }

      // Add the row to rowWidgets
      rowWidgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: rowChildren,
          ),
        ),
      );
    }

    return Column(
      children: rowWidgets,
    );
  }
}

class OptionComponent extends StatelessWidget {
  final Map<String, dynamic> data;

  const OptionComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    switch (data['type']) {
      case 'card':
        return GestureDetector(
          onTap: data['onTap'],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  data['icon'],
                  width: MediaQuery.of(context).size.width > 1550 ? 16 : 14,
                  height: MediaQuery.of(context).size.width > 1550 ? 16 : 14,
                  colorFilter: const ColorFilter.mode(
                      Color(0xFF0058FF), BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  data['label'],
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s14
                        : FontManager.s12,
                    color: Colors.black,
                    fontWeight: FontManager.regular,
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class RecommendationBox extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const RecommendationBox({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7DA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.map((item) {
          switch (item['type']) {
            case 'text':
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    if (item['icon'] != null &&
                        item['icon'].toString().isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Text(
                          item['icon'],
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                      ),
                    Expanded(
                      child: Text(
                        item['value'],
                        style: FontManager.getCustomStyle(
                          fontSize: MediaQuery.of(context).size.width > 1550
                              ? FontManager.s14
                              : FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                          fontWeight: FontManager.medium,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            default:
              return const SizedBox.shrink();
          }
        }).toList(),
      ),
    );
  }
}

class ChatField extends StatefulWidget {
  final double? width;
  final double? height;
  final bool isGeneralLoading;
  final Function() onSendMessage;
  final Function()? onCancelRequest;
  final TextEditingController? controller;
  final List<Map<String, dynamic>>? actionButtonsData;

  ChatField({
    super.key,
    this.width,
    this.height,
    required this.isGeneralLoading,
    required this.onSendMessage,
    this.onCancelRequest,
    this.controller,
    this.actionButtonsData,
  });

  @override
  State<ChatField> createState() => _ChatFieldState();
}

class _ChatFieldState extends State<ChatField> {
  late TextEditingController chatController;
  final FocusNode _focusNode = FocusNode();
  bool _isShiftPressed = false;

  @override
  void initState() {
    super.initState();
    chatController = widget.controller ?? TextEditingController();
    chatController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      chatController.dispose();
    }
    chatController.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  bool get hasTextInChatField => chatController.text.trim().isNotEmpty;

  void _sendMessage() {
    if (widget.isGeneralLoading) {
      return;
    }

    final cleanedText = chatController.text.trim();
    if (cleanedText.isNotEmpty) {
      chatController.text = cleanedText;
      widget.onSendMessage();
    }
  }

  void _cancelRequest() {
    chatController.clear();
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
        provider.solutionWidgetsSelectedFrom ==
            ScreenConstants.myBusinessHome) {
      provider.currentScreenIndex = ScreenConstants.myBusinessHome;
      provider.solutionWidgetsSelectedFrom = "";
    } else if (provider.solutionWidgetsSelectedFrom.isNotEmpty &&
        provider.solutionWidgetsSelectedFrom ==
            ScreenConstants.myBusinessSolutions) {
      provider.currentScreenIndex = ScreenConstants.myBusinessSolutions;
      provider.solutionWidgetsSelectedFrom = ""; // Navigator.pop(context);
    }
    if (widget.onCancelRequest != null) {
      widget.onCancelRequest!();
    }
  }

  Future<void> _saveScreen() async {
    try {
      print("Save button pressed - saving screen data to local storage");

      final prefs = await SharedPreferences.getInstance();

      final screenData = {
        'currentText': chatController.text,
        'timestamp': DateTime.now().toIso8601String(),
        'screenType': 'web_solution_widgets',
      };

      final jsonString = jsonEncode(screenData);

      final key = 'screen_data_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(key, jsonString);
      await prefs.setString('latest_screen_data', jsonString);

      print("Data saved to local storage: $jsonString");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Screen data saved to local storage successfully!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print("Error saving to local storage: $e");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save screen data!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildButton({
    required Widget icon,
    required Function() onPressed,
    Color? iconColor,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        hoverColor: const Color(0xFF0058FF),
        child: Padding(
          padding: MediaQuery.of(context).size.width > 1550
              ? EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs, vertical: AppSpacing.xxs)
              : EdgeInsets.all(AppSpacing.xs),
          child: icon,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      height: MediaQuery.of(context).size.width > 1550 ? 65 : 50,
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
      child: Row(
        children: [
          Expanded(
            child: KeyboardListener(
              focusNode: _focusNode,
              onKeyEvent: (KeyEvent event) {
                if (event is KeyDownEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

                  if (event.logicalKey == LogicalKeyboardKey.enter) {
                    if (!_isShiftPressed && !widget.isGeneralLoading) {
                      _sendMessage();
                    }
                  }
                } else if (event is KeyUpEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
                }
              },
              child: TextField(
                cursorHeight: 16,
                controller: chatController,
                maxLines: 1,
                enabled: !widget.isGeneralLoading,
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: MediaQuery.of(context).size.width > 1550
                      ? FontManager.s16
                      : FontManager.s14,
                  fontWeight: FontManager.regular,
                ),
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm, vertical: AppSpacing.xs),
                  hintStyle: FontManager.getCustomStyle(
                    fontSize: MediaQuery.of(context).size.width > 1550
                        ? FontManager.s16
                        : FontManager.s14,
                    fontWeight: FontManager.regular,
                    color: Colors.grey.shade500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                  hintText: widget.isGeneralLoading
                      ? AppLocalizations.of(context)
                          .translate('home.sendingMessage')
                      : AppLocalizations.of(context).translate('home.askNSL'),
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  enabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  focusedBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                  disabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                ),
                onSubmitted: (value) {
                  if (!widget.isGeneralLoading) {
                    _sendMessage();
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Row(
            children: [
              // Generate buttons from JSON data
              if (widget.actionButtonsData != null)
                ...widget.actionButtonsData!.map((buttonData) {
                  return _buildButton(
                    icon: buttonData['image'] != null &&
                            buttonData['image'].toString().isNotEmpty
                        ? SvgPicture.asset(
                            buttonData['image'],
                            width: MediaQuery.of(context).size.width > 1550
                                ? 35
                                : 30,
                            height: MediaQuery.of(context).size.width > 1550
                                ? 35
                                : 30,
                          )
                        : Icon(
                            buttonData['text'] == 'Submit Request'
                                ? Icons.send
                                : buttonData['text'] == 'Cancel'
                                    ? Icons.close
                                    : Icons.save,
                            size: 20,
                          ),
                    onPressed: () {
                      if (buttonData['text'] == 'Submit Request') {
                        _sendMessage();
                      } else if (buttonData['text'] == 'Cancel') {
                        _cancelRequest();
                      } else if (buttonData['text'] == 'Save as Draft') {
                        _saveScreen();
                      }
                      print("${buttonData['text']} clicked");
                    },
                  );
                }).toList(),

              // Fallback buttons if no JSON data
              if (widget.actionButtonsData == null ||
                  widget.actionButtonsData!.isEmpty) ...[
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/cancel_soluton.svg',
                  ),
                  onPressed: _cancelRequest,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/draft_solution.svg',
                  ),
                  onPressed: _saveScreen,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/submit_solution.svg',
                  ),
                  onPressed: (hasTextInChatField && !widget.isGeneralLoading)
                      ? _sendMessage
                      : () {},
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}

class RightSectionWithTabs extends StatefulWidget {
  final Map<String, dynamic>? jsonData;
  final Map<String, dynamic>? solutionWidgetsData;
  bool isChatEnabled;
  bool isExpanded;
  Function? onExpansionChange;

  RightSectionWithTabs(
      {super.key,
      this.jsonData,
      this.solutionWidgetsData,
      this.isChatEnabled = false,
      this.isExpanded = false,
      this.onExpansionChange});

  @override
  State<RightSectionWithTabs> createState() => _RightSectionWithTabsState();
}

class _RightSectionWithTabsState extends State<RightSectionWithTabs> {
  int selectedIndex = 0;
  List<Map<String, dynamic>> tabs = [];

  @override
  void initState() {
    super.initState();
    _loadTabsData();
  }

  @override
  void didUpdateWidget(covariant RightSectionWithTabs oldWidget) {
    _loadTabsData();
    super.didUpdateWidget(oldWidget);
  }

  void _loadTabsData() {
    // Use solution_widgets.json data if available, otherwise fallback to solution_components.json
    if (widget.solutionWidgetsData != null) {
      // Build content from solution_widgets.json side_panel_extended
      final conversationalUI = widget.solutionWidgetsData!['conversational_ui_information_hierarchy'];
      final level2Context = conversationalUI?['level_2_contextual_information'];
      final sidePanelExtended = level2Context?['side_panel_extended'];

      String relatedContent = _buildSolutionWidgetsRelatedContent(sidePanelExtended);
      String contextualContent = _buildSolutionWidgetsContextualContent(sidePanelExtended);

      tabs = [
        {
          "label": "Related",
          "iconPath":
              'assets/images/my_business/solutions/solution_related.svg',
          "content": relatedContent,
        },
        {
          "label": "Contextual",
          "iconPath":
              'assets/images/my_business/solutions/solution_contextual.svg',
          "content": contextualContent,
        },
      ];
      setState(() {});
    } else if (widget.jsonData != null) {
      // Fallback to original solution_components.json data
      final leaveAnalytics = widget.jsonData!['leave_analytics_dashboard'];
      final dataAnalytics = leaveAnalytics?['data_analytics'];
      final systemArchitecture = leaveAnalytics?['system_architecture'];

      // Build Related tab content from data_analytics
      String relatedContent = _buildDataAnalyticsContent(dataAnalytics);

      // Build Contextual tab content from system_architecture
      String contextualContent =
          _buildSystemArchitectureContent(systemArchitecture);

      tabs = [
        {
          "label": "Related",
          "iconPath":
              'assets/images/my_business/solutions/solution_related.svg',
          "content": relatedContent,
        },
        {
          "label": "Contextual",
          "iconPath":
              'assets/images/my_business/solutions/solution_contextual.svg',
          "content": contextualContent,
        },
      ];
      setState(() {});
    } else {
      // Fallback tabs
      tabs = [
        {
          "label": "Related",
          "iconPath":
              'assets/images/my_business/solutions/solution_related.svg',
          "content": "This is the Related tab content.\n" * 30,
        },
        {
          "label": "Contextual",
          "iconPath":
              'assets/images/my_business/solutions/solution_contextual.svg',
          "content": "This is the Contextual tab content.\n" * 30,
        },
      ];
    }
  }

  String _buildDataAnalyticsContent(Map<String, dynamic>? dataAnalytics) {
    if (dataAnalytics == null) return "No data analytics available.";

    StringBuffer content = StringBuffer();
    // content.writeln("LEAVE ANALYTICS\n");

    // Leave Analytics Card
    final leaveAnalyticsCard = dataAnalytics['leave_analytics_card'];
    if (leaveAnalyticsCard != null) {
      content.writeln("${leaveAnalyticsCard['header'] ?? 'LEAVE ANALYTICS'}\n");

      final personalBalance = leaveAnalyticsCard['personal_balance'];
      if (personalBalance != null) {
        content.writeln("Personal Balance:");
        content.writeln(
            "• Current Usage: ${personalBalance['current_usage'] ?? 'N/A'}");
        content
            .writeln("• Percentage: ${personalBalance['percentage'] ?? 'N/A'}");
        content.writeln(
            "• Remaining After Request: ${personalBalance['remaining_after_request'] ?? 'N/A'}\n");
      }
    }

    // Team Availability Card
    final teamAvailabilityCard = dataAnalytics['team_availability_card'];
    if (teamAvailabilityCard != null) {
      content.writeln("TEAM AVAILABILITY\n");
      content.writeln(
          "Time Period: ${teamAvailabilityCard['time_period'] ?? 'N/A'}\n");

      final teamMembers =
          teamAvailabilityCard['team_members'] as List<dynamic>?;
      if (teamMembers != null) {
        content.writeln("Team Members:");
        for (var member in teamMembers) {
          content.writeln("• ${member['name']} (${member['role']})");
          content
              .writeln("  Availability: ${member['availability_percentage']}");
          content.writeln("  Status: ${member['status']}\n");
        }
      }
    }

    // Conflicts & Warnings Card
    final conflictsCard = dataAnalytics['conflicts_warnings_card'];
    if (conflictsCard != null) {
      content.writeln("⚠️ CONFLICTS & WARNINGS\n");

      final conflicts =
          conflictsCard['high_priority_conflicts'] as List<dynamic>?;
      if (conflicts != null) {
        content.writeln("High Priority Conflicts:");
        for (var conflict in conflicts) {
          content.writeln(
              "• ${conflict['description']} (${conflict['severity']})");
        }
        content.writeln();
      }

      final aiSuggestion = conflictsCard['ai_suggestion'];
      if (aiSuggestion != null) {
        content.writeln("💡 AI Suggestion:");
        content.writeln("${aiSuggestion['recommendation'] ?? 'N/A'}\n");

        final benefits = aiSuggestion['benefits'] as List<dynamic>?;
        if (benefits != null) {
          content.writeln("Benefits:");
          for (var benefit in benefits) {
            content.writeln("• $benefit");
          }
        }
      }
    }

    return content.toString();
  }

  String _buildSystemArchitectureContent(
      Map<String, dynamic>? systemArchitecture) {
    if (systemArchitecture == null)
      return "No system architecture information available.";

    StringBuffer content = StringBuffer();
    content.writeln("🏗️ SYSTEM ARCHITECTURE\n");

    content.writeln(
        "${systemArchitecture['description'] ?? 'Backend infrastructure supporting the leave analytics dashboard'}\n");

    // Real-time Processing
    final realTimeProcessing = systemArchitecture['real_time_processing'];
    if (realTimeProcessing != null) {
      content.writeln("⚡ REAL-TIME PROCESSING\n");

      final dataSources = realTimeProcessing['data_sources'] as List<dynamic>?;
      if (dataSources != null) {
        content.writeln("Data Sources:");
        for (var source in dataSources) {
          content.writeln("• $source");
        }
        content.writeln();
      }

      final updateFreqs = realTimeProcessing['update_frequencies'];
      if (updateFreqs != null) {
        content.writeln("Update Frequencies:");
        updateFreqs.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // AI Processing
    final aiProcessing = systemArchitecture['ai_processing'];
    if (aiProcessing != null) {
      content.writeln("🤖 AI PROCESSING\n");

      final intentRecognition = aiProcessing['intent_recognition'];
      if (intentRecognition != null) {
        content.writeln("Intent Recognition:");
        content.writeln("• Input: ${intentRecognition['input'] ?? 'N/A'}");
        content.writeln(
            "• Confidence: ${intentRecognition['confidence'] ?? 'N/A'}\n");

        final parsedEntities = intentRecognition['parsed_entities'];
        if (parsedEntities != null) {
          content.writeln("Parsed Entities:");
          parsedEntities.forEach((key, value) {
            content.writeln("• ${key.toUpperCase()}: $value");
          });
          content.writeln();
        }
      }

      final suggestionEngine = aiProcessing['suggestion_engine'];
      if (suggestionEngine != null) {
        content.writeln("Suggestion Engine:");
        suggestionEngine.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // Integration Points
    final integrationPoints =
        systemArchitecture['integration_points'] as List<dynamic>?;
    if (integrationPoints != null) {
      content.writeln("🔗 INTEGRATION POINTS\n");
      for (var point in integrationPoints) {
        content.writeln("• $point");
      }
      content.writeln();
    }

    // Performance Metrics
    final performanceMetrics = systemArchitecture['performance_metrics'];
    if (performanceMetrics != null) {
      content.writeln("📈 PERFORMANCE METRICS\n");

      final responseTimes = performanceMetrics['response_times'];
      if (responseTimes != null) {
        content.writeln("Response Times:");
        responseTimes.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }

      final dataFreshness = performanceMetrics['data_freshness'];
      if (dataFreshness != null) {
        content.writeln("Data Freshness:");
        dataFreshness.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
      }
    }

    return content.toString();
  }

  // Build content for Related tab from solution_widgets.json
  String _buildSolutionWidgetsRelatedContent(Map<String, dynamic>? sidePanelExtended) {
    if (sidePanelExtended == null) return "No side panel data available.";

    StringBuffer content = StringBuffer();

    // Leave Analytics Card
    final leaveAnalyticsCard = sidePanelExtended['leave_analytics_card'];
    if (leaveAnalyticsCard != null) {
      final cardStructure = leaveAnalyticsCard['card_structure'];
      if (cardStructure != null) {
        final cardHeader = cardStructure['card_header'];
        if (cardHeader != null) {
          final primaryTitle = cardHeader['primary_title'];
          if (primaryTitle != null) {
            content.writeln("${primaryTitle['text'] ?? '📊 LEAVE ANALYTICS'}\n");
          }
        }

        // Balance Section
        final balanceSection = cardStructure['balance_section'];
        if (balanceSection != null) {
          final sectionLabel = balanceSection['section_label'];
          if (sectionLabel != null) {
            content.writeln("${sectionLabel['text'] ?? 'Your Balance'}");
          }
          final progressIndicator = balanceSection['progress_indicator'];
          if (progressIndicator != null) {
            final textLabel = progressIndicator['text_label'];
            if (textLabel != null) {
              content.writeln("• ${textLabel['text'] ?? '15/20 days'}\n");
            }
          }
        }

        // Optimal Days Section
        final optimalDaysSection = cardStructure['optimal_days_section'];
        if (optimalDaysSection != null) {
          final sectionLabel = optimalDaysSection['section_label'];
          if (sectionLabel != null) {
            content.writeln("${sectionLabel['text'] ?? 'Optimal Days (High Approval ✓)'}");
          }
          
          final legend = optimalDaysSection['legend'];
          if (legend != null) {
            final items = legend['items'] as List<dynamic>?;
            if (items != null) {
              content.writeln("Legend:");
              for (var item in items) {
                content.writeln("• ${item['label']} (${item['color']})");
              }
            }
          }
          content.writeln();
        }
      }
    }

    // Team Availability Card
    final teamAvailabilityCard = sidePanelExtended['team_availability_card'];
    if (teamAvailabilityCard != null) {
      final cardStructure = teamAvailabilityCard['card_structure'];
      if (cardStructure != null) {
        final cardHeader = cardStructure['card_header'];
        if (cardHeader != null) {
          final primaryTitle = cardHeader['primary_title'];
          if (primaryTitle != null) {
            content.writeln("${primaryTitle['text'] ?? '👥 TEAM AVAILABILITY'}\n");
          }
          final timeContext = cardHeader['time_context'];
          if (timeContext != null) {
            content.writeln("${timeContext['text'] ?? 'Week of Jan 15-19:'}\n");
          }
        }

        final teamMemberEntries = cardStructure['team_member_entries'];
        if (teamMemberEntries != null) {
          final entryStructure = teamMemberEntries['entry_structure'];
          if (entryStructure != null) {
            final memberName = entryStructure['member_name'];
            final memberRole = entryStructure['member_role'];
            final statusDescription = entryStructure['status_description'];
            
            if (memberName != null && memberName['examples'] != null) {
              content.writeln("Team Members:");
              final names = memberName['examples'] as List<dynamic>?;
              final roles = memberRole?['examples'] as List<dynamic>?;
              final statuses = statusDescription?['examples'] as List<dynamic>?;
              
              if (names != null) {
                for (int i = 0; i < names.length; i++) {
                  final name = names[i];
                  final role = roles != null && i < roles.length ? roles[i] : 'N/A';
                  final status = statuses != null && i < statuses.length ? statuses[i] : 'Available';
                  content.writeln("• $name ($role) - $status");
                }
              }
              content.writeln();
            }
          }
        }
      }
    }

    return content.toString();
  }

  // Build content for Contextual tab from solution_widgets.json
  String _buildSolutionWidgetsContextualContent(Map<String, dynamic>? sidePanelExtended) {
    if (sidePanelExtended == null) return "No side panel data available.";

    StringBuffer content = StringBuffer();

    // Conflicts & Warnings Card
    final conflictsWarningsCard = sidePanelExtended['conflicts_warnings_card'];
    if (conflictsWarningsCard != null) {
      final cardStructure = conflictsWarningsCard['card_structure'];
      if (cardStructure != null) {
        final cardHeader = cardStructure['card_header'];
        if (cardHeader != null) {
          final primaryTitle = cardHeader['primary_title'];
          if (primaryTitle != null) {
            content.writeln("${primaryTitle['text'] ?? '⚠️ CONFLICTS & WARNINGS'}\n");
          }
        }

        // High Priority Section
        final highPrioritySection = cardStructure['high_priority_section'];
        if (highPrioritySection != null) {
          final sectionHeader = highPrioritySection['section_header'];
          if (sectionHeader != null) {
            content.writeln("${sectionHeader['text'] ?? '🚨 High Priority Conflict:'}");
          }
          
          final conflictList = highPrioritySection['conflict_list'];
          if (conflictList != null) {
            final items = conflictList['items'] as List<dynamic>?;
            if (items != null) {
              for (var item in items) {
                content.writeln("• $item");
              }
            }
          }
          content.writeln();
        }

        // AI Suggestion Section
        final aiSuggestionSection = cardStructure['ai_suggestion_section'];
        if (aiSuggestionSection != null) {
          final suggestionHeader = aiSuggestionSection['suggestion_header'];
          if (suggestionHeader != null) {
            content.writeln("${suggestionHeader['text'] ?? '💡 AI Suggestion:'}");
          }
          
          final recommendationText = aiSuggestionSection['recommendation_text'];
          if (recommendationText != null) {
            content.writeln("${recommendationText['text'] ?? 'Consider Jan 21-23 instead:'}");
          }
          
          final benefitsList = aiSuggestionSection['benefits_list'];
          if (benefitsList != null) {
            final items = benefitsList['items'] as List<dynamic>?;
            if (items != null) {
              content.writeln("Benefits:");
              for (var item in items) {
                content.writeln("• $item");
              }
            }
          }
          content.writeln();
        }
      }
    }

    // Recent Patterns Card
    final recentPatternsCard = sidePanelExtended['recent_patterns_card'];
    if (recentPatternsCard != null) {
      final cardStructure = recentPatternsCard['card_structure'];
      if (cardStructure != null) {
        final cardHeader = cardStructure['card_header'];
        if (cardHeader != null) {
          final primaryTitle = cardHeader['primary_title'];
          if (primaryTitle != null) {
            content.writeln("${primaryTitle['text'] ?? '📋 RECENT PATTERNS'}\n");
          }
        }

        // Personal History Section
        final personalHistorySection = cardStructure['personal_history_section'];
        if (personalHistorySection != null) {
          final sectionHeader = personalHistorySection['section_header'];
          if (sectionHeader != null) {
            content.writeln("${sectionHeader['text'] ?? 'Your Leave History:'}");
          }
          
          final patternList = personalHistorySection['pattern_list'];
          if (patternList != null) {
            final items = patternList['items'] as List<dynamic>?;
            if (items != null) {
              for (var item in items) {
                content.writeln("• $item");
              }
            }
          }
          content.writeln();
        }

        // Team Patterns Section
        final teamPatternsSection = cardStructure['team_patterns_section'];
        if (teamPatternsSection != null) {
          final sectionHeader = teamPatternsSection['section_header'];
          if (sectionHeader != null) {
            content.writeln("${sectionHeader['text'] ?? 'Team Patterns:'}");
          }
          content.writeln("• Team patterns and insights would be displayed here");
          content.writeln();
        }
      }
    }

    return content.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Fixed header row containing collection controls
          Padding(
            padding: const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Expand Collection button - Fullscreen
                    Container(
                      width: 32,
                      height: 32,
                      padding: EdgeInsets.only(
                        top: AppSpacing.sm,
                      ),
                      // decoration: BoxDecoration(
                      //   color:
                      //       Color(0xFFF7F9FB), // Light gray background
                      //   borderRadius: BorderRadius.circular(4),
                      // ),
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () {
                            // Toggle expanded/fullscreen mode
                            setState(() {
                              widget.isExpanded = !widget.isExpanded;
                              // // Change the number of widgets per row based on expanded state
                              // if (_isExpanded) {
                              //   // In expanded mode, show 5 widgets per row
                              //   FlexMapper.setTotalRowFlex(5);
                              // } else {
                              //   // In normal mode, show 4 widgets per row
                              //   FlexMapper.setTotalRowFlex(4);
                              // }
                            });
                            if (widget.onExpansionChange != null) {
                              widget.onExpansionChange!();
                            }
                          },
                          child: Center(
                            child: CustomImage.asset(
                              // Always use the expand image from JSON
                              "assets/images/my_business/expand_collection.svg",
                              width: 18,
                              height: 18,
                            ).toWidget(),
                          ),
                        ),
                      ),
                    ),
              
                    SizedBox(
                        width: AppSpacing.md), // Smaller gap between buttons
                    Container(
                      padding: EdgeInsets.only(
                        top: AppSpacing.sm,
                        right: AppSpacing.md,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            height: 20,
                            width: 25,
                            child: Transform.scale(
                              scale: 0.6,
                              child: Switch.adaptive(
                                value: widget.isChatEnabled,
                                activeColor: Colors.white,
                                activeTrackColor: Color(0xff0058FF),
                                inactiveThumbColor: Colors.white,
                                inactiveTrackColor: Colors.grey[300],
                                trackOutlineColor:
                                    WidgetStateProperty.all(Colors.transparent),
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                onChanged: (value) {
                                  setState(() {
                                    widget.isChatEnabled = value;
                                  });

                                  WorkflowTransactionScreen.toggleChatInput(
                                      widget.isChatEnabled);

                                  // Navigate based on toggle state
                                  if (widget.isChatEnabled) {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants
                                            .webTransactionWidgetsDemo;
                                  } else {
                                    Provider.of<WebHomeProvider>(context,
                                                listen: false)
                                            .currentScreenIndex =
                                        ScreenConstants.webCollectionModuleDemo;
                                  }
                                },
                              ),
                            ),
                          ),
                          // SizedBox(
                          //     height:
                          //         4), // Spacing between toggle and label
                          Text(
                            widget.isChatEnabled ? "NSL" : "Normal",
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          Container(
            height: 44,
            child: Row(
              children: List.generate(tabs.length, (index) {
                final isSelected = selectedIndex == index;
                final isTwoTabs = tabs.length == 2;

                return Expanded(
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => setState(() => selectedIndex = index),
                      child: Container(
                        decoration: BoxDecoration(
                            color: isTwoTabs && isSelected
                                ? const Color(0xFFF1F5FB)
                                : Colors.white,
                            border: Border.all(color: Colors.grey.shade300)),
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                              tabs[index]["iconPath"]!,
                              width: MediaQuery.of(context).size.width > 1550
                                  ? 16
                                  : 14,
                              height: MediaQuery.of(context).size.width > 1550
                                  ? 16
                                  : 14,
                              colorFilter: const ColorFilter.mode(
                                  Colors.blue, BlendMode.srcIn),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              tabs[index]["label"]!,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    MediaQuery.of(context).size.width > 1550
                                        ? FontManager.s14
                                        : FontManager.s12,
                                fontWeight: FontManager.semiBold,
                                color: Colors.black,
                                fontFamily: FontManager.fontFamilyInter,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
             padding: const EdgeInsets.symmetric(
                  vertical: AppSpacing.sm, horizontal: AppSpacing.md),
             child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              child: Text(
                tabs[selectedIndex]["content"] ?? "",
                style: FontManager.getCustomStyle(
                  fontSize: MediaQuery.of(context).size.width > 1550
                      ? FontManager.s16
                      : FontManager.s14,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
            ),
                 WebPoReviewRightPanel(),
          ],
        ),
            ),
          ),
        ],
      ),
    );
  }
}
