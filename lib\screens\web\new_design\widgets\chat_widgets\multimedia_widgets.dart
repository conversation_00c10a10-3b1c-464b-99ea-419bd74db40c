import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_dialog.dart';

/// A widget that displays a toggle button for the OCR panel
class OcrPanelToggleButton extends StatelessWidget {
  /// Whether the OCR panel is currently shown
  final bool isShown;

  /// Callback when the toggle button is pressed
  final VoidCallback onToggle;

  /// Constructor
  const OcrPanelToggleButton({
    super.key,
    required this.isShown,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 40,
          child: Container(
            decoration: BoxDecoration(),
            child: Center(
              child: IconButton(
                icon: SvgPicture.asset(
                  'assets/images/chat/toggle_open_close.svg',
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    Colors.grey.shade700,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: onToggle,
                tooltip: isShown ? 'Close Panel' : 'Show Panel',
                padding: EdgeInsets.zero,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// A widget that displays a menu item for the add button menu
class AddButtonMenuItem extends StatelessWidget {
  /// The path to the image asset
  final String imagePath;

  /// The text to display
  final String text;

  /// Callback when the menu item is tapped
  final VoidCallback onTap;

  /// Constructor
  const AddButtonMenuItem({
    super.key,
    required this.imagePath,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        child: Row(
          children: [
            SvgPicture.asset(
              imagePath,
              width: 20,
              height: 20,
              colorFilter: ColorFilter.mode(
                Colors.grey.shade700,
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 12),
            Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// A widget that displays a menu for the add button
class AddButtonMenu extends StatelessWidget {
  /// The parent state
  final State parentState;

  /// The multimedia service
  final MultimediaService multimediaService;

  /// Callback when the menu is removed
  final VoidCallback onRemove;

  /// Constructor
  const AddButtonMenu({
    super.key,
    required this.parentState,
    required this.multimediaService,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Align(
        alignment: Alignment.bottomRight,
        child: Container(
          margin: EdgeInsets.only(bottom: 60, right: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AddButtonMenuItem(
                imagePath: 'assets/images/upload_file.svg',
                text: 'Upload A File',
                onTap: () {
                  // Show file upload dialog
                  FileUploadDialog.show(
                    context,
                    parentState,
                    multimediaService,
                  );
                  onRemove();
                },
              ),
              AddButtonMenuItem(
                imagePath: 'assets/images/screenshot.svg',
                text: 'Take A Screenshot',
                onTap: () {
                  onRemove();
                  // Handle screenshot action
                  Logger.info('Screenshot action not implemented yet');
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
