import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';

/// A reusable component that displays detailed user profile information in a card format.
///
/// This widget is typically used in tooltips or detail panels to show comprehensive
/// user information including profile image, ID, version, creation and modification dates.
class UserProfileCard extends StatelessWidget {
  /// The user's ID
  final String id;

  /// The user's version
  final String version;

  /// The user's display name
  final String displayName;

  /// The user who created this profile
  final String createdBy;

  /// The date when this profile was created
  final String createdDate;

  /// The user who last modified this profile
  final String modifiedBy;

  /// The date when this profile was last modified
  final String modifiedDate;

  /// The role or title of the user
  final String roleTitle;

  /// Description of what the user can access or do
  final String roleDescription;

  /// Path to the profile image asset
  final String profileImagePath;

  /// Width of the card
  final double width;

  /// Left margin of the card
  final double leftMargin;

  /// Background color of the header
  final Color headerColor;

  /// Border color of the properties section
  final Color propertiesBorderColor;

  const UserProfileCard({
    super.key,
    required this.id,
    required this.version,
    this.displayName = 'Administrator',
    required this.createdBy,
    required this.createdDate,
    required this.modifiedBy,
    required this.modifiedDate,
    this.roleTitle = 'CRM Administrator',
    this.roleDescription =
        'Can access Claims Processing, Claims Investigation and Claims Recovery',
    this.profileImagePath = 'assets/images/user_profile_image.png',
    this.width = 0,
    this.leftMargin = 170,
    this.headerColor = const Color(0xFF0058FF), // Colors.blue.shade700
    this.propertiesBorderColor = const Color(0xFF93B8FF),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: leftMargin,
      ),
      width: width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Blue header with icon
          _buildHeader(),

          // Profile image and info
          _buildProfileSection(),

          // Editable Properties section
          _buildPropertiesSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: headerColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white,
                width: 1.5,
              ),
              color: headerColor,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.person_outline,
              color: Colors.white,
              size: 18,
            ),
          ),
          SizedBox(width: AppSpacing.xs),
          Text(
            displayName,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontFamily: 'TiemposText',
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
/// Extract numeric ID from full ID string
String _extractNumericId(String fullId) {
  // Split by underscore and take the last part (the numeric ID)
  final parts = fullId.split('_');
  return parts.isNotEmpty ? parts.last : fullId;
}

  Widget _buildProfileSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Profile image
        Container(
          margin: EdgeInsets.all(AppSpacing.sm),
          width: 80,
          height: 85,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: Image.asset(
              profileImagePath,
              fit: BoxFit.cover,
            ),
          ),
        ),

        // Information container - more compact layout
        Expanded(
          child: Container(
            height: 85,
            margin: EdgeInsets.only(
              top: AppSpacing.sm,
              right: AppSpacing.sm,
              bottom: AppSpacing.sm,
            ),
            padding: EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // First row: ID and Version side by side
                Row(
                  children: [
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'ID: ',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text:
                              _extractNumericId(id),
                              // id,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'Version: ',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: version,
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: AppSpacing.xs),

                // Second row: Created info
                RichText(
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: 'Created: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      TextSpan(
                        text: createdDate, 
                        //'$createdDate by $createdBy',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: AppSpacing.xs),

                // Third row: Modified info
                RichText(
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: 'Modified: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      TextSpan(
                        text:  modifiedDate,
                        //'$modifiedDate by $modifiedBy',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPropertiesSection() {
    return Container(
      margin: const EdgeInsets.only(
        left: 12,
        right: 12,
        bottom: 12,
      ),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: propertiesBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '$roleTitle: ',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                    height: 1.5,
                  ),
                ),
                TextSpan(
                  text: roleDescription,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
