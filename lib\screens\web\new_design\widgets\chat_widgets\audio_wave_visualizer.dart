import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';

/// A widget that displays an animated audio wave visualization
class AudioWaveVisualizer extends StatefulWidget {
  /// The color of the wave
  final Color color;

  /// The height of the wave container
  final double height;

  /// The width of the wave container
  final double width;

  /// The number of bars to display
  final int barCount;

  /// Whether the wave is active (animating)
  final bool isActive;

  const AudioWaveVisualizer({
    super.key,
    this.color = Colors.red,
    this.height = 40,
    this.width = 200,
    this.barCount = 20,
    this.isActive = true,
  });

  @override
  State<AudioWaveVisualizer> createState() => _AudioWaveVisualizerState();
}

class _AudioWaveVisualizerState extends State<AudioWaveVisualizer> {
  late List<double> _barHeights;
  Timer? _animationTimer;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    // Initialize bar heights
    _initializeBarHeights();
    // Start animation if active
    if (widget.isActive) {
      _startAnimation();
    }
  }

  @override
  void didUpdateWidget(AudioWaveVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Handle changes in isActive state
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    _stopAnimation();
    super.dispose();
  }

  void _initializeBarHeights() {
    _barHeights = List.generate(
      widget.barCount,
      (_) => _generateRandomHeight(),
    );
  }

  double _generateRandomHeight() {
    // Generate a random height between 0.1 and 0.9
    return 0.1 + _random.nextDouble() * 0.8;
  }

  void _startAnimation() {
    // Cancel any existing timer
    _stopAnimation();

    // Create a new timer that updates the bar heights
    _animationTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      if (mounted) {
        setState(() {
          // Update a random subset of bars
          final barsToUpdate = max(widget.barCount ~/ 3, 1);
          for (int i = 0; i < barsToUpdate; i++) {
            final barIndex = _random.nextInt(widget.barCount);
            _barHeights[barIndex] = _generateRandomHeight();
          }
        });
      }
    });
  }

  void _stopAnimation() {
    _animationTimer?.cancel();
    _animationTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          widget.barCount,
          (index) => _buildBar(index),
        ),
      ),
    );
  }

  Widget _buildBar(int index) {
    final barHeight = _barHeights[index] * widget.height;

    return Container(
      width: (widget.width / widget.barCount) *
          0.6, // Leave some space between bars
      height: barHeight,
      decoration: BoxDecoration(
        color: widget.color,
        borderRadius: BorderRadius.circular(1.5),
      ),
    );
  }
}
