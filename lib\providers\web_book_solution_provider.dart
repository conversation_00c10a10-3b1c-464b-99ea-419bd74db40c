import 'package:flutter/material.dart';
import 'base_provider.dart';

class WebBookSolutionProvider extends BaseProvider {
  // Solution items and options
  List<SolutionItem> _solutionItems = [];
  List<String> _solutionOptions = [];

  // Hover state variables
  bool _isAddModulesHeaderButtonHovering = false;
  bool _isPaginationLeftChevronHovering = false;
  bool _isPaginationRightChevronHovering = false;
  bool _isModulesButtonHovering = false;
  final TextEditingController _chatController = TextEditingController();

  // State for showing input field
  bool _showAddModules = false;

  // Module popup state
  final LayerLink _modulesButtonLink = LayerLink();
  OverlayEntry? _modulesPopupEntry;
  final TextEditingController _moduleInputController = TextEditingController();
  bool _showModulesPopup = false;

  // Updated module structure to support submodules and expansion state
  List<ModuleItem> _modules = []; // Initialize as empty list

  // Add state for module popup
  int? _activeModulePopupIndex;
  final TextEditingController _moduleRowInputController =
      TextEditingController();

  // Selected module for showing submodules in third column
  int? _selectedModuleIndex;

  // Submodule popup state
  OverlayEntry? _submodulePopupEntry;
  final GlobalKey _submoduleButtonKey = GlobalKey();
  final GlobalKey _modulesColumnKey = GlobalKey();

  // Keys for submodule buttons
  List<GlobalKey> _submoduleButtonKeys = [];

  // Module button key
  final GlobalKey _modulesButtonKey = GlobalKey();

  // Pagination properties
  final int _itemsPerPage = 12;
  int _currentPage = 1;
  int get itemsPerPage => _itemsPerPage;
  int get currentPage => _currentPage;
  int get totalPages => (_solutionOptions.length / _itemsPerPage).ceil();

  List<String> get currentPageItems {
    final startIndex = (_currentPage - 1) * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;
    if (startIndex >= _solutionOptions.length) return [];
    return _solutionOptions.sublist(
        startIndex,
        endIndex > _solutionOptions.length
            ? _solutionOptions.length
            : endIndex);
  }

  void nextPage() {
    if (_currentPage < totalPages) {
      _currentPage++;
      notifyListeners();
    }
  }

  void previousPage() {
    if (_currentPage > 1) {
      _currentPage--;
      notifyListeners();
    }
  }

  // Getters
  List<SolutionItem> get solutionItems => _solutionItems;
  List<String> get solutionOptions => _solutionOptions;
  bool get isAddModulesHeaderButtonHovering =>
      _isAddModulesHeaderButtonHovering;
  bool get isPaginationLeftChevronHovering => _isPaginationLeftChevronHovering;
  bool get isPaginationRightChevronHovering =>
      _isPaginationRightChevronHovering;
  bool get isModulesButtonHovering => _isModulesButtonHovering;
  TextEditingController get chatController => _chatController;
  bool get showAddModules => _showAddModules;
  LayerLink get modulesButtonLink => _modulesButtonLink;
  OverlayEntry? get modulesPopupEntry => _modulesPopupEntry;
  TextEditingController get moduleInputController => _moduleInputController;
  bool get showModulesPopup => _showModulesPopup;
  List<ModuleItem> get modules => _modules;
  int? get activeModulePopupIndex => _activeModulePopupIndex;
  TextEditingController get moduleRowInputController =>
      _moduleRowInputController;
  OverlayEntry? get submodulePopupEntry => _submodulePopupEntry;
  GlobalKey get submoduleButtonKey => _submoduleButtonKey;
  GlobalKey get modulesColumnKey => _modulesColumnKey;
  List<GlobalKey> get submoduleButtonKeys => _submoduleButtonKeys;
  GlobalKey get modulesButtonKey => _modulesButtonKey;
  int? get selectedModuleIndex => _selectedModuleIndex;

  WebBookSolutionProvider() {
    _initializeData();
  }

  void _initializeData() {
    // Initialize with sample data
    _solutionItems = [
      SolutionItem(
        title: 'Create a solution of a Product management',
        lastMessageTime: '18 hours ago',
        date: '22/04/2023',
        versionId: 'V00512',
        description: 'This is a sample description for the solution.',
      ),
      // Add more items as needed
    ];

    // Initialize solution options for the modal (increased to show pagination)
    _solutionOptions = List.generate(50, (index) => 'Solutions');

    // Initialize submodule button keys for empty modules list
    _submoduleButtonKeys = [];
  }

  // Setters with notifyListeners
  set isAddModulesHeaderButtonHovering(bool value) {
    _isAddModulesHeaderButtonHovering = value;
    notifyListeners();
  }

  set isPaginationLeftChevronHovering(bool value) {
    _isPaginationLeftChevronHovering = value;
    notifyListeners();
  }

  set isPaginationRightChevronHovering(bool value) {
    _isPaginationRightChevronHovering = value;
    notifyListeners();
  }

  set isModulesButtonHovering(bool value) {
    _isModulesButtonHovering = value;
    notifyListeners();
  }

  set showAddModules(bool value) {
    _showAddModules = value;
    notifyListeners();
  }

  set modulesPopupEntry(OverlayEntry? entry) {
    _modulesPopupEntry = entry;
    notifyListeners();
  }

  set showModulesPopup(bool value) {
    _showModulesPopup = value;
    notifyListeners();
  }

  set activeModulePopupIndex(int? index) {
    _activeModulePopupIndex = index;
    notifyListeners();
  }

  set submodulePopupEntry(OverlayEntry? entry) {
    _submodulePopupEntry = entry;
    notifyListeners();
  }

  void toggleAddModules() {
    _showAddModules = !_showAddModules;
    notifyListeners();
  }

  void addModule() {
    final value = _moduleInputController.text.trim();
    if (value.isNotEmpty) {
      // Add the new module
      _modules.add(ModuleItem(name: value, isExpanded: false));
      // Update submodule button keys to match new length
      _submoduleButtonKeys = List.generate(_modules.length, (_) => GlobalKey());
      _moduleInputController.clear();
      // Close the popup dialog after successfully adding the module
      hideModulesPopupMenu();
      notifyListeners();
    }
  }

  void addSubModule(int moduleIndex) {
    final value = _moduleRowInputController.text.trim();
    if (value.isNotEmpty && moduleIndex < _modules.length) {
      // Add the submodule to the specific module
      _modules[moduleIndex].submodules.add(value);
      _moduleRowInputController.clear();
      // Close the submodule popup
      hideSubmodulePopup();
      notifyListeners();
    }
  }

  void updateSubmoduleButtonKeys() {
    if (_submoduleButtonKeys.length != _modules.length) {
      _submoduleButtonKeys = List.generate(_modules.length, (_) => GlobalKey());
      notifyListeners();
    }
  }

  void showModulesPopupMenu(BuildContext context) {
    if (_modulesPopupEntry != null) return;
    final overlay = Overlay.of(context);
    RenderBox button =
        _modulesButtonKey.currentContext!.findRenderObject() as RenderBox;
    final position = button.localToGlobal(Offset.zero);
    _modulesPopupEntry = OverlayEntry(
      builder: (context) => Positioned(
          left: position.dx - 10, // Centered under button, adjust as needed
          top: position.dy + button.size.height + 8,
          child: CompositedTransformFollower(
            link: _modulesButtonLink,
            showWhenUnlinked: false,
            offset: Offset(0, button.size.height + 8),
            child: Material(
              color: Colors.transparent,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.183,
                padding:
                    EdgeInsets.only(top: 16, left: 16, right: 16, bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Color(0xFFBDBDBD), width: 1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                            height: 20), // Add gap at the top of the container
                        TextField(
                          controller: _moduleInputController,
                          decoration: InputDecoration(
                            hintText: 'Type here',
                            hintStyle: TextStyle(
                              color: Color(0xFFBDBDBD),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'TiemposText',
                            ),
                            border: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Color(0xFF707070), width: 1),
                            ),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Color(0xFF707070), width: 1),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(
                                  color: Color(0xFF707070), width: 1),
                            ),
                            filled: false,
                            isDense: true,
                            contentPadding: EdgeInsets.only(bottom: 8),
                          ),
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: 'TiemposText'),
                          cursorColor: Color(0xFF377DFF),
                          autofocus: true,
                          onSubmitted: (_) => addModule(),
                        ),
                        SizedBox(
                            height:
                                20), // Increased gap above input for better spacing
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            _HoverIconButton(
                              icon: Icons.close,
                              color: Colors.black,
                              size: 20,
                              onTap: () {
                                _moduleInputController.clear();
                                hideModulesPopupMenu();
                              },
                            ),
                            SizedBox(width: 16),
                            _HoverIconButton(
                              icon: Icons.check,
                              color: Color(0xFF377DFF),
                              size: 20,
                              hoverColor:
                                  Color(0xFF0058FF), // Updated hover color
                              hoverBorder: Border.all(
                                  color: Color(0xFFEAF4FF),
                                  width: 1.5), // Added hover border
                              onTap: addModule,
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          )),
    );
    overlay.insert(_modulesPopupEntry!);
    _showModulesPopup = true;
    notifyListeners();
  }

  void hideModulesPopupMenu() {
    _modulesPopupEntry?.remove();
    _modulesPopupEntry = null;
    _showModulesPopup = false;
    notifyListeners();
  }

  void showSubmodulePopup(int moduleIndex) {
    if (_submodulePopupEntry != null) return;
    if (moduleIndex >= _submoduleButtonKeys.length) return;

    final context = _submoduleButtonKeys[moduleIndex].currentContext;
    if (context == null) return;

    final overlay = Overlay.of(context);
    RenderBox button = context.findRenderObject() as RenderBox;
    final position = button.localToGlobal(Offset.zero);

    _activeModulePopupIndex = moduleIndex;

    // Get the size of the CRM module column
    final RenderBox? modulesColumn =
        _modulesColumnKey.currentContext?.findRenderObject() as RenderBox?;
    final moduleColumnWidth = modulesColumn?.size.width ??
        220; // Use 220px as default based on the design

    _submodulePopupEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: (() {
          final screenWidth = MediaQuery.of(context).size.width;
          // Show popup inside the column for screen widths <= 1366
          if (screenWidth <= 1366) {
            // Clamp left to at least 16px from left edge, or align with button
            return position.dx
                .clamp(16.0, screenWidth - moduleColumnWidth - 16.0);
          } else {
            // For larger screens, show popup outside (to the left)
            return position.dx - 340;
          }
        })(),
        top: position.dy +
            button.size.height, // Position below the CRM text to match design
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: moduleColumnWidth, // Use actual column width
            padding: EdgeInsets.only(top: 16, left: 16, right: 16, bottom: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Color(0xFFBDBDBD), width: 1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20),
                    TextField(
                      controller: _moduleRowInputController,
                      decoration: InputDecoration(
                        hintText: 'Type here',
                        hintStyle: TextStyle(
                          color: Color(0xFFBDBDBD),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          fontFamily: 'TiemposText',
                        ),
                        border: UnderlineInputBorder(
                          borderSide:
                              BorderSide(color: Color(0xFF707070), width: 1),
                        ),
                        enabledBorder: UnderlineInputBorder(
                          borderSide:
                              BorderSide(color: Color(0xFF707070), width: 1),
                        ),
                        focusedBorder: UnderlineInputBorder(
                          borderSide:
                              BorderSide(color: Color(0xFF707070), width: 1),
                        ),
                        filled: false,
                        isDense: true,
                        contentPadding: EdgeInsets.only(bottom: 8),
                      ),
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.black,
                          fontFamily: 'TiemposText'),
                      cursorColor: Color(0xFF377DFF),
                      autofocus: true,
                      onSubmitted: (_) => addSubModule(moduleIndex),
                    ),
                    SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _HoverIconButton(
                          icon: Icons.close,
                          color: Colors.black,
                          size: 20,
                          onTap: () {
                            _moduleRowInputController.clear();
                            hideSubmodulePopup();
                          },
                        ),
                        SizedBox(width: 16),
                        _HoverIconButton(
                          icon: Icons.check,
                          color: Color(0xFF377DFF),
                          size: 20,
                          hoverColor: Color(0xFF0058FF),
                          hoverBorder:
                              Border.all(color: Color(0xFFEAF4FF), width: 1.5),
                          onTap: () => addSubModule(moduleIndex),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(_submodulePopupEntry!);
    notifyListeners();
  }

  void hideSubmodulePopup() {
    _submodulePopupEntry?.remove();
    _submodulePopupEntry = null;
    _activeModulePopupIndex = null;
    _moduleRowInputController.clear();
    notifyListeners();
  }

  void setActiveModulePopupIndex(int? index) {
    _activeModulePopupIndex = index;
    _moduleRowInputController.clear();
    notifyListeners();
  }

  void selectModule(int? index) {
    _selectedModuleIndex = index;
    notifyListeners();
  }

  @override
  void dispose() {
    _chatController.dispose();
    _moduleInputController.dispose();
    _moduleRowInputController.dispose();
    super.dispose();
  }
}

class SolutionItem {
  final String title;
  final String lastMessageTime;
  final String date;
  final String versionId;
  final String description; // Added description property

  SolutionItem({
    required this.title,
    required this.lastMessageTime,
    required this.date,
    required this.versionId,
    required this.description, // Initialize description
  });
}

class ModuleItem {
  final String name;
  final List<String> submodules;
  bool isExpanded;

  ModuleItem({
    required this.name,
    List<String>? submodules,
    this.isExpanded = false,
  }) : submodules = submodules ?? [];
}

class _HoverIconButton extends StatefulWidget {
  final IconData icon;
  final Color color;
  final double size;
  final Color? hoverColor; // Nullable hoverColor
  final Border? hoverBorder; // Nullable hoverBorder
  final VoidCallback onTap;
  const _HoverIconButton({
    required this.icon,
    required this.color,
    required this.size,
    this.hoverColor, // Initialize hoverColor as nullable
    this.hoverBorder, // Initialize hoverBorder as nullable
    required this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  State<_HoverIconButton> createState() => _HoverIconButtonState();
}

class _HoverIconButtonState extends State<_HoverIconButton> {
  bool _hovering = false;
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _hovering = true),
      onExit: (_) => setState(() => _hovering = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: Duration(milliseconds: 120),
          decoration: BoxDecoration(
            color: _hovering ? Color(0xFFF5F7FA) : Colors.transparent,
            border: _hovering
                ? Border.all(color: Color(0xFFBDBDBD))
                : Border.all(color: Colors.transparent),
            borderRadius: BorderRadius.circular(4),
          ),
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            widget.icon,
            size: 20, // Decreased size to 14
            color: widget.color,
          ),
        ),
      ),
    );
  }
}
