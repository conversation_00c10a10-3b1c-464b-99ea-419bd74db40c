import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_ellipsis.dart';
import 'package:nsl/utils/logger.dart';

/// A reusable entity table widget that displays entity data in a table format.
///
/// This widget shows entity information extracted from validation results
/// and provides interaction capabilities for entity selection.
class EntityTable extends StatefulWidget {
  /// The manual creation provider containing entity data
  final ManualCreationProvider provider;

  /// Callback when an entity is selected
  final Function(entities_model.Entity)? onEntitySelected;

  /// The current selected section index for entity details
  final int selectedSectionIndex;

  /// Callback when section index changes
  final Function(int)? onSectionIndexChanged;

  const EntityTable({
    super.key,
    required this.provider,
    this.onEntitySelected,
    this.selectedSectionIndex = 0,
    this.onSectionIndexChanged,
  });

  @override
  State<EntityTable> createState() => _EntityTableState();
}

class _EntityTableState extends State<EntityTable> {
  DateTime _lastClickTime = DateTime.now();

  @override
  Widget build(BuildContext context) {
    if (widget.provider.extractedEntityData == null ||
        widget.provider.extractedEntityData!.entityGroups == null ||
        widget.provider.extractedEntityData!.entityGroups!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No entity data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Entities and Relationships',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${widget.provider.extractedEntityData!.entityGroups!.length} Entity Group${widget.provider.extractedEntityData!.entityGroups!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Entity rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount:
                  widget.provider.extractedEntityData!.entityGroups!.length,
              itemBuilder: (context, groupIndex) {
                final group = widget
                    .provider.extractedEntityData!.entityGroups![groupIndex];

                return Container(
                  // padding:
                  //     const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    color: Colors.white,
                  ),
                  child: Column(
                    children: (group.entities ?? []).map<Widget>((entity) {
                      // Create a non-nullable list for the _buildEntityCard method
                      final List<entities_model.Entity> entityList =
                          group.entities ?? [];
                      return _buildEntityCard(entity, entityList, group);
                    }).toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntityCard(
      entities_model.Entity entity,
      List<entities_model.Entity> allEntities,
      entities_model.EntityGroup group) {
    // If this is a relationship entity, hide it
    if (entity.relationType != null) {
      return SizedBox.shrink();
    }

    final bool isSelected = widget.provider.selectedEntity != null &&
        widget.provider.selectedEntity!.id == entity.id;

    // Create a unique global key for this entity card
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'entityCard_${entity.id}');

    // Check if the entire title row would be truncated
    bool wouldBeTruncated = false;

    double availableWidth = MediaQuery.of(context).size.width / 1.55;

    // Create a combined string of the entity title and attributes for truncation check
    String fullTitleText = entity.title ?? 'Untitled';
    if (entity.attributeString != null && entity.attributeString!.isNotEmpty) {
      fullTitleText += ' has ${entity.attributeString!}';
    }

    // Check if the combined text would be truncated
    wouldBeTruncated = _wouldTextBeTruncated(
        fullTitleText,
        TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        availableWidth);

    return Column(
      children: [
        Container(
        //  padding: EdgeInsets.all(0),
          decoration: BoxDecoration(
            color: isSelected ? Color(0xFFE3F2FD) : Colors.white,
            border: Border(
            //  top: BorderSide(color: Colors.grey.shade200),
              left: isSelected
                  ? BorderSide(color: Colors.blue.shade700, width: 4)
                  : BorderSide.none,
            ),
          ),
          child: Column(
            children: [
              CustomExpansionTileWithEllipsis(
                entity: entity,
                entityCardKey: entityCardKey,
                onExpansionChanged: (expanded) {
                  setState(() {
                    // Update the local entity for immediate UI response
                    entity.expanded = expanded;

                    // Update the entity's expanded state in the global data
                    widget.provider.extractedEntityData
                        ?.updateEntityExpandedState(entity.id ?? '', expanded);
                  });
                },
                backgroundColor: Colors.transparent,
                onThreeDotsPressed: () {
                  // Handle three dots menu action for entity
                  Logger.info('Three dots pressed for entity: ${entity.title}');
                },
                onTitleTap: () {
                  // Update last click time to prevent tooltip from showing
                  _lastClickTime = DateTime.now();

                  setState(() {
                    if (widget.provider.selectedEntity != null &&
                        widget.provider.selectedEntity!.id == entity.id) {
                      // Keep selected
                    } else {
                      widget.onEntitySelected?.call(entity);
                    }
                    widget.onSectionIndexChanged?.call(0);
                  });
                },
                showThreeDots: false,
                children: [],
              )
            ],
          ),
        ),
      ],
    );
  }

  bool _wouldTextBeTruncated(String text, TextStyle style, double maxWidth) {
    // First, measure the actual width of the text without constraints
    final TextPainter measuringPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    // Get the actual width of the text
    final double actualTextWidth = measuringPainter.width;

    // Return true if the actual width exceeds the maximum width
    return actualTextWidth > maxWidth;
  }
}
