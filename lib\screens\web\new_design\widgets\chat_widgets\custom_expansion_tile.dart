// Custom ExpansionTile that separates title tap from expansion toggle
import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_new.dart';

class CustomExpansionTile extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final Color backgroundColor;

  const CustomExpansionTile({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.backgroundColor = Colors.white,
  });

  @override
  State<CustomExpansionTile> createState() => CustomExpansionTileState();
}

class CustomExpansionTileState extends State<CustomExpansionTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurn;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));
    _iconTurn = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeIn)));
    _isExpanded = widget.initiallyExpanded;
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Title row with combined tap handlers
        !_isExpanded
            ? Container(
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  color: Color(0xffF7F9FB),
                  // color: Colors.grey.shade50,
                  border: Border(
                    bottom: BorderSide(
                      color: _isExpanded
                          ? Colors.grey.shade200
                          : Colors.transparent,
                      width: 1.0,
                    ),
                  ),
                ),
                child: InkWell(
                  onTap: () {
                    // Call both onTitleTap and toggle expansion when title is tapped
                    widget.onTitleTap();
                    _toggleExpansion();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize:
                        _isExpanded ? MainAxisSize.max : MainAxisSize.min,
                    children: [
                      widget.title,
                      // Arrow icon that toggles expansion
                      Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 7.0),
                        child: HoverArrowIcon(
                          onTap: _toggleExpansion,
                          iconTurn: _iconTurn,
                        ),
                      )
                    ],
                  ),
                ),
              )
            : SizedBox(),
        // Expandable content
        Transform.translate(
          offset: Offset(0, -2),
          child: AnimatedBuilder(
            animation: _controller.view,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  heightFactor: _heightFactor.value,
                  alignment: Alignment.topCenter, // Center align for full width
                  child: child,
                ),
              );
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                // border: Border(
                //   top: BorderSide(
                //     color: Colors.grey.shade200,
                //     width: 1,
                //   ),
                // ),
              ),
              // Width is full when expanded
              width: _isExpanded ? double.infinity : null,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      // Call both onTitleTap and toggle expansion when title is tapped
                      widget.onTitleTap();
                      _toggleExpansion();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      mainAxisSize:
                          _isExpanded ? MainAxisSize.max : MainAxisSize.min,
                      children: [
                        widget.title,
                        // Arrow icon that toggles expansion
                        Container(
                          color: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 9.0),
                          child: HoverArrowIcon(
                            onTap: _toggleExpansion,
                            iconTurn: _iconTurn,
                          ),
                        )
                      ],
                    ),
                  ),
                  ...widget.children.map((widget) => widget)
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
