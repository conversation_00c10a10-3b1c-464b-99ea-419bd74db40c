import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../models/nsl_hierarchy_model.dart';

class NSLHierarchyChart extends StatefulWidget {
  final NSLNode rootNode;
  final Function(NSLNode)? onNodeTap;
  final double horizontalSpacing;
  final double verticalSpacing;
  final double lineThickness;
  final Color lineColor;

  const NSLHierarchyChart({
    super.key,
    required this.rootNode,
    this.onNodeTap,
    this.horizontalSpacing = 120.0,
    this.verticalSpacing = 80.0,
    this.lineThickness = 2.0,
    this.lineColor = const Color(0xFF4ECDC4),
  });

  @override
  State<NSLHierarchyChart> createState() => _NSLHierarchyChartState();
}

class _NSLHierarchyChartState extends State<NSLHierarchyChart> {
  late NSLNode _rootNode;
  NSLNode? _selectedNode;
  List<String> _selectedPathIds = [];
  final TransformationController _transformationController = TransformationController();
  final Map<String, double> _subtreeWidths = {};

  // Responsive card dimensions - moved to methods to avoid context issues
  double _getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 200.0; // Mobile
    if (screenWidth < 1024) return 220.0; // Tablet
    return 240.0; // Desktop
  }

  double _getCardHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 100.0; // Mobile
    if (screenWidth < 1024) return 110.0; // Tablet
    return 120.0; // Desktop
  }

  // Cached values for current build
  late double _cardWidth;
  late double _cardHeight;

  @override
  void initState() {
    super.initState();
    _rootNode = widget.rootNode;
    // Initialize with default values - will be recalculated in build
    _cardWidth = 240.0;
    _cardHeight = 120.0;
    _calculateAllSubtreeWidths(_rootNode);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _transformationController.value = Matrix4.identity()
        ..scale(0.8, 0.8)  
        ..translate(25.0, 25.0);
    });
  }

  @override
  void didUpdateWidget(NSLHierarchyChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.rootNode != widget.rootNode) {
      _rootNode = widget.rootNode;
      setState(() {});
    }
  }

  void _calculateAllSubtreeWidths(NSLNode node) {
    _getSubtreeWidth(node);
  }

  double _getSubtreeWidth(NSLNode node) {
    if (_subtreeWidths.containsKey(node.id)) {
      return _subtreeWidths[node.id]!;
    }

    double width;
    if (!node.isExpanded || node.children.isEmpty) {
      width = _cardWidth;
    } else {
      double childrenCombinedWidth = 0;
      for (int i = 0; i < node.children.length; i++) {
        childrenCombinedWidth += _getSubtreeWidth(node.children[i]);
      }
      width = childrenCombinedWidth + (node.children.length - 1) * widget.horizontalSpacing;
      if (width < _cardWidth) {
        width = _cardWidth;
      }
    }
    _subtreeWidths[node.id] = width;
    return width;
  }

  @override
  Widget build(BuildContext context) {
    // Initialize responsive dimensions for this build
    final newCardWidth = _getCardWidth(context);
    final newCardHeight = _getCardHeight(context);
    
    // Check if dimensions changed and recalculate if needed
    if (_cardWidth != newCardWidth || _cardHeight != newCardHeight) {
      _cardWidth = newCardWidth;
      _cardHeight = newCardHeight;
      _subtreeWidths.clear();
      _calculateAllSubtreeWidths(_rootNode);
    }
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          InteractiveViewer(
            transformationController: _transformationController,
            constrained: false,
            minScale: 0.1,
            maxScale: 2.0,
            child: Container(
              width: 5000,
              height: 3000,
              color: Colors.white,
              child: _buildOrganizationTree(_rootNode),
            ),
          ),
          _buildZoomControls(),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 1,
            )
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.add, color: Colors.black54),
              onPressed: () => _zoom(1.2),
              tooltip: 'Zoom In',
            ),
            SizedBox(
              height: 30,
              width: 2,
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(height: 30, width: 1.5, color: Colors.grey.shade400),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: const Color(0xFFA5D6A7),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.remove, color: Colors.black54),
              onPressed: () => _zoom(0.8),
              tooltip: 'Zoom Out',
            ),
          ],
        ),
      ),
    );
  }

  void _zoom(double zoomFactor) {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    double newScale = currentScale * zoomFactor;
    newScale = newScale.clamp(0.1, 2.0);
    final relativeScaleFactor = newScale / currentScale;
    final Matrix4 newMatrix = _transformationController.value.clone()
      ..scale(relativeScaleFactor, relativeScaleFactor);
    _transformationController.value = newMatrix;
  }

  Widget _buildOrganizationTree(NSLNode rootNode) {
    final nodesInfo = _calculateNodePositions(rootNode);
    final nodeWidgets = _buildAllNodes(rootNode, nodesInfo);
    final expandCollapseButtons = _generateExpandCollapseButtonWidgets(rootNode, nodesInfo);
    final levelLabels = _buildLevelLabels(rootNode, nodesInfo);

    return Stack(
      children: [
        CustomPaint(
          painter: NSLConnectionsPainter(
            rootNode: _rootNode,
            nodePositions: nodesInfo,
            selectedPathIds: _selectedPathIds,
            defaultLineColor: widget.lineColor,
            lineThickness: widget.lineThickness,
            verticalSpacing: widget.verticalSpacing,
            highlightColor: Colors.deepOrangeAccent,
          ),
          child: Container(),
        ),
        ...levelLabels,
        ...nodeWidgets,
        ...expandCollapseButtons,
      ],
    );
  }

  List<Widget> _buildLevelLabels(NSLNode rootNode, Map<String, NSLNodePosition> nodesInfo) {
    final List<Widget> labels = [];
    final Map<String, double> levelYPositions = {};
    
    // Collect Y positions for each level
    void collectLevelPositions(NSLNode node) {
      final position = nodesInfo[node.id];
      if (position != null) {
        final currentY = levelYPositions[node.level];
        if (currentY == null || position.y < currentY) {
          levelYPositions[node.level] = position.y;
        }
      }
      for (var child in node.children) {
        collectLevelPositions(child);
      }
    }
    
    collectLevelPositions(rootNode);
    
    // Create labels for each level
    levelYPositions.forEach((level, yPosition) {
      final levelName = _getLevelName(level);
      labels.add(
        Positioned(
          left: 50, // Increased left spacing
          top: yPosition + 40, // Center vertically with the cards
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: NSLNode.getLevelColor(level).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: NSLNode.getLevelColor(level),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  '$level ',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: NSLNode.getLevelColor(level),
                    fontFamily: 'TiemposText',
                  ),
                ),
                SizedBox(height:2),
                
                Text(
                  '($levelName)',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: NSLNode.getLevelColor(level),
                    fontFamily: 'TiemposText',
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
    
    return labels;
  }

  String _getLevelName(String level) {
    switch (level) {
      case 'M4':
        return 'Executive';
      case 'M3':
        return 'Departments';
      case 'M2':
        return 'Teams';
      case 'M1':
        return 'Individual Employees';
      default:
        return 'Unknown';
    }
  }

  List<Widget> _generateExpandCollapseButtonWidgets(NSLNode currentNode, Map<String, NSLNodePosition> nodesInfo) {
    List<Widget> buttons = [];
    
    void dfs(NSLNode node) {
      if (node.children.isNotEmpty) {
        final positionInfo = nodesInfo[node.id];
        if (positionInfo != null) {
          buttons.add(_buildSingleExpandCollapseButton(node, positionInfo));
        }
      }
      for (var child in node.children) {
        dfs(child);
      }
    }

    dfs(currentNode);
    return buttons;
  }

  Widget _buildSingleExpandCollapseButton(NSLNode node, NSLNodePosition positionInfo) {
    final bool isExpanded = node.isExpanded;
    final String buttonText = isExpanded ? 'Collapse' : '+${node.children.length}';
    final IconData buttonIcon = isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down;
    
    final double buttonWidth = isExpanded ? 100 : 60;
    final double buttonHeight = 28;
    final double spacingBelowCard = 8.0;

    final buttonTop = positionInfo.bottomCenter.dy + spacingBelowCard;
    final buttonLeft = positionInfo.bottomCenter.dx - (buttonWidth / 2);

    return Positioned(
      left: buttonLeft,
      top: buttonTop,
      child: GestureDetector(
        onTap: () {
          final newRoot = _toggleNodeExpansionRecursive(_rootNode, node.id);
          _subtreeWidths.clear();
          _calculateAllSubtreeWidths(newRoot);
          setState(() {
            _rootNode = newRoot;
          });
        },
        child: Container(
          width: buttonWidth,
          height: buttonHeight,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14),
            border: Border.all(color: Colors.grey.shade300, width: 1.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                buttonText,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.blueGrey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isExpanded) const SizedBox(width: 4),
              Icon(
                buttonIcon,
                size: 16,
                color: Colors.blueGrey.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildAllNodes(NSLNode node, Map<String, NSLNodePosition> nodesInfo) {
    final List<Widget> allWidgets = [];
    final position = nodesInfo[node.id];
    
    if (position != null) {
      allWidgets.add(
        Positioned(
          left: position.x,
          top: position.y,
          child: _buildNodeWidget(node),
        ),
      );
      
      if (node.isExpanded) {
        for (final child in node.children) {
          allWidgets.addAll(_buildAllNodes(child, nodesInfo));
        }
      }
    }
    
    return allWidgets;
  }

  Widget _buildNodeWidget(NSLNode node) {
    return NSLCard(
      node: node,
      isSelected: _selectedNode?.id == node.id,
      isHighlightedInPath: _selectedPathIds.contains(node.id),
      onTap: () {
        setState(() {
          if (_selectedNode?.id == node.id) {
            _selectedNode = null;
            _selectedPathIds = [];
          } else {
            _selectedNode = node;
            _selectedPathIds = _getPathToNode(node.id);
          }
        });
        widget.onNodeTap?.call(node);
      },
    );
  }

  NSLNode _toggleNodeExpansionRecursive(NSLNode currentNode, String targetNodeId) {
    if (currentNode.id == targetNodeId) {
      return currentNode.copyWith(isExpanded: !currentNode.isExpanded);
    }

    final List<NSLNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild = _toggleNodeExpansionRecursive(child, targetNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }

    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  Map<String, NSLNodePosition> _calculateNodePositions(NSLNode rootNode) {
    final Map<String, NSLNodePosition> positions = {};

    const rootX = 250.0; // Increased space for level labels
    const rootY = 0.0;

    final rootSubtreeWidth = _subtreeWidths[rootNode.id] ?? _cardWidth;
    final rootCardX = rootX + (rootSubtreeWidth / 2) - (_cardWidth / 2);

    positions[rootNode.id] = NSLNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootY,
      width: _cardWidth,
      height: _cardHeight,
    );

    _calculateChildPositions(rootNode, positions, rootCardX, rootY, _cardWidth, _cardHeight);

    return positions;
  }

  void _calculateChildPositions(
    NSLNode parent,
    Map<String, NSLNodePosition> positions,
    double parentCardX,
    double parentCardY,
    double parentCardWidth,
    double parentCardHeight,
  ) {
    if (!parent.isExpanded || parent.children.isEmpty) return;

    final numberOfChildren = parent.children.length;
    final childY = parentCardY + parentCardHeight + widget.verticalSpacing;

    double totalChildrenBlockWidth = 0;
    for (final child in parent.children) {
      totalChildrenBlockWidth += (_subtreeWidths[child.id] ?? _cardWidth);
    }
    if (numberOfChildren > 1) {
      totalChildrenBlockWidth += (numberOfChildren - 1) * widget.horizontalSpacing;
    }

    final startXForChildrenBlock = parentCardX + (parentCardWidth / 2) - (totalChildrenBlockWidth / 2);
    double currentXOffset = startXForChildrenBlock;

    for (int i = 0; i < numberOfChildren; i++) {
      final child = parent.children[i];
      final childSubtreeWidth = _subtreeWidths[child.id] ?? _cardWidth;

      final childCardX = currentXOffset + (childSubtreeWidth / 2) - (_cardWidth / 2);

      positions[child.id] = NSLNodePosition(
        id: child.id,
        x: childCardX,
        y: childY,
        width: _cardWidth,
        height: _cardHeight,
        parentId: parent.id,
      );

      _calculateChildPositions(
        child,
        positions,
        childCardX,
        childY,
        _cardWidth,
        _cardHeight,
      );

      currentXOffset += childSubtreeWidth + widget.horizontalSpacing;
    }
  }

  List<String> _getPathToNode(String targetNodeId) {
    List<String> currentPath = [];
    bool findPathRecursive(NSLNode currentNode) {
      currentPath.add(currentNode.id);
      if (currentNode.id == targetNodeId) {
        return true;
      }
      for (final child in currentNode.children) {
        if (findPathRecursive(child)) {
          return true;
        }
      }
      currentPath.removeLast();
      return false;
    }

    findPathRecursive(_rootNode);
    return currentPath;
  }
}

class NSLNodePosition {
  final String id;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? parentId;

  NSLNodePosition({
    required this.id,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.parentId,
  });

  Offset get bottomCenter => Offset(x + width / 2, y + height);
  Offset get topCenter => Offset(x + width / 2, y);
}

class NSLCard extends StatelessWidget {
  final NSLNode node;
  final bool isSelected;
  final bool isHighlightedInPath;
  final VoidCallback onTap;

  const NSLCard({
    super.key,
    required this.node,
    required this.isSelected,
    required this.isHighlightedInPath,
    required this.onTap,
  });

  // Responsive dimensions and positioning
  double _getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 200.0; // Mobile
    if (screenWidth < 1024) return 220.0; // Tablet
    return 240.0; // Desktop
  }

  double _getCardHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 100.0; // Mobile
    if (screenWidth < 1024) return 110.0; // Tablet
    return 120.0; // Desktop
  }

  double _getFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 12.0; // Mobile
    if (screenWidth < 1024) return 13.0; // Tablet
    return 14.0; // Desktop
  }

  double _getPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 8.0; // Mobile
    if (screenWidth < 1024) return 10.0; // Tablet
    return 12.0; // Desktop
  }

  double _getHorizontalMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) return 15.0; // Mobile
    if (screenWidth < 1024) return 18.0; // Tablet
    return 20.0; // Desktop
  }

  @override
  Widget build(BuildContext context) {
    final cardWidth = _getCardWidth(context);
    final cardHeight = _getCardHeight(context);
    final fontSize = _getFontSize(context);
    final padding = _getPadding(context);
    final horizontalMargin = _getHorizontalMargin(context);
    
    // Calculate responsive positioning with space for visible circle connector
    final topMargin = cardHeight * 0.02; // Minimal gap (2%)
    final bottomMargin = cardHeight * 0.02; // Minimal gap (2%)
    
    // Circle configuration
    final circleSize = 15.0;
    final circleSpacing = 0.0; // Small gap around circle for visibility
    
    // Calculate container heights with space for circle connector
    final availableHeight = cardHeight - topMargin - bottomMargin - circleSize - (circleSpacing * 2);
    final titleContainerHeight = availableHeight * 0.5; // 50% of available space
    final betsContainerHeight = availableHeight * 0.5; // 50% of available space
    
    // Calculate positions
    final titleContainerTop = topMargin;
    final circleTop = titleContainerTop + titleContainerHeight + circleSpacing;
    final betsContainerTop = circleTop + circleSize + circleSpacing;
    
    // Circle positioning - centered horizontally
    final circleLeft = (cardWidth - circleSize) / 2;

    return MouseRegion(
      child: GestureDetector(
        onTap: onTap,
        child: SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: Stack(
            children: [
              // Title container - positioned at top
              Positioned(
                top: topMargin,
                left: horizontalMargin,
                right: horizontalMargin,
                child: Container(
                  height: titleContainerHeight,
                  padding: EdgeInsets.symmetric(horizontal: padding, vertical: padding * 0.67),
                  decoration: BoxDecoration(
                    color: node.levelColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? node.levelColor
                          : isHighlightedInPath
                              ? Colors.orange.shade300
                              : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      node.title,
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'TiemposText',
                        color: node.levelColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),

              // Circle positioned at the junction between containers
              Positioned(
                top: circleTop,
                left: circleLeft,
                child: Container(
                  width: circleSize,
                  height: circleSize,
                  decoration: BoxDecoration(
                    color: Colors.white, // White background
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? node.levelColor
                          : isHighlightedInPath
                              ? Colors.orange.shade400
                              : Colors.grey.shade400,
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ),

              // Total Bets container - positioned with space for circle
              Positioned(
                top: betsContainerTop,
                left: horizontalMargin,
                right: horizontalMargin,
                child: Container(
                  height: betsContainerHeight,
                  padding: EdgeInsets.symmetric(horizontal: padding, vertical: padding * 0.67),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? node.levelColor
                          : isHighlightedInPath
                              ? Colors.orange.shade300
                              : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      'Bets: ${node.totalBets}',
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'TiemposText',
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NSLConnectionsPainter extends CustomPainter {
  final NSLNode rootNode;
  final Map<String, NSLNodePosition> nodePositions;
  final List<String> selectedPathIds;
  final Color defaultLineColor;
  final double lineThickness;
  final double verticalSpacing;
  final Color highlightColor;

  NSLConnectionsPainter({
    required this.rootNode,
    required this.nodePositions,
    required this.selectedPathIds,
    required this.defaultLineColor,
    this.lineThickness = 2.0,
    this.verticalSpacing = 80.0,
    this.highlightColor = Colors.deepOrangeAccent,
  });

  NSLNode? _findNodeById(NSLNode currentNode, String id) {
    if (currentNode.id == id) return currentNode;
    for (final child in currentNode.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = lineThickness
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    for (final position in nodePositions.values) {
      if (position.parentId != null) {
        final parentPosition = nodePositions[position.parentId!];
        if (parentPosition != null) {
          final parentNode = _findNodeById(rootNode, position.parentId!);

          bool isPathHighlighted = selectedPathIds.contains(position.id) &&
                                 selectedPathIds.contains(position.parentId!);

          paint.color = isPathHighlighted
              ? highlightColor
              : (parentNode?.levelColor ?? defaultLineColor);
          paint.strokeWidth = isPathHighlighted ? lineThickness * 1.8 : lineThickness;

          _drawConnection(canvas, paint, parentPosition, position);
        }
      }
    }
  }

  void _drawConnection(Canvas canvas, Paint paint, NSLNodePosition parent, NSLNodePosition child) {
    // Calculate connection points to touch the containers directly
    final parentCardWidth = parent.width;
    final parentCardHeight = parent.height;
    final childCardWidth = child.width;
    final childCardHeight = child.height;
    
    // Parent connection point: bottom of total bets container (90% down from top)
    final parentBottomMargin = parentCardHeight * 0.1;
    final parentConnectionPoint = Offset(
      parent.x + parentCardWidth / 2, 
      parent.y + parentCardHeight - parentBottomMargin
    );
    
    // Child connection point: top of title container (10% down from top)
    final childTopMargin = childCardHeight * 0.1;
    final childConnectionPoint = Offset(
      child.x + childCardWidth / 2, 
      child.y + childTopMargin
    );

    final midY = parentConnectionPoint.dy + (childConnectionPoint.dy - parentConnectionPoint.dy) / 2;

    if (parentConnectionPoint.dx == childConnectionPoint.dx) {
      canvas.drawLine(parentConnectionPoint, childConnectionPoint, paint);
    } else {
      canvas.drawLine(parentConnectionPoint, Offset(parentConnectionPoint.dx, midY), paint);
      canvas.drawLine(Offset(parentConnectionPoint.dx, midY), Offset(childConnectionPoint.dx, midY), paint);
      canvas.drawLine(Offset(childConnectionPoint.dx, midY), childConnectionPoint, paint);
    }

    _drawArrowhead(canvas, paint, childConnectionPoint, math.pi / 2);
  }

  void _drawArrowhead(Canvas canvas, Paint paint, Offset point, double angle) {
    const double arrowSize = 8.0;
    const double arrowAngle = math.pi / 6;

    final Path path = Path();
    path.moveTo(point.dx, point.dy);
    path.lineTo(
      point.dx - arrowSize * math.cos(angle - arrowAngle),
      point.dy - arrowSize * math.sin(angle - arrowAngle),
    );
    path.moveTo(point.dx, point.dy);
    path.lineTo(
      point.dx - arrowSize * math.cos(angle + arrowAngle),
      point.dy - arrowSize * math.sin(angle + arrowAngle),
    );
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(NSLConnectionsPainter oldDelegate) =>
      oldDelegate.nodePositions != nodePositions ||
      oldDelegate.rootNode != rootNode ||
      oldDelegate.selectedPathIds != selectedPathIds ||
      oldDelegate.defaultLineColor != defaultLineColor ||
      oldDelegate.highlightColor != highlightColor ||
      oldDelegate.lineThickness != lineThickness;
}
