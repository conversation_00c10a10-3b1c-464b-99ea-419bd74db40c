import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A widget that displays a preview of an uploaded file with a close button
class FileUploadPreview extends StatelessWidget {
  /// The name of the file
  final String fileName;

  /// Callback when the close button is pressed
  final VoidCallback onClose;

  /// Callback when the file preview is tapped
  final VoidCallback onFileTap;

  /// Whether the file is currently being processed (OCR in progress)
  final bool isLoading;

  /// Constructor
  const FileUploadPreview({
    super.key,
    required this.fileName,
    required this.onClose,
    required this.onFileTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // File icon with overlapping close button and loading overlay
          Stack(
            clipBehavior: Clip.none,
            children: [
              // File icon - always clickable
              InkWell(
                onTap: onFileTap,
                child: SvgPicture.asset(
                  'assets/images/chat/file_upload.svg',
                  width: 80,
                  height: 80,
                ),
              ),

              // Loading overlay when processing
              if (isLoading)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xff0058FF),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

              // Close button positioned at the top-right corner
              Positioned(
                top: -8,
                left: -8,
                child: InkWell(
                  onTap: isLoading ? null : onClose,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      'assets/images/chat/close.svg',
                      width: 22,
                      height: 22,
                    ),
                  ),
                ),
              ),
            ],
          ),

          // File name below the icon
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: SizedBox(
              width: 120, // Limit width to keep it compact
              child: Text(
                fileName,
                style: TextStyle(
                  fontSize: 14,
                  color: isLoading ? Colors.black54 : Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          // // Processing indicator text
          // if (isLoading)
          //   Padding(
          //     padding: const EdgeInsets.only(top: 4.0),
          //     child: SizedBox(
          //       width: 120,
          //       child: Text(
          //         'Processing...',
          //         style: TextStyle(
          //           fontSize: 12,
          //           color: Color(0xff0058FF),
          //           fontStyle: FontStyle.italic,
          //         ),
          //         textAlign: TextAlign.center,
          //       ),
          //     ),
          //   ),
        ],
      ),
    );
  }
}
