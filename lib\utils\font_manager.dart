import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

class FontManager {
  static const String fontFamilyTiemposText = "TiemposText";
  static const String fontFamilyInter = "Inter";

  // Font weights
  static const FontWeight thin = FontWeight.w100;
  static const FontWeight extraLight = FontWeight.w200;
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;

  // Font sizes
  static const double s10 = 10.0;
  static const double s12 = 12.0;
  static const double s13 = 13.0;
  static const double s14 = 14.0;
  static const double s16 = 16.0;
  static const double s18 = 18.0;
  static const double s20 = 20.0;
  static const double s22 = 22.0;
  static const double s24 = 24.0;
  static const double s26 = 26.0;
  static const double s28 = 28.0;
  static const double s30 = 30.0;
  static const double s32 = 32.0;
  static const double s36 = 36.0;
  static const double s40 = 40.0;
  static const double s48 = 48.0;

  // Line heights
  static const double lineHeight1_2 = 1.2;
  static const double lineHeight1_3 = 1.3;
  static const double lineHeight1_4 = 1.4;
  static const double lineHeight1_5 = 1.5;
  static const double lineHeight1_6 = 1.6;

  // Letter spacing
  static const double letterSpacingTight = -0.5;
  static const double letterSpacingNormal = 0.0;
  static const double letterSpacingWide = 0.5;
  static const double letterSpacingExtraWide = 1.0;

  // ============================================================================
  // THEME-AWARE TEXT STYLES (Require BuildContext)
  // ============================================================================

  // Primary Theme Text Styles
  static TextStyle getPrimaryText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    double? height,
    double? letterSpacing,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? regular,
      color: isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight,
      height: height ?? lineHeight1_4,
      letterSpacing: letterSpacing ?? letterSpacingNormal,
    );
  }

  static TextStyle getSecondaryText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    double? height,
    double? letterSpacing,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? regular,
      color: isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight,
      height: height ?? lineHeight1_4,
      letterSpacing: letterSpacing ?? letterSpacingNormal,
    );
  }

  // Theme-Aware Display Styles
  static TextStyle getDisplayLarge(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s48,
      fontWeight: bold,
      height: lineHeight1_2,
      letterSpacing: letterSpacingTight,
    );
  }

  static TextStyle getDisplayMedium(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s40,
      fontWeight: bold,
      height: lineHeight1_2,
      letterSpacing: letterSpacingTight,
    );
  }

  static TextStyle getDisplaySmall(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s36,
      fontWeight: semiBold,
      height: lineHeight1_3,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Theme-Aware Headline Styles
  static TextStyle getHeadlineLarge(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s32,
      fontWeight: semiBold,
      height: lineHeight1_3,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getHeadlineMedium(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s28,
      fontWeight: semiBold,
      height: lineHeight1_3,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getHeadlineSmall(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s24,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Theme-Aware Title Styles
  static TextStyle getTitleLarge(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s22,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getTitleMedium(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s20,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getTitleSmall(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s18,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  // Theme-Aware Body Styles
  static TextStyle getBodyLarge(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s16,
      fontWeight: regular,
      height: lineHeight1_5,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getBodyMedium(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s14,
      fontWeight: regular,
      height: lineHeight1_5,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getBodySmall(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s12,
      fontWeight: regular,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Theme-Aware Label Styles
  static TextStyle getLabelLarge(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s14,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getLabelMedium(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s12,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getLabelSmall(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s10,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingExtraWide,
    );
  }

  // Theme-Aware Button Styles
  static TextStyle getButtonLarge(BuildContext context, {bool isPrimary = true}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    Color textColor;
    
    if (isPrimary) {
      textColor = Colors.white; // Always white on primary buttons
    } else {
      textColor = isDark ? AppColors.subtleIndigoDark : AppColors.primaryIndigo;
    }
    
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s16,
      fontWeight: semiBold,
      color: textColor,
      height: lineHeight1_2,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getButtonMedium(BuildContext context, {bool isPrimary = true}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    Color textColor;
    
    if (isPrimary) {
      textColor = Colors.white;
    } else {
      textColor = isDark ? AppColors.subtleIndigoDark : AppColors.primaryIndigo;
    }
    
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: semiBold,
      color: textColor,
      height: lineHeight1_2,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getButtonSmall(BuildContext context, {bool isPrimary = true}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    Color textColor;
    
    if (isPrimary) {
      textColor = Colors.white;
    } else {
      textColor = isDark ? AppColors.subtleIndigoDark : AppColors.primaryIndigo;
    }
    
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s12,
      fontWeight: semiBold,
      color: textColor,
      height: lineHeight1_2,
      letterSpacing: letterSpacingWide,
    );
  }

  // Theme-Aware Caption and Overline
  static TextStyle getCaption(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s12,
      fontWeight: regular,
      height: lineHeight1_3,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getOverline(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s10,
      fontWeight: medium,
      height: lineHeight1_6,
      letterSpacing: letterSpacingExtraWide,
    );
  }

  // Theme-Aware Subtitle Styles
  static TextStyle getSubtitle1(BuildContext context) {
    return getPrimaryText(context,
      fontSize: s16,
      fontWeight: medium,
      height: lineHeight1_5,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getSubtitle2(BuildContext context) {
    return getSecondaryText(context,
      fontSize: s14,
      fontWeight: medium,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  // Status Text Styles (Theme-Aware)
  static TextStyle getErrorText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: regular,
      color: AppColors.error,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getSuccessText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: regular,
      color: AppColors.success,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getWarningText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: regular,
      color: AppColors.warning,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  static TextStyle getInfoText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: regular,
      color: AppColors.info,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Theme-Aware Link Styles
  static TextStyle getLinkText(BuildContext context, {bool underline = true}) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: s14,
      fontWeight: regular,
      color: isDark ? AppColors.subtleIndigoDark : AppColors.primaryIndigo,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
      decoration: underline ? TextDecoration.underline : null,
    );
  }

  // Brand-Specific Text Styles
  static TextStyle getBrandText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s16,
      fontWeight: fontWeight ?? semiBold,
      color: isDark ? AppColors.subtleIndigoDark : AppColors.primaryIndigo,
      height: lineHeight1_4,
      letterSpacing: letterSpacingWide,
    );
  }

  static TextStyle getAccentText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? medium,
      color: AppColors.textBlue,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Muted Text Styles
  static TextStyle getMutedText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s12,
      fontWeight: fontWeight ?? regular,
      color: (isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight).withAlpha(153), // 60% opacity
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // High Contrast Text (for accessibility)
  static TextStyle getHighContrastText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? semiBold,
      color: isDark ? AppColors.white : AppColors.black,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // Surface Text (for cards and elevated surfaces)
  static TextStyle getSurfaceText(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    bool isSecondary = false,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    Color textColor;
    
    if (isSecondary) {
      textColor = isDark ? AppColors.textSecondaryDark : AppColors.textSecondaryLight;
    } else {
      textColor = isDark ? AppColors.textPrimaryDark : AppColors.textPrimaryLight;
    }
    
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? regular,
      color: textColor,
      height: lineHeight1_4,
      letterSpacing: letterSpacingNormal,
    );
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  // Utility Methods for Custom Styling
  static TextStyle getCustomStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
    String? fontFamily
  }) {
    return TextStyle(
      fontFamily: fontFamily ?? FontManager.fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? regular,
      color: color,
      height: height ?? lineHeight1_4,
      letterSpacing: letterSpacing ?? letterSpacingNormal,
      decoration: decoration,
    );
  }

  // Color variants for existing styles
  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle baseStyle, FontWeight weight) {
    return baseStyle.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle baseStyle, double size) {
    return baseStyle.copyWith(fontSize: size);
  }

  static TextStyle withDecoration(TextStyle baseStyle, TextDecoration decoration) {
    return baseStyle.copyWith(decoration: decoration);
  }

  // Responsive text styles (can be used with MediaQuery)
  static TextStyle getResponsiveStyle({
    required BuildContext context,
    required TextStyle baseStyle,
    double? scaleFactor,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    double factor = scaleFactor ?? 1.0;

    if (screenWidth < 600) {
      factor = 0.9; // Mobile
    } else if (screenWidth < 1200) {
      factor = 1.0; // Tablet
    } else {
      factor = 1.1; // Desktop
    }

    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? s14) * factor,
    );
  }

  // Utility method to get theme-aware custom style
  static TextStyle getThemeAwareCustomStyle(BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? lightColor,
    Color? darkColor,
    double? height,
    double? letterSpacing,
    TextDecoration? decoration,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    Color? textColor;
    
    if (lightColor != null && darkColor != null) {
      textColor = isDark ? darkColor : lightColor;
    } else if (lightColor != null) {
      textColor = lightColor;
    } else if (darkColor != null) {
      textColor = darkColor;
    }
    
    return TextStyle(
      fontFamily: fontFamilyInter,
      fontSize: fontSize ?? s14,
      fontWeight: fontWeight ?? regular,
      color: textColor,
      height: height ?? lineHeight1_4,
      letterSpacing: letterSpacing ?? letterSpacingNormal,
      decoration: decoration,
    );
  }
}
