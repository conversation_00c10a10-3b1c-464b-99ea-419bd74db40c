import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_audio_widget_mobile.dart';

void main() {
  group('WebAudioRecorderWidgetImpl Mobile Tests', () {
    testWidgets('WebAudioRecorderWidgetImpl displays recording UI correctly', (WidgetTester tester) async {
      bool cancelCalled = false;
      bool loadingChanged = false;
      final chatController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WebAudioRecorderWidgetImpl(
              chatController: chatController,
              onCancel: () {
                cancelCalled = true;
              },
              onLoadingChanged: (isLoading) {
                loadingChanged = isLoading;
              },
            ),
          ),
        ),
      );

      // Allow the widget to initialize
      await tester.pump();

      // Verify the recording UI elements are present
      expect(find.byIcon(Icons.close), findsOneWidget);
      expect(find.byIcon(Icons.mic), findsOneWidget);
      expect(find.byIcon(Icons.check), findsOneWidget);
      
      // Verify timer display is present (should show 00:00 initially)
      expect(find.text('00:00'), findsOneWidget);
    });

    testWidgets('WebAudioRecorderWidgetImpl cancel button works', (WidgetTester tester) async {
      bool cancelCalled = false;
      final chatController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WebAudioRecorderWidgetImpl(
              chatController: chatController,
              onCancel: () {
                cancelCalled = true;
              },
            ),
          ),
        ),
      );

      // Allow the widget to initialize
      await tester.pump();

      // Tap the cancel button
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      // Verify the cancel callback was called
      expect(cancelCalled, isTrue);
    });

    testWidgets('WebAudioRecorderWidgetImpl timer format is correct', (WidgetTester tester) async {
      final chatController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WebAudioRecorderWidgetImpl(
              chatController: chatController,
            ),
          ),
        ),
      );

      // Allow the widget to initialize
      await tester.pump();

      // Initially should show 00:00
      expect(find.text('00:00'), findsOneWidget);
      
      // The timer should be running, but we can't easily test the actual timing
      // in a unit test without mocking the timer
    });

    testWidgets('WebAudioRecorderWidgetImpl has proper UI layout', (WidgetTester tester) async {
      final chatController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WebAudioRecorderWidgetImpl(
              chatController: chatController,
            ),
          ),
        ),
      );

      // Allow the widget to initialize
      await tester.pump();

      // Verify the main structure
      expect(find.byType(Column), findsOneWidget);
      expect(find.byType(Row), findsAtLeastNWidgets(2)); // Main row and timer row
      expect(find.byType(IconButton), findsNWidgets(2)); // Cancel and confirm buttons
      expect(find.byType(Container), findsOneWidget); // Confirm button container
    });
  });
}
