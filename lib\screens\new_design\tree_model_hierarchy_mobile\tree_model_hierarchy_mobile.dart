import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/screens/new_design/tree_model_hierarchy_mobile/emloyee_hierarchy_chat_mobile.dart';
import 'package:nsl/screens/new_design/tree_model_hierarchy_mobile/employee_details_panel_mobile.dart';
import '../../../../models/employee_tree_model.dart';

class TreeHierarchyModelMobile extends StatefulWidget {
  const TreeHierarchyModelMobile({super.key});

  @override
  State<TreeHierarchyModelMobile> createState() =>
      _TreeHierarchyModelMobileState();
}

class _TreeHierarchyModelMobileState extends State<TreeHierarchyModelMobile>
    with TickerProviderStateMixin {
  List<EmployeeData> _employees = [];
  EmployeeNode? _rootNode;
  EmployeeNode? _filteredRootNode;
  EmployeeData? _selectedEmployee;
  bool _showBottomPanel = false;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _searchQuery = '';

  // Animation controller for bottom panel
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  // Resizable panel state
  double _currentPanelHeight = 0.0;
  double _minPanelHeight = 0.0;
  double _maxPanelHeight = 0.0;
  bool _isResizing = false;

  // Expandable search state
  bool _isSearchExpanded = false;
  late AnimationController _searchAnimationController;
  late Animation<double> _searchExpandAnimation;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadEmployeeData();
    _searchController.addListener(_onSearchChanged);

    // Initialize animation controller for bottom panel
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize search animation controller
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _searchExpandAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchAnimationController,
      curve: Curves.easeInOut,
    ));

    // Listen to focus changes
    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus && _searchQuery.isEmpty) {
        _collapseSearch();
      }
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _animationController.dispose();
    _searchAnimationController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.trim();
      _buildFilteredTree();
    });
  }

  Future<void> _loadEmployeeData() async {
    try {
      // Load JSON data from assets
      final String jsonString =
          await rootBundle.loadString('assets/data/employees.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // Parse the nested employee structure
      if (jsonData.containsKey('employee')) {
        // New nested structure
        final employeeData = EmployeeData.fromJson(jsonData['employee']);
        _employees = employeeData.getAllEmployees(); // Flatten for search
        _rootNode = EmployeeHierarchyBuilder.buildHierarchyFromNestedData(employeeData);
      } 
      else if (jsonData.containsKey('employees')) {
        // Legacy flat structure
        final List<dynamic> employeesJson = jsonData['employees'];
        _employees = employeesJson.map((json) => EmployeeData.fromJson(json)).toList();
        _rootNode = EmployeeHierarchyBuilder.buildHierarchy(_employees);
     }
       else {
        throw Exception('Invalid JSON structure: missing "employee" or "employees" key');
      }

      // Build filtered tree
      _buildFilteredTree();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading employee data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      // Find employee by name or ID in the flattened list
      final foundEmployees = _employees
          .where((emp) =>
              emp.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              emp.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundEmployees.isNotEmpty) {
        // For nested structure, we need to find the employee in the tree and show its subtree
        _filteredRootNode = _findEmployeeInTree(_rootNode, foundEmployees.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  // Helper method to find an employee in the tree and return its subtree
  EmployeeNode? _findEmployeeInTree(EmployeeNode? node, String employeeId) {
    if (node == null) return null;
    
    if (node.id == employeeId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findEmployeeInTree(child, employeeId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  void _onEmployeeTap(EmployeeNode node) {
    setState(() {
      _selectedEmployee = node.originalEmployee;
      _showBottomPanel = true;

      // Initialize panel dimensions
      final screenHeight = MediaQuery.of(context).size.height;
      _minPanelHeight = screenHeight * 0.25; // Minimum 25% of screen
      _maxPanelHeight = screenHeight * 0.8; // Maximum 80% of screen
      _currentPanelHeight = _getBottomPanelHeight(context); // Default size
    });
    _animationController.forward();
  }

  void _hideBottomPanel() {
    _animationController.reverse().then((_) {
      setState(() {
        _showBottomPanel = false;
        _selectedEmployee = null;
      });
    });
  }

  // Search control methods
  void _expandSearch() {
    setState(() {
      _isSearchExpanded = true;
    });
    _searchAnimationController.forward();
    _searchFocusNode.requestFocus();
  }

  void _collapseSearch() {
    _searchFocusNode.unfocus();
    _searchAnimationController.reverse().then((_) {
      setState(() {
        _isSearchExpanded = false;
      });
    });
  }

  void _clearSearch() {
    _searchController.clear();
    if (_searchQuery.isEmpty) {
      _collapseSearch();
    }
  }

  // Get responsive dimensions
  double _getResponsiveTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 16.0; // Very small phones
    if (screenWidth < 400) return 18.0; // Small phones
    return 20.0; // Medium and larger phones
  }

  String _getResponsiveTitle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 'Hierarchy'; // Very small phones
    return 'Employee Hierarchy'; // All other sizes
  }

  double _getResponsiveSearchWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 400) return screenWidth * 0.6; // Small phones
    if (screenWidth < 600) return screenWidth * 0.65; // Medium phones
    return math.min(300, screenWidth * 0.4); // Larger screens
  }

  double _getBottomPanelHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return screenHeight * 0.45; // Small screens
    if (screenHeight < 800) return screenHeight * 0.4; // Medium screens
    return screenHeight * 0.35; // Large screens
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Main content area
          Column(
            children: [
              // Header with search
              _buildHeader(),

              // Tree hierarchy chart
              Expanded(
                child: _filteredRootNode != null
                    ? EmployeeHierarchyChartMobile(
                        rootNode: _filteredRootNode!,
                        onNodeTap: _onEmployeeTap,
                      )
                    : _buildEmptyState(),
              ),
            ],
          ),

          // Bottom panel for employee details
          if (_showBottomPanel && _selectedEmployee != null)
            _buildBottomPanel(),
        ],
      ),
    );
  }

  Widget _buildBottomPanel() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _slideAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // Background overlay - tap to dismiss
              if (_slideAnimation.value < 1.0)
                GestureDetector(
                  onTap: _hideBottomPanel,
                  child: Container(
                    color: Colors.black
                        .withOpacity(0.3 * (1 - _slideAnimation.value)),
                  ),
                ),

              // Resizable bottom panel
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Transform.translate(
                  offset:
                      Offset(0, _currentPanelHeight * _slideAnimation.value),
                  child: Container(
                    height: _currentPanelHeight,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 2,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Resize handle area
                        GestureDetector(
                          onPanUpdate: (details) {
                            if (!_isResizing) {
                              setState(() {
                                _isResizing = true;
                              });
                            }

                            // Calculate new height based on drag
                            final newHeight =
                                _currentPanelHeight - details.delta.dy;
                            setState(() {
                              _currentPanelHeight = newHeight.clamp(
                                  _minPanelHeight, _maxPanelHeight);
                            });
                          },
                          onPanEnd: (details) {
                            setState(() {
                              _isResizing = false;
                            });

                            // Snap to predefined sizes based on velocity
                            final velocity =
                                details.velocity.pixelsPerSecond.dy;
                            if (velocity.abs() > 500) {
                              if (velocity > 0) {
                                // Swiping down - minimize or close
                                if (_currentPanelHeight <
                                    _minPanelHeight * 1.5) {
                                  _hideBottomPanel();
                                } else {
                                  setState(() {
                                    _currentPanelHeight = _minPanelHeight;
                                  });
                                }
                              } else {
                                // Swiping up - maximize
                                setState(() {
                                  _currentPanelHeight = _maxPanelHeight;
                                });
                              }
                            } else {
                              // Snap to nearest predefined size
                              final screenHeight =
                                  MediaQuery.of(context).size.height;
                              final midHeight = screenHeight * 0.5;

                              if (_currentPanelHeight < midHeight) {
                                setState(() {
                                  _currentPanelHeight = _minPanelHeight;
                                });
                              } else {
                                setState(() {
                                  _currentPanelHeight = _maxPanelHeight;
                                });
                              }
                            }
                          },
                          onTap: () {
                            // Double tap to toggle between min and max
                          },
                          child: Container(
                            width: double.infinity,
                            height: 32,
                            color: Colors.transparent,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Drag handle
                                Container(
                                  width: 40,
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: _isResizing
                                        ? Colors.blue.shade400
                                        : Colors.grey.shade400,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // Resize indicator
                                if (_isResizing)
                                  Container(
                                    width: 20,
                                    height: 2,
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade300,
                                      borderRadius: BorderRadius.circular(1),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),

                        // Employee details panel
                        Expanded(
                          child: GestureDetector(
                            onPanUpdate: (details) {
                              // Only handle swipe down to dismiss when not resizing
                              if (!_isResizing && details.delta.dy > 0) {
                                final progress =
                                    details.delta.dy / _currentPanelHeight;
                                final newValue =
                                    (_slideAnimation.value + progress)
                                        .clamp(0.0, 1.0);
                                _animationController.value = 1.0 - newValue;
                              }
                            },
                            onPanEnd: (details) {
                              if (!_isResizing) {
                                // If swiped down significantly, close the panel
                                if (details.velocity.pixelsPerSecond.dy > 300 ||
                                    _slideAnimation.value > 0.5) {
                                  _hideBottomPanel();
                                } else {
                                  // Otherwise, snap back to open position
                                  _animationController.forward();
                                }
                              }
                            },
                            onTap: () {}, // Prevent tap through
                            child: EmployeeDetailsPanelMobile(
                              employee: _selectedEmployee!,
                              onClose: _hideBottomPanel,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    final screenWidth = MediaQuery.of(context).size.width;
    final titleFontSize = _getResponsiveTitleFontSize(context);
    final title = _getResponsiveTitle(context);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth < 400 ? 12.0 : 16.0,
        vertical: screenWidth < 400 ? 12.0 : 16.0,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.transparent, ),
        ),
      ),
      child: SafeArea(
        child: AnimatedBuilder(
          animation: _searchExpandAnimation,
          builder: (context, child) {
            return Column(
              children: [
                Row(
                  children: [
                    // Title - shrinks when search expands
                    if (!_isSearchExpanded ||
                        _searchExpandAnimation.value < 0.5)
                      Expanded(
                        flex: _isSearchExpanded ? 0 : 1,
                        child: Opacity(
                          opacity: 1.0 - _searchExpandAnimation.value,
                          child: Text(
                            title,
                            style: TextStyle(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),

                    // Spacer when title is visible
                    if (!_isSearchExpanded &&
                        _searchExpandAnimation.value < 0.5)
                      const SizedBox(width: 16),

                    // Search icon or expanded search field
                    if (!_isSearchExpanded)
                      // Search icon button
                      GestureDetector(
                        onTap: _expandSearch,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Icon(
                            Icons.search,
                            color: Colors.grey.shade600,
                            size: 20,
                          ),
                        ),
                      )
                    else
                      // Expanded search field
                      Expanded(
                        child: Transform.scale(
                          scale: 0.8 + (0.2 * _searchExpandAnimation.value),
                          child: Opacity(
                            opacity: _searchExpandAnimation.value,
                            child: Container(
                              height: 45,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Row(
                                children: [
                                  // Left side - search icon with fixed width
                                  Container(
                                    width: 40,
                                    child: Icon(
                                      Icons.search,
                                      color: Colors.grey.shade500,
                                      size: 20,
                                    ),
                                  ),
                                  
                                  // Center - text field
                                  Expanded(
                                    child: TextField(
                                      controller: _searchController,
                                      focusNode: _searchFocusNode,
                                      decoration: InputDecoration(
                                        hintText: 'Search employees...',
                                        hintStyle: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                        ),
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                        errorBorder: InputBorder.none,
                                        disabledBorder: InputBorder.none,
                                        contentPadding: EdgeInsets.only(left:5),
                                      ),
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontFamily: 'TiemposText',
                                        color: Colors.black,
                                      ),
                                     // textAlign: TextAlign.center,
                                    //  textAlignVertical: TextAlignVertical.center,
                                    ),
                                  ),
                                  
                                  // Right side - action buttons with fixed width
                                  Container(
                                    width: 40,
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        if (_searchQuery.isNotEmpty)
                                          GestureDetector(
                                            onTap: _clearSearch,
                                            child: Container(
                                              padding: const EdgeInsets.all(4),
                                              child: Icon(
                                                Icons.clear,
                                                color: Colors.grey.shade500,
                                                size: 16,
                                              ),
                                            ),
                                          )
                                        else
                                          GestureDetector(
                                            onTap: _collapseSearch,
                                            child: Container(
                                              padding: const EdgeInsets.all(4),
                                              child: Icon(
                                                Icons.close,
                                                color: Colors.grey.shade600,
                                                size: 16,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Search by name or ID...',
        hintStyle: TextStyle(
          color: Colors.grey.shade500,
          fontSize: 14,
          fontFamily: 'TiemposText',
        ),
        prefixIcon: Icon(
          Icons.search,
          color: Colors.grey.shade500,
          size: 20,
        ),
        suffixIcon: _searchQuery.isNotEmpty
            ? IconButton(
                icon: Icon(
                  Icons.clear,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                onPressed: () {
                  _searchController.clear();
                },
              )
            : null,
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
      ),
      style: TextStyle(
        fontSize: 14,
        fontFamily: 'TiemposText',
        color: Colors.black,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No employee data available'
                : 'No employees found for "$_searchQuery"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                _searchController.clear();
              },
              child: Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
