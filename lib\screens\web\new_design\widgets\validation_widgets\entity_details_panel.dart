import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/entities_data.dart' as entities_model;
import 'package:nsl/models/conversation_response.dart';
import 'package:nsl/theme/spacing.dart';

/// A reusable entity details panel widget that displays detailed information about a selected entity.
///
/// This widget shows entity information including attributes, business rules, relationships,
/// and validations in a structured format with navigation shortcuts and interactive elements.
class EntityDetailsPanel extends StatefulWidget {
  /// The entity information to display
  final entities_model.Entity entity;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Global entity elements for additional data
  final Map<String, EntityElement> globalEntityElements;

  const EntityDetailsPanel({
    super.key,
    required this.entity,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.globalEntityElements = const {},
  });

  @override
  State<EntityDetailsPanel> createState() => _EntityDetailsPanelState();
}

class _EntityDetailsPanelState extends State<EntityDetailsPanel> {
  // Track active section for navigation styling
  String? activeSectionId;

  // Global keys for sections - moved to class level to persist across builds
  late Map<String, GlobalKey> sectionKeys;

  // ScrollController for the content area
  late ScrollController _scrollController;

  // Bottom spacer height for better scrolling
  double _bottomSpacerHeight = 500;
  bool _isProgrammaticScroll = false;
  bool _hasUserScrolled = false;
  int _selectedSectionIndex = 0;

  // VisibilityInfo objects for each section
  VisibilityInfo _attributesVi = VisibilityInfo(key: Key('attributesVi'));
  VisibilityInfo _businessRulesVi = VisibilityInfo(key: Key('businessRulesVi'));
  VisibilityInfo _relationshipsVi = VisibilityInfo(key: Key('relationshipsVi'));
  VisibilityInfo _constantsValidationsVi =
      VisibilityInfo(key: Key('constantsValidationsVi'));

  // VisibilityInfo objects for each section
  Map<String, VisibilityInfo> sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();

    // Initialize section keys
    sectionKeys = {
      'attributes': GlobalKey(),
      'business_rules': GlobalKey(),
      'relationships': GlobalKey(),
      'constants_validations': GlobalKey(),
    };

    // Add scroll listener
    _scrollController.addListener(_onScroll);

    // Initialize the first section as active
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        activeSectionId = 'attributes';
      });
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    if (!_isProgrammaticScroll && !_hasUserScrolled) {
      setState(() {
        _hasUserScrolled = true;
        _bottomSpacerHeight = 100;
      });
    }
  }

  /// Method to update active section based on visibility
  void _updateActiveSection() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != activeSectionId) {
      if (mounted) {
        setState(() {
          activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Define the sections for navigation with their abbreviations
    final Map<String, String> sectionMap = {
      'attributes': 'ATT',
      'business_rules': 'BR',
      'relationships': 'REL',
      'constants_validations': 'CV',
    };

    // Function to scroll to a specific section
    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final key = sectionKeys[sectionId];

        if (key?.currentContext != null) {
          // Update active section immediately
          setState(() {
            activeSectionId = sectionId;
          });

          // Update bottom spacer height for better scrolling
          setState(() {
            final sectionKeysList = sectionMap.keys.toList();
            final sectionIndex = sectionKeysList.indexOf(sectionId);

            // Calculate required bottom padding based on section position
            if (sectionIndex >= (sectionKeysList.length - 2)) {
              // Last 2 sections need more space
              _bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              _bottomSpacerHeight = 500;
            }
          });

          // Scroll to the section
          Future.delayed(Duration(milliseconds: 50), () {
            if (_scrollController.hasClients) {
              Scrollable.ensureVisible(
                key!.currentContext!,
                alignment: 0.0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }

    return Container(
      width: 400,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x249B9B9B),
            blurRadius: 20,
            offset: Offset(-3, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with entity title and close button
          Container(
            padding: const EdgeInsets.only(
                left: AppSpacing.xxl,
                right: AppSpacing.xxs,
                bottom: AppSpacing.xxs,
                top: AppSpacing.xxs),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        radius: 10,
                        child: Icon(
                          Icons.person_outline,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: Text(
                          widget.entity.title ?? 'Entity Details',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                const SizedBox(
                  width: AppSpacing.xxs,
                ),
                IconButton(
                  icon: SvgPicture.asset(
                    'assets/images/chat/toggle_open_close.svg',
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade700,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: widget.onClose,
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          // Navigation and content
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left navigation
                Container(
                  width: 32,
                  padding: const EdgeInsets.only(top: 3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      for (int i = 0; i < sectionMap.length; i++)
                        Padding(
                          padding:
                              EdgeInsets.only(left: 8, top: i == 0 ? 16 : 6),
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: () =>
                                  scrollToSection(sectionMap.keys.elementAt(i)),
                              child: Text(
                                sectionMap.values.elementAt(i),
                                style: TextStyle(
                                  fontWeight: activeSectionId ==
                                          sectionMap.keys.elementAt(i)
                                      ? FontWeight.bold
                                      : FontWeight.w400,
                                  fontSize: 10,
                                  fontFamily:  "TiemposText",
                                  color: activeSectionId ==
                                          sectionMap.keys.elementAt(i)
                                      ? Colors.blue.shade700
                                      : Colors.black,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Right content area with scrollable sections
                Expanded(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            for (final sectionId in sectionMap.keys)
                              _buildEntitySectionWithKey(
                                sectionId,
                                widget.entity,
                                sectionKeys,
                              ),

                            // Smart bottom padding
                            SizedBox(height: _bottomSpacerHeight),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEntitySectionWithKey(String sectionId,
      entities_model.Entity entity, Map<String, GlobalKey> sectionKeys) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        sectionVisibilityInfo[sectionId] = info;
        _updateActiveSection();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.only(left:16, bottom:16, right:16,top: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_getSectionTitle(sectionId)}:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 8),
            _buildContentForEntitySection(sectionId, entity),
          ],
        ),
      ),
    );
  }

  Widget _buildContentForEntitySection(
      String sectionId, entities_model.Entity entity) {
    // Special handling for attributes section - use entity's attributes data
    if (sectionId == 'attributes') {
      return _buildAttributesFromEntityData(entity);
    }

    if (sectionId == 'business_rules') {
      return _buildBusinessRulesFromEntityData(entity);
    }

    if (sectionId == 'relationships') {
      return _buildRelationFromEntityData(entity);
    }

    if (sectionId == 'constants_validations') {
      return _buildValidationsFromEntityData(entity);
    }

    // Only display content when it comes from the API - no fallback
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildAttributesFromEntityData(entities_model.Entity entity) {
    // Check if this entity has attributes data from API
    if (entity.attributeMetaDataList != null &&
        entity.attributeMetaDataList!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final attribute in entity.attributeMetaDataList!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: _generateAttributeSentence(attribute))
                ],
              ),
            ),
        ],
      );
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildBusinessRulesFromEntityData(entities_model.Entity entity) {
    // Check if this entity has relationship properties data from API
    if (entity.relationshipProperties != null &&
        entity.relationshipProperties!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final relationShipProp in entity.relationshipProperties!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: '${relationShipProp.sourceEntity}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' and ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '${relationShipProp.targetEntity}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' have relationship. those are  ${relationShipProp.onDelete} and  ${relationShipProp.onUpdate}.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No business rules data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No business rules data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildRelationFromEntityData(entities_model.Entity entity) {
    // Check if this entity has relationships data from API
    if (entity.relationships != null && entity.relationships!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final relation in entity.relationships!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: '${relation.sourceEntity}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' has ${relation.type} relationship with ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '${relation.targetEntity}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: ' using ',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '${relation.joinCondition.replaceAll("=", "to")}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No relationships data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No relationships data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildValidationsFromEntityData(entities_model.Entity entity) {
    // Check if this entity has validations data from API
    if (entity.validationsList != null && entity.validationsList!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final validation in entity.validationsList!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: '${validation.attribute}',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                          TextSpan(
                            text: '  ${validation.constraintText}.',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No validations data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No validations data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  String _getSectionTitle(String sectionId) {
    switch (sectionId) {
      case 'attributes':
        return 'Attributes';
      case 'business_rules':
        return 'Business Rules';
      case 'relationships':
        return 'Relationships';
      case 'constants_validations':
        return 'Constants & Validations';
      default:
        return 'Section';
    }
  }

  Widget _generateAttributeSentence(dynamic attribute) {
    return Text.rich(TextSpan(
        text: "${attribute.displayName} ",
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        children: <InlineSpan>[
          TextSpan(
            text:
                "is ${attribute.keyType} key and ${attribute.description}. Data type is ${attribute.dataType}${attribute.values != "N/A" ? " ( ${attribute.values} )" : ""}. Error message: \"${attribute.errorMessage}\". ${attribute.required} field.",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          )
        ])
        );
  }
}
