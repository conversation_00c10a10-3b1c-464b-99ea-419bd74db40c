import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_svg/flutter_svg.dart';

class LibrarySideDrawer extends StatefulWidget {
  const LibrarySideDrawer({
    super.key,
  });

  @override
  State<LibrarySideDrawer> createState() => _LibrarySideDrawerState();
}

// Enum for category selection
enum CategoryType { projects, gsis, cus }

// Enum for period selection
enum PeriodType { qtly, half, annual }

class _LibrarySideDrawerState extends State<LibrarySideDrawer> {
  // Timeline filter states
  CategoryType selectedCategory = CategoryType.projects;
  PeriodType selectedPeriod = PeriodType.annual;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status Of All Projects Section
          _buildStatusSection(),
          const SizedBox(height: 14),

          // Progress Status Section
          _buildProgressSection(),
          const SizedBox(height: 14),

          // Working Timeline Section
          _buildTimelineSection(),
        ],
      ),
    );
  }

  Widget _buildStatusSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(23, 18, 23, 18),
      decoration: BoxDecoration(
        color: Color(0xFFF7F9FD),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Status Of All Projects',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: "TiemposText",
              color: Color(0xFF151515),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            height: 240,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Doughnut Chart
                PieChart(
                  PieChartData(
                    sectionsSpace: 0,
                    centerSpaceRadius: 70,
                    sections: [
                      // Blue section (Published - 56)
                      PieChartSectionData(
                        color: Color(0xFF689DFF),
                        value: 56,
                        title: '56',
                        titleStyle: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: "TiemposText",
                        ),
                        radius: 50,
                        titlePositionPercentageOffset: 0.5,
                      ),
                      // Teal section (Saved - 30)
                      PieChartSectionData(
                        color: Color(0xFF00B5B0),
                        value: 30,
                        title: '30',
                        titleStyle: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: "TiemposText",
                        ),
                        radius: 50,
                        titlePositionPercentageOffset: 0.5,
                      ),
                      // Pink section (In Progress - 20)
                      PieChartSectionData(
                        color: Color(0xFFFF7BB7),
                        value: 20,
                        title: '20',
                        titleStyle: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: "TiemposText",
                        ),
                        radius: 50,
                        titlePositionPercentageOffset: 0.5,
                      ),
                    ],
                  ),
                ),
                // Center text
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'All Projects',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF7A8487),
                        fontFamily: "TiemposText",
                      ),
                    ),
                    Text(
                      '100',
                      style: TextStyle(
                        fontSize: 46,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: "TiemposText",
                        height: 50 / 46, // Line height of 50px
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // Legend
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Flexible(
                  child:
                      _buildLegendItem(Color(0xFF689DFF), '56', 'Published')),
              Flexible(
                  child: _buildLegendItem(Color(0xFF00B5B0), '30', 'Saved')),
              Flexible(
                  child:
                      _buildLegendItem(Color(0xFFFF7BB7), '20', 'In Progress')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(Color color, String count, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(11),
          ),
          child: Text(
            count,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontFamily: "TiemposText",
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.black,
            fontFamily: "TiemposText",
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    final progressItems = [
      {
        'name': 'Chemical Substance for GMK',
        'progress': 0.85,
        'imagePath': 'assets/images/library_side_drawer/book-1.svg',
        'color': Color(0xFF00C853),
        'icon': Icons.home_outlined,
      },
      {
        'name': 'Mobile App Solutions',
        'progress': 0.42,
        'imagePath': 'assets/images/library_side_drawer/book-2.svg',
        'color': Color(0xFF1565C0),
        'icon': Icons.apps,
      },
      {
        'name': 'Mobile App Solutions',
        'progress': 0.68,
        'imagePath': 'assets/images/library_side_drawer/book-3.svg',
        'color': Color(0xFF00ACC1),
        'icon': Icons.phone_android,
      },
      {
        'name': 'Fashion Brands Sales',
        'progress': 0.42,
        'imagePath': 'assets/images/library_side_drawer/book-4.svg',
        'color': Color(0xFFFF6F00),
        'icon': Icons.shopping_bag_outlined,
      },
      {
        'name': 'Mobile App Solutions',
        'progress': 0.42,
        'imagePath': 'assets/images/library_side_drawer/book-5.svg',
        'color': Color(0xFF1565C0),
        'icon': Icons.apps,
      },
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(23, 18, 23, 18),
      decoration: BoxDecoration(
        // color: Color(0xFFF7F9FD),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Progress Status',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: "TiemposText",
              color: Color(0xFF151515),
            ),
          ),
          const SizedBox(height: 16),
          ...progressItems.map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildProgressItem(
                  item['name'] as String,
                  item['progress'] as double,
                  item['imagePath'] as String,
                  item['color'] as Color,
                  item['icon'] as IconData,
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String name, double progress, String imagePath,
      Color fallbackColor, IconData fallbackIcon) {
    return Row(
      children: [
        // Book image container - using images from library_side_drawer folder
        _buildImageContainer(imagePath, fallbackColor, fallbackIcon),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: 12,
                  fontFamily: "TiemposText",
                  color: Color(0xFF101010),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 6),
              // Custom progress bar with percentage inside
              Container(
                height: 20,
                decoration: BoxDecoration(
                  color: Color(0xFFE0E0E0),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    // Progress fill
                    FractionallySizedBox(
                      widthFactor: progress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xFF9BE2AF),
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    // Percentage text
                    Center(
                      child: Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF151515),
                          fontFamily: "TiemposText",
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(23, 18, 23, 18),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Working Timeline',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              fontFamily: "TiemposText",
              color: Color(0xFF151515),
            ),
          ),
          const SizedBox(height: 16),
          // Stacked bar chart
          SizedBox(
            height: 220, // Fixed height for the bar chart
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: _getMaxYValue(),
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final labels = _getTimelineLabels();
                        if (value.toInt() < labels.length &&
                            labels[value.toInt()].isNotEmpty) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              labels[value.toInt()],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                                fontFamily: "TiemposText",
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }
                        return Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 40,
                      getTitlesWidget: (value, meta) {
                        final interval = _getYAxisInterval();
                        if (value % interval == 0) {
                          return Text(
                            '${value.toInt()} hr',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                              fontFamily: "TiemposText",
                            ),
                          );
                        }
                        return Text('');
                      },
                    ),
                  ),
                  topTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles:
                      AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: _getYAxisInterval(),
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.grey[300]!,
                      strokeWidth: 1,
                      dashArray: [5, 5],
                    );
                  },
                ),
                borderData: FlBorderData(show: false),
                barGroups: _generateStackedBarGroups(),
              ),
            ),
          ),
          const SizedBox(height: 20),
          // Legend with radio buttons
          _buildLegendSection(),
        ],
      ),
    );
  }

  Widget _buildLegendSection() {
    return Column(
      children: [
        Row(
          children: [
            _buildCategoryRadioItem(CategoryType.projects, 'Projects'),
            const SizedBox(width: 24),
            _buildCategoryRadioItem(CategoryType.gsis, 'GSIs'),
            const SizedBox(width: 24),
            _buildCategoryRadioItem(CategoryType.cus, 'CUs'),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildPeriodRadioItem(PeriodType.qtly, 'Qtly'),
            const SizedBox(width: 24),
            _buildPeriodRadioItem(PeriodType.half, 'Half'),
            const SizedBox(width: 24),
            _buildPeriodRadioItem(PeriodType.annual, 'Annual'),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryRadioItem(
      CategoryType categoryValue, String categoryLabel) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: Radio<CategoryType>(
            value: categoryValue,
            groupValue: selectedCategory,
            onChanged: (CategoryType? value) {
              if (value != null) {
                setState(() {
                  selectedCategory = value;
                });
              }
            },
            activeColor: Color(0xFF4A90E2),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          categoryLabel,
          style: TextStyle(
            fontSize: 12,
            color: Colors.black,
            fontFamily: "TiemposText",
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodRadioItem(PeriodType periodValue, String periodLabel) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: Radio<PeriodType>(
            value: periodValue,
            groupValue: selectedPeriod,
            onChanged: (PeriodType? value) {
              if (value != null) {
                setState(() {
                  selectedPeriod = value;
                });
              }
            },
            activeColor: Color(0xFF4A90E2),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: VisualDensity.compact,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          periodLabel,
          style: TextStyle(
            fontSize: 12,
            color: Colors.black,
            fontFamily: "TiemposText",
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  List<List<int>> _getTimelineData() {
    // Different datasets based on selected period - only return actual data points
    Map<PeriodType, List<List<int>>> periodData = {
      PeriodType.annual: [
        [30, 40, 20], // Jan - total 90
        [45, 50, 40], // Feb - total 135
        [35, 30, 55], // Mar - total 120
        [25, 35, 35], // Apr - total 95
        [40, 35, 35], // May - total 110
        [50, 45, 30], // Jun - total 125
        [35, 30, 35], // Jul - total 100
      ],
      PeriodType.half: [
        [60, 80, 40], // H1 - total 180
        [70, 65, 70], // H2 - total 205
      ],
      PeriodType.qtly: [
        [90, 120, 60], // Q1 - total 270
        [100, 95, 105], // Q2 - total 300
        [85, 110, 80], // Q3 - total 275
        [95, 85, 95], // Q4 - total 275
      ],
    };

    return periodData[selectedPeriod] ?? periodData[PeriodType.annual]!;
  }

  List<String> _getTimelineLabels() {
    Map<PeriodType, List<String>> periodLabels = {
      PeriodType.annual: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
      PeriodType.half: ['H1', 'H2'],
      PeriodType.qtly: ['Q1', 'Q2', 'Q3', 'Q4'],
    };

    return periodLabels[selectedPeriod] ?? periodLabels[PeriodType.annual]!;
  }

  double _getMaxYValue() {
    Map<PeriodType, double> maxValues = {
      PeriodType.annual: 180,
      PeriodType.half: 220,
      PeriodType.qtly: 320,
    };
    return maxValues[selectedPeriod] ?? 180;
  }

  double _getYAxisInterval() {
    Map<PeriodType, double> intervals = {
      PeriodType.annual: 45,
      PeriodType.half: 55,
      PeriodType.qtly: 80,
    };
    return intervals[selectedPeriod] ?? 45;
  }

  List<BarChartGroupData> _generateStackedBarGroups() {
    // Get data based on selected period and category
    final stackedData = _getTimelineData();

    return stackedData.asMap().entries.map((entry) {
      final monthData = entry.value;
      final projects = monthData[0].toDouble();
      final gsis = monthData[1].toDouble();
      final cus = monthData[2].toDouble();

      // Always show all categories in stacked format
      List<BarChartRodStackItem> stackItems = [
        BarChartRodStackItem(
            0, projects, Color(0xFFFFD54F)), // Yellow/Orange (Projects)
        BarChartRodStackItem(
            projects, projects + gsis, Color(0xFF4FC3F7)), // Blue (GSIs)
        BarChartRodStackItem(projects + gsis, projects + gsis + cus,
            Color(0xFF81C784)), // Green (CUs)
      ];
      double totalHeight = projects + gsis + cus;

      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: totalHeight,
            color: Color(0xFF81C784), // Green (top)
            width: 20,
            borderRadius: BorderRadius.circular(10),
            rodStackItems: stackItems,
          ),
        ],
      );
    }).toList();
  }

  Widget _buildImageContainer(
      String imagePath, Color fallbackColor, IconData fallbackIcon) {
    // Calculate dimensions maintaining aspect ratio 28.34:42.91
    const double aspectRatio = 28.34 / 42.91; // ≈ 0.66
    const double containerHeight = 40.0;
    const double containerWidth = containerHeight * aspectRatio; // ≈ 26.4

    return Container(
      width: containerWidth,
      height: containerHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: SvgPicture.asset(
          imagePath,
          width: containerWidth,
          height: containerHeight,
          fit: BoxFit.cover,
          placeholderBuilder: (BuildContext context) => Container(
            width: containerWidth,
            height: containerHeight,
            decoration: BoxDecoration(
              color: fallbackColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(fallbackIcon, color: Colors.white, size: 16),
          ),
        ),
      ),
    );
  }
}
