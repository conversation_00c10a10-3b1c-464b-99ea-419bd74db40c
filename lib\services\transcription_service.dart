import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:nsl/config/environment.dart';
import '../models/multimedia/transcription_response_model.dart';
import '../services/auth_service.dart';
import '../services/dio_client.dart';
import '../utils/logger.dart';

/// Service for handling transcription API calls
class TranscriptionService {
  final Dio _dio = DioClient().client;
  final AuthService _authService = AuthService();

  // API endpoint - using the new adapter endpoint
  final String _transcriptionAdapterUrl =
      "${Environment.adaptersBaseUrl}/adapters";

  /// Transcribe audio to text using the adapter-based API
  ///
  /// [audioData] - The audio file data as Uint8List
  /// [fileName] - The filename for the audio file
  /// [languageHint] - Language hint for transcription (e.g., 'en', 'hi', 'te')
  /// [enableCommandProcessing] - Whether to enable command processing
  /// [enableTranslation] - Whether to enable translation
  ///
  /// Returns a Map with:
  /// - success: Whether the transcription was successful
  /// - transcription: The transcribed text (if successful)
  /// - message: Error message (if unsuccessful)
  /// - model: The TranscriptionResponseModel object (if successful)
  Future<Map<String, dynamic>> transcribeAudio({
    required Uint8List audioData,
    required String fileName,
    String languageHint = 'en',
    bool enableCommandProcessing = true,
    bool enableTranslation = false,
  }) async {
    try {
      // Get the token
      final token = await _authService.getValidToken();

      // if (token == null) {
      //   Logger.error('No valid token available for transcription');
      //   return {
      //     'success': false,
      //     'message': 'Authentication required',
      //   };
      // }

      // Create the request JSON structure
      final requestJson = {
        'adapter_config': {
          'service': 'intelligent_voice',
          'operation': 'process_audio',
          'version': 'v1',
        },
        'parameters': {
          'language_hint': languageHint,
          'enable_command_processing': enableCommandProcessing,
          'enable_translation': enableTranslation,
        },
      };

      // Create form data with the new structure
      final formData = FormData.fromMap({
        'request': jsonEncode(requestJson),
        'audio': MultipartFile.fromBytes(
          audioData,
          filename: fileName,
        ),
      });

      // Set up headers
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'multipart/form-data',
      };

      Logger.info(
          'Starting transcription for file: $fileName with language: $languageHint');

      // Make the API call
      final response = await _dio.post(
        _transcriptionAdapterUrl,
        data: formData,
        options: Options(
          headers: headers,
        ),
      );

      // Check if the response is successful
      if (response.statusCode == 200) {
        Logger.info('Transcription successful');
        Logger.info(
            'Transcription response structure: ${response.data.keys.join(', ')}');

        // Parse the response using the TranscriptionResponseModel
        final transcriptionModel =
            TranscriptionResponseModel.fromJson(response.data);

        // Get the transcribed text from the model
        final transcribedText = transcriptionModel.data?.transcription ??
            transcriptionModel.data?.processedText ??
            '';

        return {
          'success': true,
          'transcription': transcribedText,
          'model': transcriptionModel,
        };
      } else if (response.statusCode == 401) {
        // Handle 401 Unauthorized - try to refresh token and retry
        Logger.info(
            'Received 401 Unauthorized, attempting to refresh token and retry');

        final newToken = await _authService.getValidToken();
        if (newToken != null) {
          Logger.info(
              'Token refreshed successfully, retrying transcription request');

          // Update headers with new token
          final retryHeaders = {
            'Authorization': 'Bearer $newToken',
            'Content-Type': 'multipart/form-data',
          };

          // Retry the request
          final retryResponse = await _dio.post(
            _transcriptionAdapterUrl,
            data: formData,
            options: Options(
              headers: retryHeaders,
            ),
          );

          if (retryResponse.statusCode == 200) {
            Logger.info('Transcription successful after retry');

            final transcriptionModel =
                TranscriptionResponseModel.fromJson(retryResponse.data);
            final transcribedText = transcriptionModel.data?.transcription ??
                transcriptionModel.data?.processedText ??
                '';

            return {
              'success': true,
              'transcription': transcribedText,
              'model': transcriptionModel,
            };
          } else {
            final errorMessage =
                'Transcription failed after retry: ${retryResponse.statusCode}';
            Logger.error(errorMessage);

            // Display alert with response message for non-200 status codes
            String alertMessage = errorMessage;
            if (retryResponse.data != null &&
                retryResponse.data is Map &&
                retryResponse.data.containsKey('message')) {
              alertMessage = retryResponse.data['message'];
            }

            return {
              'success': false,
              'message': alertMessage,
            };
          }
        } else {
          Logger.error('Failed to refresh token for transcription');
          return {
            'success': false,
            'message': 'Authentication failed. Please login again.',
          };
        }
      } else {
        final errorMessage = 'Transcription failed: ${response.statusCode}';
        Logger.error(errorMessage);

        // Display alert with response message for non-200 status codes
        String alertMessage = errorMessage;
        if (response.data != null &&
            response.data is Map &&
            response.data.containsKey('message')) {
          alertMessage = response.data['message'];
        }

        return {
          'success': false,
          'message': alertMessage,
        };
      }
    } catch (e) {
      Logger.error('Exception during transcription: $e');

      // Handle specific error types for better user experience
      String errorMessage = 'An error occurred during transcription';

      if (e.toString().contains('DioException')) {
        if (e.toString().contains('SocketException')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else if (e.toString().contains('Connection refused')) {
          errorMessage =
              'Transcription service is unreachable. Please try again later.';
        } else if (e.toString().contains('404')) {
          errorMessage =
              'Transcription service not found. Please contact support.';
        } else if (e.toString().contains('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        }
      }

      return {
        'success': false,
        'message': errorMessage,
      };
    }
  }
}
