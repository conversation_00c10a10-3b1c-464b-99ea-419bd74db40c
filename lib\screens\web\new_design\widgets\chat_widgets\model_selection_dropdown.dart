import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class ModelInfo {
  final String id;
  final String name;
  final String description;
  final bool isPro;
  final bool isSelected;

  ModelInfo({
    required this.id,
    required this.name,
    required this.description,
    this.isPro = false,
    this.isSelected = false,
  });
}

class ModelSelectionDropdown extends StatefulWidget {
  final List<ModelInfo> models;
  final ModelInfo selectedModel;
  final Function(ModelInfo) onModelSelected;
  final bool dropdownAbove;

  const ModelSelectionDropdown({
    super.key,
    required this.models,
    required this.selectedModel,
    required this.onModelSelected,
    this.dropdownAbove = false,
  });

  @override
  State<ModelSelectionDropdown> createState() => _ModelSelectionDropdownState();
}

class _ModelSelectionDropdownState extends State<ModelSelectionDropdown>
    with RouteAware, WidgetsBindingObserver {
  bool isDropdownOpen = false;
  bool isHovered = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  RouteObserver<PageRoute>? _routeObserver;

  bool isMobile(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width < 600;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      _routeObserver = RouteObserver<PageRoute>();
      _routeObserver?.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _routeObserver?.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void deactivate() {
    _closeDropdown();
    super.deactivate();
  }

  @override
  void didPushNext() => _closeDropdown();
  @override
  void didPop() => _closeDropdown();
  @override
  void didPopNext() => _closeDropdown();
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _closeDropdown();
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  void _closeDropdown() {
    if (isDropdownOpen) {
      _removeOverlay();
      if (mounted) {
        setState(() {
          isDropdownOpen = false;
        });
      }
    }
  }

  void _toggleDropdown() {
    isDropdownOpen ? _closeDropdown() : _showDropdown();
  }

  void _showDropdown() {
    if (!mounted) return;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      isDropdownOpen = true;
    });
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);
    var size = renderBox.size;
    const double dropdownHeight = 120.0;
    bool mobile = isMobile(context);

    return OverlayEntry(
        builder: (context) => GestureDetector(
              onTap: _closeDropdown,
              behavior: HitTestBehavior.translucent,
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.transparent,
                child: Stack(
                  children: [
                    Positioned(
                      left: mobile ? offset.dx - 190 : offset.dx - 218,
                      top: widget.dropdownAbove
                          ? offset.dy - dropdownHeight + 10
                          : offset.dy + size.height + 6,
                      width: mobile ? 280 : 320,
                      child: GestureDetector(
                        onTap: () {},
                        child: Material(
                          color: Colors.transparent,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(7),
                              border: Border.all(color: Colors.grey.shade400),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: Material(
                                color: Colors.white,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ...widget.models.map((model) {
                                      return InkWell(
                                        onTap: () {
                                          widget.onModelSelected(model);
                                          _closeDropdown();
                                        },
                                        child: Container(
                                          width: double.infinity,
                                          padding: mobile
                                              ? EdgeInsets.symmetric(
                                                  horizontal: AppSpacing.sm, vertical: AppSpacing.xs)
                                              : EdgeInsets.symmetric(
                                                  horizontal: 16, vertical: 9),
                                          child: Row(
                                            children: mobile
                                                ? [
                                                    if (model.id ==
                                                        widget.selectedModel.id)
                                                      Icon(Icons.check,
                                                          color: Colors.black,
                                                          size: 16),
                                                    SizedBox(width: AppSpacing.xs),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .end,
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              if (model.isPro)
                                                                Container(
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          horizontal:
                                                                              6,
                                                                          vertical:
                                                                              2),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .orange
                                                                        .shade100,
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(4),
                                                                  ),
                                                                  child: Text(
                                                                    'PRO',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          10,
                                                                      fontFamily:
                                                                          'TimposText',
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      color: Colors
                                                                          .orange
                                                                          .shade800,
                                                                    ),
                                                                  ),
                                                                ),
                                                              SizedBox(
                                                                  width: 6),
                                                              Text(
                                                                model.name,
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontFamily:
                                                                      'TimposText',
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  color: Colors
                                                                      .black,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(height: 2),
                                                          Text(
                                                            model.description,
                                                            textAlign:
                                                                TextAlign.right,
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              fontFamily:
                                                                  'TimposText',
                                                              color: Colors.grey
                                                                  .shade600,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ]
                                                : [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Row(
                                                            children: [
                                                              Text(
                                                                model.name,
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontFamily:
                                                                      'TimposText',
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  color: Colors
                                                                      .black,
                                                                ),
                                                              ),
                                                              if (model
                                                                  .isPro) ...[
                                                                SizedBox(
                                                                    width: 8),
                                                                Container(
                                                                  padding: EdgeInsets
                                                                      .symmetric(
                                                                          horizontal:
                                                                              6,
                                                                          vertical:
                                                                              2),
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .orange
                                                                        .shade100,
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(4),
                                                                  ),
                                                                  child: Text(
                                                                    'PRO',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          10,
                                                                      fontFamily:
                                                                          'TimposText',
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                      color: Colors
                                                                          .orange
                                                                          .shade800,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ],
                                                          ),
                                                          SizedBox(height: 2),
                                                          Text(
                                                            model.description,
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              fontFamily:
                                                                  'TimposText',
                                                              color: Colors.grey
                                                                  .shade600,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    if (model.id ==
                                                        widget.selectedModel.id)
                                                      Icon(Icons.check,
                                                          color: Colors
                                                              .grey.shade600,
                                                          size: 16),
                                                  ],
                                          ),
                                        ),
                                      );
                                    }),
                                    InkWell(
                                      onTap: _closeDropdown,
                                      child: Container(
                                        width: double.infinity,
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 12),
                                        child: Row(
                                          children: mobile
                                              ? [
                                                  SvgPicture.asset(
                                                    "assets/images/chat/arrow_left_mobile.svg",
                                                    height: 12,
                                                    width: 12,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                            Colors.black,
                                                            BlendMode.srcIn),
                                                  ),
                                                  Spacer(),
                                                  Text(
                                                    'More models',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontFamily: 'TimposText',
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                ]
                                              : [
                                                  Text(
                                                    'More models',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontFamily: 'TimposText',
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  Spacer(),
                                                  Icon(
                                                    Icons.arrow_forward_ios,
                                                    size: 14,
                                                    color: Colors.grey.shade600,
                                                  ),
                                                ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  @override
  Widget build(BuildContext context) {
    final bool mobile = isMobile(context);
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSpacing.xs),
              border: Border.all(
                color: mobile
                    ? Color(0xFFE4E7EA) // Mobile border color
                    : (isHovered || isDropdownOpen
                        ? Colors.grey.shade400
                        : Colors.transparent),
                width: 1,
              ),
            ),
            padding: mobile
                ? EdgeInsets.symmetric(
                    horizontal: AppSpacing.xs, vertical: AppSpacing.xs)
                : EdgeInsets.symmetric(horizontal: 14, vertical: AppSpacing.xs),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.selectedModel.name,
                  style: FontManager.getCustomStyle(
                      fontSize: mobile ? FontManager.s12 : FontManager.s14,
                      color: mobile ? Colors.black : Colors.grey.shade700,
                      fontWeight:
                          mobile ? FontManager.medium : FontManager.regular,
                      fontFamily:FontManager.fontFamilyInter),
                ),
                SizedBox(width: AppSpacing.xxs),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: mobile ? Colors.black : Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
