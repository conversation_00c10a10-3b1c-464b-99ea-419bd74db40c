import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';

/// A side panel widget to display OCR text with a copy button
class OcrTextSidePanel extends StatelessWidget {
  /// The OCR text to display
  final String ocrText;

  /// The file name
  final String fileName;

  /// Constructor
  const OcrTextSidePanel({
    super.key,
    required this.ocrText,
    required this.fileName,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title
          _buildHeader(context),

          // Content in a scrollable container
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // File name section
                  _buildSection(
                    title: 'File Name',
                    content: fileName,
                    hasCopyButton: false,
                  ),

                  // OCR Text section
                  _buildSection(
                    title: 'Extracted Text',
                    content: ocrText.isEmpty ? 'No text extracted' : ocrText,
                    hasCopyButton: ocrText.isNotEmpty,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the header with title
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.md,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'File Name',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          Icon(Icons.description, size: 20, color: Colors.grey.shade700),
        ],
      ),
    );
  }

  /// Build a section with title and content
  Widget _buildSection({
    required String title,
    required String content,
    required bool hasCopyButton,
  }) {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Text(
                '• $title',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade800,
                ),
              ),
              if (hasCopyButton) ...[
                Spacer(),
                _buildCopyButton(content),
              ],
            ],
          ),
          SizedBox(height: AppSpacing.xs),

          // Section content
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: content.isEmpty
                ? Text(
                    'Null',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade800,
                    ),
                  )
                : SelectableText(
                    content,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade800,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// Build a copy button
  Widget _buildCopyButton(String textToCopy) {
    return InkWell(
      onTap: () {
        Clipboard.setData(ClipboardData(text: textToCopy));
        Logger.info('Copied text to clipboard');
      },
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: EdgeInsets.all(4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.content_copy,
              size: 14,
              color: Colors.grey.shade600,
            ),
            SizedBox(width: 4),
            Text(
              'Copy',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
