import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/screens/web_transaction/workflow_transaction_screen.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/chat_input_field.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class SolutionWidgets extends StatefulWidget {
  const SolutionWidgets({super.key});

  @override
  State<SolutionWidgets> createState() => _SolutionWidgetsState();
}

class _SolutionWidgetsState extends State<SolutionWidgets> {
  bool isAudioLoading = false;
  // JSON data variables
  Map<String, dynamic>? jsonData;
  Map<String, dynamic>? userInterfaceData;
  List<Map<String, dynamic>> optionButtonsData = [];
  List<Map<String, dynamic>> recommendationData = [];
  List<Map<String, dynamic>> actionButtonsData = [];

  // Chat variables
  final List<String> _messages = [];
  final TextEditingController chatController = TextEditingController();
  final FocusNode _chatFocusNode = FocusNode();

  // Tab selection state
  int selectedTabIndex = -1;

  // Recommendation expansion state
  bool _isRecommendationExpanded = false;
  final double _recommendationMaxHeight = 400.0; // Maximum height limit
  final double _recommendationMinHeight = 200.0; // Minimum content height

  @override
  void initState() {
    super.initState();
    _loadJsonData();
  }

  @override
  void dispose() {
    chatController.dispose();
    _chatFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadJsonData() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/solution_components.json');
      final Map<String, dynamic> data = jsonDecode(jsonString);

      setState(() {
        jsonData = data;

        // Extract data from JSON
        final leaveAnalytics = data['leave_analytics_dashboard'];
        final uiComponents = leaveAnalytics?['user_interface_components'];
        final embeddedForm = uiComponents?['chat_interface']?['embedded_form'];

        userInterfaceData = uiComponents;

        // Map quick_actions to optionButtonsData
        final quickActions = embeddedForm?['quick_actions'] as List<dynamic>?;
        if (quickActions != null) {
          optionButtonsData = quickActions
              .map((action) => {
                    'type': 'card',
                    'icon': 'assets/images/my_business/box_add.svg',
                    'label': action.toString(),
                    'onTap': () {
                      print("$action clicked");
                    },
                  })
              .toList();
        }

        // Map info_notifications to recommendationData
        final infoNotifications =
            embeddedForm?['info_notifications'] as List<dynamic>?;
        if (infoNotifications != null) {
          recommendationData = infoNotifications
              .map((notification) => {
                    'type': 'text',
                    'icon': notification['icon'] ?? '',
                    'value': notification['message'] ?? '',
                    'notificationType': notification['type'] ?? 'info',
                  })
              .toList();
        }

        // Map action_buttons to actionButtonsData
        final actionButtons = embeddedForm?['action_buttons'] as List<dynamic>?;
        if (actionButtons != null) {
          actionButtonsData = actionButtons
              .map((button) => {
                    'text': button['text'] ?? '',
                    'type': button['type'] ?? 'secondary',
                    'action': button['action'] ?? '',
                    'image': button['image'] ?? '',
                  })
              .toList();
        }
      });

      print("JSON data loaded successfully");
    } catch (e) {
      print("Error loading JSON data: $e");
      // Fallback to default data
      _setDefaultData();
    }
  }

  void _setDefaultData() {
    optionButtonsData = [
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Check Policy',
        'onTap': () {
          print("Check Policy clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Add to Calendar',
        'onTap': () {
          print("Add to Calendar clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Use AI Suggestion',
        'onTap': () {
          print("AI Suggestion clicked");
        },
      },
    ];

    recommendationData = [
      {'type': 'text', 'value': '2-Day Overlap With John (Lead Developer)'},
      {'type': 'text', 'value': 'Manager Is Available For Approval All Week'},
      {
        'type': 'text',
        'value': 'You\'ll Have 12 Days Remaining After This Request'
      },
    ];
  }

  void _handleSendMessage() {
    final text = chatController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        chatController.clear();
      });
    }
  }

  // Build data analytics content for Related tab
  String _buildDataAnalyticsContent(Map<String, dynamic>? dataAnalytics) {
    if (dataAnalytics == null) return "No data analytics available.";

    StringBuffer content = StringBuffer();

    // Leave Analytics Card
    final leaveAnalyticsCard = dataAnalytics['leave_analytics_card'];
    if (leaveAnalyticsCard != null) {
      content.writeln("${leaveAnalyticsCard['header'] ?? 'LEAVE ANALYTICS'}\n");

      final personalBalance = leaveAnalyticsCard['personal_balance'];
      if (personalBalance != null) {
        content.writeln("Personal Balance:");
        content.writeln(
            "• Current Usage: ${personalBalance['current_usage'] ?? 'N/A'}");
        content
            .writeln("• Percentage: ${personalBalance['percentage'] ?? 'N/A'}");
        content.writeln(
            "• Remaining After Request: ${personalBalance['remaining_after_request'] ?? 'N/A'}\n");
      }
    }

    // Team Availability Card
    final teamAvailabilityCard = dataAnalytics['team_availability_card'];
    if (teamAvailabilityCard != null) {
      content.writeln("TEAM AVAILABILITY\n");
      content.writeln(
          "Time Period: ${teamAvailabilityCard['time_period'] ?? 'N/A'}\n");

      final teamMembers =
          teamAvailabilityCard['team_members'] as List<dynamic>?;
      if (teamMembers != null) {
        content.writeln("Team Members:");
        for (var member in teamMembers) {
          content.writeln("• ${member['name']} (${member['role']})");
          content
              .writeln("  Availability: ${member['availability_percentage']}");
          content.writeln("  Status: ${member['status']}\n");
        }
      }
    }

    // Conflicts & Warnings Card
    final conflictsCard = dataAnalytics['conflicts_warnings_card'];
    if (conflictsCard != null) {
      content.writeln("⚠️ CONFLICTS & WARNINGS\n");

      final conflicts =
          conflictsCard['high_priority_conflicts'] as List<dynamic>?;
      if (conflicts != null) {
        content.writeln("High Priority Conflicts:");
        for (var conflict in conflicts) {
          content.writeln(
              "• ${conflict['description']} (${conflict['severity']})");
        }
        content.writeln();
      }

      final aiSuggestion = conflictsCard['ai_suggestion'];
      if (aiSuggestion != null) {
        content.writeln("💡 AI Suggestion:");
        content.writeln("${aiSuggestion['recommendation'] ?? 'N/A'}\n");

        final benefits = aiSuggestion['benefits'] as List<dynamic>?;
        if (benefits != null) {
          content.writeln("Benefits:");
          for (var benefit in benefits) {
            content.writeln("• $benefit");
          }
        }
      }
    }

    return content.toString();
  }

  // Build system architecture content for Contextual tab
  String _buildSystemArchitectureContent(
      Map<String, dynamic>? systemArchitecture) {
    if (systemArchitecture == null)
      return "No system architecture information available.";

    StringBuffer content = StringBuffer();
    content.writeln("🏗️ SYSTEM ARCHITECTURE\n");

    content.writeln(
        "${systemArchitecture['description'] ?? 'Backend infrastructure supporting the leave analytics dashboard'}\n");

    // Real-time Processing
    final realTimeProcessing = systemArchitecture['real_time_processing'];
    if (realTimeProcessing != null) {
      content.writeln("⚡ REAL-TIME PROCESSING\n");

      final dataSources = realTimeProcessing['data_sources'] as List<dynamic>?;
      if (dataSources != null) {
        content.writeln("Data Sources:");
        for (var source in dataSources) {
          content.writeln("• $source");
        }
        content.writeln();
      }

      final updateFreqs = realTimeProcessing['update_frequencies'];
      if (updateFreqs != null) {
        content.writeln("Update Frequencies:");
        updateFreqs.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // AI Processing
    final aiProcessing = systemArchitecture['ai_processing'];
    if (aiProcessing != null) {
      content.writeln("🤖 AI PROCESSING\n");

      final intentRecognition = aiProcessing['intent_recognition'];
      if (intentRecognition != null) {
        content.writeln("Intent Recognition:");
        content.writeln("• Input: ${intentRecognition['input'] ?? 'N/A'}");
        content.writeln(
            "• Confidence: ${intentRecognition['confidence'] ?? 'N/A'}\n");

        final parsedEntities = intentRecognition['parsed_entities'];
        if (parsedEntities != null) {
          content.writeln("Parsed Entities:");
          parsedEntities.forEach((key, value) {
            content.writeln("• ${key.toUpperCase()}: $value");
          });
          content.writeln();
        }
      }

      final suggestionEngine = aiProcessing['suggestion_engine'];
      if (suggestionEngine != null) {
        content.writeln("Suggestion Engine:");
        suggestionEngine.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // Integration Points
    final integrationPoints =
        systemArchitecture['integration_points'] as List<dynamic>?;
    if (integrationPoints != null) {
      content.writeln("🔗 INTEGRATION POINTS\n");
      for (var point in integrationPoints) {
        content.writeln("• $point");
      }
      content.writeln();
    }

    // Performance Metrics
    final performanceMetrics = systemArchitecture['performance_metrics'];
    if (performanceMetrics != null) {
      content.writeln("📈 PERFORMANCE METRICS\n");

      final responseTimes = performanceMetrics['response_times'];
      if (responseTimes != null) {
        content.writeln("Response Times:");
        responseTimes.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }

      final dataFreshness = performanceMetrics['data_freshness'];
      if (dataFreshness != null) {
        content.writeln("Data Freshness:");
        dataFreshness.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
      }
    }

    return content.toString();
  }

  void _showBottomSheet(String tabType, int tabIndex) {
    String content = "";
    String title = "";

    if (tabType == "Related") {
      final leaveAnalytics = jsonData?['leave_analytics_dashboard'];
      final dataAnalytics = leaveAnalytics?['data_analytics'];
      content = _buildDataAnalyticsContent(dataAnalytics);
      title = "Related Information";
    } else if (tabType == "Contextual") {
      final leaveAnalytics = jsonData?['leave_analytics_dashboard'];
      final systemArchitecture = leaveAnalytics?['system_architecture'];
      content = _buildSystemArchitectureContent(systemArchitecture);
      title = "Contextual Information";
    }

    // Set the selected tab index
    setState(() {
      selectedTabIndex = tabIndex;
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BottomSheetContent(
        title: title,
        content: content,
        scrollController: null,
      ),
    ).then((_) {
      // Reset selection when bottom sheet is closed
      setState(() {
        selectedTabIndex = -1;
      });
    });
  }

  void _toggleRecommendationExpansion() {
    setState(() {
      _isRecommendationExpanded = !_isRecommendationExpanded;
    });
  }

  // Calculate dynamic height for recommendation section
  double _calculateRecommendationHeight() {
    // Base height for header and padding
    double baseHeight = 60.0; // Header + padding

    // Calculate content height based on number of items
    int itemCount = optionButtonsData.length;
    double itemHeight = 40.0; // Approximate height per item
    double spacing = 12.0; // Spacing between items

    // Calculate rows needed (assuming 2-3 items per row based on screen width)
    int itemsPerRow = 2; // Conservative estimate
    int rows = (itemCount / itemsPerRow).ceil();

    double contentHeight = (rows * itemHeight) + ((rows - 1) * spacing) + 32.0; // Extra padding
    double totalHeight = baseHeight + contentHeight;

    // Return the smaller of calculated height or max height
    return totalHeight > _recommendationMaxHeight
        ? _recommendationMaxHeight
        : totalHeight.clamp(_recommendationMinHeight, _recommendationMaxHeight);
  }

  @override
  Widget build(BuildContext context) {
    final String latestMessage = _messages.isNotEmpty ? _messages.last : "";
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom > 0;

    return NSLKnowledgeLoaderWrapper(
       isLoading: isAudioLoading,
      child: Scaffold(
        resizeToAvoidBottomInset: false, // Prevent recommendations from moving
        backgroundColor: Color(0xffF7F9FB),
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          leading: Builder(
            builder: (context) => IconButton(
              icon: Icon(Icons.menu),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            ),
          ),
          title: Text(''),
          backgroundColor: Color(0xffF7F9FB),
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.black),
        ),
        drawer: CustomDrawer(),
        body: SafeArea(
          child: Column(
            children: [
              // Fixed toggle tabs at the top
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                ),
                child: ToggleTabs(onTabTapped: _showBottomSheet),
              ),
      
              // Scrollable content in the middle
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Chat message display
                      Container(
                        margin: EdgeInsets.only(top: AppSpacing.sm),
                        color: const Color(0xffF7F9FB),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.85,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFE9F2F7),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CircleAvatar(
                                  backgroundColor: const Color(0xFF0058FF),
                                  radius: 12,
                                  child: Text(
                                    'D',
                                    style: FontManager.getCustomStyle(
                                      color: Colors.white,
                                      fontSize: FontManager.s14,
                                      fontWeight: FontManager.regular,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    latestMessage.isNotEmpty
                                        ? latestMessage
                                        : "Type a message below...",
                                    style: FontManager.getCustomStyle(
                                      fontSize: FontManager.s14,
                                      fontWeight: FontManager.regular,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
      
                      // Widget Component
                      WidgetComponent(uiData: userInterfaceData),
      
                      // SizedBox(height: AppSpacing.xs),
      
                      // Recommendation Box
                      RecommendationBox(data: recommendationData),
      
                      // Add some bottom padding for the scrollable content
                      SizedBox(height: AppSpacing.md),
                    ],
                  ),
                ),
              ),
      
              // Fixed recommendation button above chat input
              if (!isKeyboardOpen)
                Container(
                  padding: EdgeInsets.only(
                      left: AppSpacing.sm,
                      right: AppSpacing.sm,
                      top: AppSpacing.xs),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _toggleRecommendationExpansion,
                      style: ElevatedButton.styleFrom(
                          backgroundColor: _isRecommendationExpanded
                              ? Color(0xFFF0F8FF) // Light blue background when expanded
                              : Colors.white, // White background when collapsed
                          foregroundColor: Colors.black,
                          elevation: 0,
                          side: BorderSide(color: Colors.grey.shade200),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSpacing.sm),
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 16),
                          alignment: Alignment.centerLeft),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                "assets/images/my_business/solutions/recommendation_ai.svg",
                                height: 16,
                                width: 16,
                                // colorFilter: ColorFilter.mode(
                                //   textColor,
                                //   BlendMode.srcIn,
                                // ),
                              ),
                              SizedBox(width: AppSpacing.xs),
                              Text(
                                "Recommendation",
                                style: FontManager.getCustomStyle(
                                  fontSize: FontManager.s14,
                                  fontWeight: FontManager.medium,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                          // Show cross icon when expanded, otherwise show nothing
                          if (_isRecommendationExpanded)
                            Icon(
                              Icons.close,
                              color: Colors.black,
                              size: 20,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
      
              // Expandable recommendation section with dynamic height
              if (!isKeyboardOpen)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                  child: AnimatedContainer(
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    height: _isRecommendationExpanded
                        ? _calculateRecommendationHeight()
                        : 0,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppSpacing.sm),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: _isRecommendationExpanded
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(AppSpacing.sm),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header (fixed)
                                Padding(
                                  padding: EdgeInsets.all(AppSpacing.md),
                                  child: Text(
                                    "Quick Actions",
                                    style: FontManager.getCustomStyle(
                                      fontSize: FontManager.s14,
                                      fontWeight: FontManager.medium,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                ),
                                // Scrollable content area
                                Expanded(
                                  child: SingleChildScrollView(
                                    padding: EdgeInsets.only(
                                      left: AppSpacing.md,
                                      right: AppSpacing.md,
                                      bottom: AppSpacing.md,
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Wrap(
                                          spacing: 12,
                                          runSpacing: 12,
                                          children: optionButtonsData.map((data) {
                                            return OptionComponent(data: data);
                                          }).toList(),
                                        ),
                                        SizedBox(height: AppSpacing.sm),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SizedBox.shrink(),
                  ),
                ),
      
              // 👇 Chat input (always visible but moves with keyboard)
              Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom,
                    left: AppSpacing.sm,
                    right: AppSpacing.sm),
                child: ChatInputField(
                  focusNode: _chatFocusNode,
                  chatController: chatController,
                    parentState: this,
                  sendMessage: _handleSendMessage,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ToggleTabs extends StatefulWidget {
  final Function(String, int) onTabTapped;

  const ToggleTabs({super.key, required this.onTabTapped});

  @override
  _ToggleTabsState createState() => _ToggleTabsState();
}

class _ToggleTabsState extends State<ToggleTabs> {
  final List<Map<String, String>> tabData = [
    {
      "label": "Related",
      "iconPath": 'assets/images/my_business/solutions/solution_related.svg',
    },
    {
      "label": "Contextual",
      "iconPath": 'assets/images/my_business/solutions/solution_contextual.svg',
    },
  ];

  void onTabTapped(int index) {
    // Call the callback to show bottom sheet with tab type and index
    widget.onTabTapped(tabData[index]["label"]!, index);
  }

  @override
  Widget build(BuildContext context) {
    // Get the selected index from parent widget
    final parentState =
        context.findAncestorStateOfType<_SolutionWidgetsState>();
    final isSelected = (int index) => parentState?.selectedTabIndex == index;

    return Row(
      children: List.generate(tabData.length, (index) {
        final selected = isSelected(index);
        final item = tabData[index];

        return Expanded(
          child: GestureDetector(
            onTap: () => onTabTapped(index),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4.0),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: selected ? Color(0xFF0058FF) : Colors.white,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: selected ? Color(0xFF0058FF) : Colors.grey.shade300,
                  width: 1,
                ),
                boxShadow: selected
                    ? [BoxShadow(color: Colors.black12, blurRadius: 5)]
                    : [],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    item["iconPath"]!,
                    height: 18,
                    width: 18,
                    color: selected ? Colors.white : Colors.blue,
                  ),
                  SizedBox(width: 6),
                  Text(
                    item["label"]!,
                    style: TextStyle(
                      color: selected ? Colors.white : Colors.black,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}

class WidgetComponent extends StatelessWidget {
  final Map<String, dynamic>? uiData;

  const WidgetComponent({super.key, this.uiData});

  @override
  Widget build(BuildContext context) {
    // Extract data from user_interface_components
    final header = uiData?['header'];
    final chatInterface = uiData?['chat_interface'];
    final embeddedForm = chatInterface?['embedded_form'];
    final formHeader = embeddedForm?['form_header'];
    final formFields = embeddedForm?['form_fields'];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Form header from JSON
          if (formHeader != null) ...[
            Text(
              formHeader,
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.medium,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Form fields from JSON
          if (formFields != null) ...[
            // Leave type dropdown
            if (formFields['leave_type'] != null) ...[
              Text(
                'Leave Type',
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  formFields['leave_type']['selected'] ?? 'Annual Leave',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontFamily: FontManager.fontFamilyInter,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Date range
            if (formFields['date_range'] != null) ...[
              Text(
                'Date Range',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        formFields['date_range']['start_date'] ?? 'Start Date',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'to',
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: FontManager.s12,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        formFields['date_range']['end_date'] ?? 'End Date',
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontFamily: FontManager.fontFamilyInter,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Reason field
            if (formFields['reason'] != null) ...[
              Text(
                'Reason',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s12,
                  fontWeight: FontManager.regular,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyInter,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  formFields['reason']['value'] ?? 'Enter reason...',
                  style: FontManager.getCustomStyle(
                    fontSize: FontManager.s14,
                    fontFamily: FontManager.fontFamilyInter,
                    color: formFields['reason']['value'] != null
                        ? Colors.black
                        : Colors.grey.shade500,
                  ),
                ),
              ),
            ],
          ],

          // Fallback content if no JSON data
          if (uiData == null) ...[
            Text("LEAVE REQUEST",
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontWeight: FontManager.semiBold,
                )),
            const SizedBox(height: 12),
            Text(
              "Form fields will be populated from JSON data",
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class OptionComponent extends StatelessWidget {
  final Map<String, dynamic> data;

  const OptionComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    switch (data['type']) {
      case 'card':
        return GestureDetector(
          onTap: data['onTap'],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  data['icon'],
                  width: 16,
                  height: 16,
                  colorFilter: const ColorFilter.mode(
                      Color(0xFF0058FF), BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  data['label'],
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: FontManager.s12,
                    color: Colors.black,
                    fontWeight: FontManager.regular,
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class RecommendationBox extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const RecommendationBox({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7DA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.map((item) {
          switch (item['type']) {
            case 'text':
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    if (item['icon'] != null &&
                        item['icon'].toString().isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Text(
                          item['icon'],
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s12,
                            fontFamily: FontManager.fontFamilyInter,
                          ),
                        ),
                      ),
                    Expanded(
                      child: Text(
                        item['value'],
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          fontFamily: FontManager.fontFamilyInter,
                          fontWeight: FontManager.medium,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            default:
              return const SizedBox.shrink();
          }
        }).toList(),
      ),
    );
  }
}

class BottomSheetContent extends StatefulWidget {
  final String title;
  final String content;
  final ScrollController? scrollController;

  const BottomSheetContent({
    super.key,
    required this.title,
    required this.content,
    this.scrollController,
  });

  @override
  State<BottomSheetContent> createState() => _BottomSheetContentState();
}

class _BottomSheetContentState extends State<BottomSheetContent> {
    bool isAudioLoading = false;
  late final TextEditingController _bottomSheetChatController;
  late final FocusNode _bottomSheetFocusNode;

  @override
  void initState() {
    super.initState();
    _bottomSheetChatController = TextEditingController();
    _bottomSheetFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _bottomSheetChatController.dispose();
    _bottomSheetFocusNode.dispose();
    super.dispose();
  }

  void _handleBottomSheetSendMessage() {
    final text = _bottomSheetChatController.text.trim();
    if (text.isNotEmpty) {
      print("Bottom sheet message: $text");
      _bottomSheetChatController.clear();
      _bottomSheetFocusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      minChildSize: 0.5,
      maxChildSize: 0.8,
      builder: (context, scrollController) => ClipRRect(
        borderRadius:
            BorderRadius.vertical(top: Radius.circular(AppSpacing.lg)),
        child: NSLKnowledgeLoaderWrapper(
           isLoading: isAudioLoading,
          child: Scaffold(
            resizeToAvoidBottomInset: true,
            backgroundColor: Colors.white,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Handle bar
          
                // Header
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.md, vertical: AppSpacing.xs),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.title,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s13,
                          fontWeight: FontManager.medium,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
          
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      widget.content,
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s13,
                        fontFamily: FontManager.fontFamilyInter,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
          
                // Chat input with proper focus management
          
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                  child: ChatInputField(
                      chatController: _bottomSheetChatController,
                        parentState: this,
                      sendMessage: () {}),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// class RecommendationBottomSheet extends StatefulWidget {
//   final List<Map<String, dynamic>> optionButtonsData;

//   const RecommendationBottomSheet({
//     super.key,
//     required this.optionButtonsData,
//   });

//   @override
//   State<RecommendationBottomSheet> createState() => _RecommendationBottomSheetState();
// }

// class _RecommendationBottomSheetState extends State<RecommendationBottomSheet> {
//   late final TextEditingController _chatController;
//   late final FocusNode _focusNode;

//   @override
//   void initState() {
//     super.initState();
//     _chatController = TextEditingController();
//     _focusNode = FocusNode();
//   }

//   @override
//   void dispose() {
//     _chatController.dispose();
//     _focusNode.dispose();
//     super.dispose();
//   }

//   void _handleSendMessage() {
//     final text = _chatController.text.trim();
//     if (text.isNotEmpty) {
//       print("Recommendation bottom sheet message: $text");
//       _chatController.clear();
//       _focusNode.unfocus();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return DraggableScrollableSheet(
//       initialChildSize: 0.6,
//       minChildSize: 0.4,
//       maxChildSize: 0.8,
//       builder: (context, scrollController) => ClipRRect(
//         borderRadius:
//             BorderRadius.vertical(top: Radius.circular(AppSpacing.lg)),
//         child: Scaffold(
//           resizeToAvoidBottomInset: true,
//           backgroundColor: Colors.white,
//           body: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Header
//               Padding(
//                 padding: EdgeInsets.symmetric(
//                     horizontal: AppSpacing.md, vertical: AppSpacing.xs),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       "Recommendation",
//                       style: FontManager.getCustomStyle(
//                         fontSize: FontManager.s16,
//                         fontWeight: FontManager.semiBold,
//                         fontFamily: FontManager.fontFamilyTiemposText,
//                       ),
//                     ),
//                     IconButton(
//                       onPressed: () => Navigator.pop(context),
//                       icon: Icon(Icons.close),
//                     ),
//                   ],
//                 ),
//               ),

//               // Content with option buttons
//               Expanded(
//                 child: SingleChildScrollView(
//                   // controller: scrollController,
//                   padding: EdgeInsets.symmetric(horizontal: AppSpacing.md),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         "Quick Actions",
//                         style: FontManager.getCustomStyle(
//                           fontSize: FontManager.s14,
//                           fontWeight: FontManager.medium,
//                           fontFamily: FontManager.fontFamilyTiemposText,
//                           color: Colors.black,
//                         ),
//                       ),
//                       SizedBox(height: AppSpacing.sm),
//                       Wrap(
//                         spacing: 12,
//                         runSpacing: 12,
//                         children: widget.optionButtonsData.map((data) {
//                           return OptionComponent(data: data);
//                         }).toList(),
//                       ),
//                       SizedBox(height: AppSpacing.lg),
//                     ],
//                   ),
//                 ),
//               ),

//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
