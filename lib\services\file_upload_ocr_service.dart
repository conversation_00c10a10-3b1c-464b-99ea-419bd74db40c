import 'package:flutter/material.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/services/file_upload_service.dart';
import 'package:nsl/utils/logger.dart';

/// A service that handles file upload and OCR processing without showing the side panel
class FileUploadOcrService {
  final FileUploadService _fileUploadService = FileUploadService();

  /// Process a file by picking, uploading, and performing OCR in one operation
  /// Returns a Future that completes with a Map containing:
  /// - success: Whether the operation was successful
  /// - fileName: The name of the uploaded file
  /// - filePath: The path of the uploaded file
  /// - extractedText: The text extracted from the image (if OCR was successful)
  /// - errorMessage: An error message (if an error occurred)
  Future<Map<String, dynamic>> processFileForChat() async {
    try {
      // Pick a file
      final file = await _fileUploadService.pickFile();

      if (file == null) {
        // User canceled the picker
        return {
          'success': false,
          'errorMessage': 'File selection canceled',
          'isCanceled': true,
        };
      }

      // Upload the file - the API now returns OCR data directly
      final uploadResult = await _fileUploadService.uploadFile(file);

      if (!uploadResult['success']) {
        // Handle upload error
        return {
          'success': false,
          'errorMessage': 'Failed to upload file: ${uploadResult['message']}',
        };
      }

      // Get the file name, path, and extracted text directly from the upload result
      final fileName = file.name;
      final filePath = uploadResult['file_path'];
      final extractedText = uploadResult['extracted_text'];
      final fileUploadOcrResponse =
          uploadResult['file_upload_ocr_response'] as FileUploadOcrResponse;

      Logger.info('File uploaded successfully. Path: $filePath');
      Logger.info('Extracted Text: $extractedText');
      Logger.info('OCR Response: $fileUploadOcrResponse');

      // Return success with all the information
      return {
        'success': true,
        'fileName': fileName,
        'filePath': filePath,
        'extractedText': extractedText,
        'fileUploadOcrResponse': fileUploadOcrResponse,
      };
    } catch (e) {
      // Handle general error
      Logger.error('Error in file processing service: $e');
      return {
        'success': false,
        'errorMessage': 'Error processing file: $e',
      };
    }
  }

  /// Show a temporary overlay with a message
  void showOverlay(BuildContext context, String message,
      {bool isError = false}) {
    // Create an overlay entry
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 50,
        left: 0,
        right: 0,
        child: Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isError ? Colors.red.shade700 : Colors.green.shade700,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                message,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}
