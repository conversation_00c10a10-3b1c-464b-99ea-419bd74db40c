import 'package:flutter/material.dart';
import 'dart:convert';

/// Provider for managing library counts across all library types
class LibraryCountsProvider extends ChangeNotifier {
  int _booksCount = 0;
  int _solutionsCount = 0;
  int _objectsCount = 0;
  int _agentsCount = 0;
  bool _isLoaded = false;

  int get booksCount => _booksCount;
  int get solutionsCount => _solutionsCount;
  int get objectsCount => _objectsCount;
  int get agentsCount => _agentsCount;
  bool get isLoaded => _isLoaded;

  /// Load all counts from the same JSON data used by individual screens
  /// This ensures counts are available immediately when the app starts
  Future<void> loadAllCounts() async {
    if (_isLoaded) return; // Prevent multiple loads

    try {
      await Future.wait([
        _loadBooksCount(),
        _loadSolutionsCount(),
        _loadObjectsCount(),
        _loadAgentsCount(),
      ]);
      _isLoaded = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading library counts: $e');
    }
  }

  void updateBooksCount(int count) {
    if (_booksCount != count) {
      _booksCount = count;
      notifyListeners();
    }
  }

  void updateSolutionsCount(int count) {
    if (_solutionsCount != count) {
      _solutionsCount = count;
      notifyListeners();
    }
  }

  void updateObjectsCount(int count) {
    if (_objectsCount != count) {
      _objectsCount = count;
      notifyListeners();
    }
  }

  void updateAgentsCount(int count) {
    if (_agentsCount != count) {
      _agentsCount = count;
      notifyListeners();
    }
  }

  /// Load books count from the same JSON used in BooksLibraryMobile
  Future<void> _loadBooksCount() async {
    try {
      // Using the exact same JSON string as BooksLibraryMobile
      const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Platform Solutions",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false,
      "lastUpdated": "2024-12-15T10:30:00Z"
    },
    {
      "title": "Fashion & Apparel Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T14:22:00Z"
    },
    {
      "title": "Financial Advisory Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false,
      "lastUpdated": "2024-12-10T09:15:00Z"
    },
    {
      "title": "Home Rentals App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true,
      "lastUpdated": "2024-12-20T16:45:00Z"
    },
    {
      "title": "Online Grocery Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false,
      "lastUpdated": "2024-12-08T11:30:00Z"
    },
    {
      "title": "Courier & Logistics",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T13:20:00Z"
    },
    {
      "title": "Automotive Marketplace",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true,
      "lastUpdated": "2024-12-12T08:45:00Z"
    },
    {
      "title": "Fitness & Wellness App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T15:10:00Z"
    },
    {
      "title": "Real Estate Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false,
      "lastUpdated": "2024-12-07T12:00:00Z"
    },
    {
      "title": "Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-16T17:30:00Z"
    },
    {
      "title": "Healthcare Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T09:25:00Z"
    },
    {
      "title": "Education Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-05T14:40:00Z"
    },
    {
      "title": "Travel Booking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T11:15:00Z"
    },
    {
      "title": "Music Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-11T16:20:00Z"
    },
    {
      "title": "Social Media Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T10:50:00Z"
    },
    {
      "title": "Gaming Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-09T13:35:00Z"
    },
    {
      "title": "News & Media App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-14T15:45:00Z"
    },
    {
      "title": "Banking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T08:30:00Z"
    },
    {
      "title": "Investment Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-06T12:10:00Z"
    },
    {
      "title": "Delivery Service",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-13T14:55:00Z"
    },
    {
      "title": "Job Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-04T09:40:00Z"
    },
    {
      "title": "Event Management",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-25T16:15:00Z"
    },
    {
      "title": "Video Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-03T11:25:00Z"
    },
    {
      "title": "Smart Home IoT",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-02T13:50:00Z"
    },
    {
      "title": "Cryptocurrency Exchange",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false,
      "lastUpdated": "2024-12-01T10:05:00Z"
    }
  ]
}
''';

      final data = json.decode(booksJsonString);
      final books = (data['books'] as List<dynamic>);
      _booksCount = books.length;
    } catch (e) {
      _booksCount = 0;
    }
  }

  /// Load solutions count from the same JSON used in SolutionsLibraryMobile
  Future<void> _loadSolutionsCount() async {
    try {
      const String solutionsJsonString = '''
{
  "solutions": [
    {
      "title": "Ecommerce Platform Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-20T14:30:00Z"
    },
    {
      "title": "Fashion & Apparel Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00173",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T16:45:00Z"
    },
    {
      "title": "Financial Advisory Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00174",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T11:20:00Z"
    },
    {
      "title": "Healthcare Management Solution",
      "subtitle": "(B2B)",
      "versionNumber": "V00175",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-25T09:15:00Z"
    },
    {
      "title": "Education Portal Solution",
      "subtitle": "(B2B)",
      "versionNumber": "V00176",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T13:40:00Z"
    },
    {
      "title": "Real Estate Platform Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00177",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T10:25:00Z"
    },
    {
      "title": "Travel Booking Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00178",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T15:50:00Z"
    },
    {
      "title": "Food Delivery Solution",
      "subtitle": "(B2C)",
      "versionNumber": "V00179",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-23T12:30:00Z"
    }
  ]
}
''';

      final data = json.decode(solutionsJsonString);
      final solutions = (data['solutions'] as List<dynamic>);
      _solutionsCount = solutions.length;
    } catch (e) {
      _solutionsCount = 0;
    }
  }

  /// Load objects count from the same JSON used in ObjectsLibraryMobile
  Future<void> _loadObjectsCount() async {
    try {
      const String objectsJsonString = '''
{
  "objects": [
    {
      "title": "Customer Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00201",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T10:15:00Z"
    },
    {
      "title": "Product Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00202",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T14:30:00Z"
    },
    {
      "title": "Address Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00203",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T09:45:00Z"
    },
    {
      "title": "Order Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00204",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-25T16:20:00Z"
    },
    {
      "title": "Payment Object",
      "subtitle": "Financial Entity",
      "versionNumber": "V00205",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-16T11:55:00Z"
    },
    {
      "title": "Inventory Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00206",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T13:40:00Z"
    },
    {
      "title": "User Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00207",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-20T08:25:00Z"
    },
    {
      "title": "Category Object",
      "subtitle": "Classification Entity",
      "versionNumber": "V00208",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T15:10:00Z"
    },
    {
      "title": "Review Object",
      "subtitle": "Content Entity",
      "versionNumber": "V00209",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T12:35:00Z"
    },
    {
      "title": "Notification Object",
      "subtitle": "System Entity",
      "versionNumber": "V00210",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T17:50:00Z"
    }
  ]
}
''';

      final data = json.decode(objectsJsonString);
      final objects = (data['objects'] as List<dynamic>);
      _objectsCount = objects.length;
    } catch (e) {
      _objectsCount = 0;
    }
  }

  /// Load agents count from the same JSON used in AgentsLibraryMobile
  Future<void> _loadAgentsCount() async {
    try {
      const String agentsJsonString = '''
{
  "agents": [
    {
      "title": "Agent-1",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-25T10:15:00Z"
    },
    {
      "title": "Agent-2",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-24T14:30:00Z"
    },
    {
      "title": "Agent-3",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": true,
      "lastUpdated": "2024-12-28T09:45:00Z"
    },
    {
      "title": "Agent-4",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-23T16:20:00Z"
    },
    {
      "title": "Agent-5",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-21T11:55:00Z"
    },
    {
      "title": "Agent-6",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": true,
      "lastUpdated": "2024-12-20T13:40:00Z"
    },
    {
      "title": "Agent-7",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-25T08:25:00Z"
    },
    {
      "title": "Agent-8",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-09T15:10:00Z"
    },
    {
      "title": "Agent-9",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-20T12:35:00Z"
    },
    {
      "title": "Agent-10",
      "imageUrl": "assets/images/Agent-bg-img.svg",
      "isDraft": false,
      "lastUpdated": "2024-12-16T17:50:00Z"
    }
  ]
}
''';

      final data = json.decode(agentsJsonString);
      final agents = (data['agents'] as List<dynamic>);
      _agentsCount = agents.length;
    } catch (e) {
      _agentsCount = 0;
    }
  }
}
