import 'dart:async';
import 'package:flutter/material.dart';

/// A custom widget for speech recognition that shows a timer,
/// cancel button, and confirm button.
class SpeechRecognitionOverlay extends StatefulWidget {
  /// Callback when the user cancels speech recognition
  final VoidCallback onCancel;

  /// Callback when the user confirms speech recognition
  final Function(String) onConfirm;

  /// The recognized text
  final String recognizedText;

  /// Constructor
  const SpeechRecognitionOverlay({
    super.key,
    required this.onCancel,
    required this.onConfirm,
    required this.recognizedText,
  });

  @override
  State<SpeechRecognitionOverlay> createState() =>
      _SpeechRecognitionOverlayState();
}

class _SpeechRecognitionOverlayState extends State<SpeechRecognitionOverlay> {
  // Timer variables
  int _seconds = 0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    // Start the timer
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  /// Start the timer to track recording duration
  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _seconds++;
      });
    });
  }

  /// Format seconds as MM:SS
  String _formatDuration() {
    final minutes = (_seconds ~/ 60).toString().padLeft(2, '0');
    final seconds = (_seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        // border: Border.all(color: Colors.red, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      height: 56,
      width: 300, // Fixed width
      child: Row(
        children: [
          // Cancel button
          IconButton(
            icon: Icon(Icons.close, color: Colors.black),
            onPressed: widget.onCancel,
          ),

          // Timer display
          Expanded(
            child: Center(
              child: Text(
                _formatDuration(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          // Confirm button
          Container(
            margin: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(Icons.check, color: Colors.white),
              onPressed: () => widget.onConfirm(widget.recognizedText),
            ),
          ),
        ],
      ),
    );
  }
}
