/// Model class for collection navigation data
class CollectionData {
  String name;
  String backImage;

  CollectionData({
    required this.name,
    required this.backImage,
  });

  /// Create a default instance with loading state
  factory CollectionData.loading() {
    return CollectionData(
      name: "Loading...",
      backImage: "assets/images/my_business/back_arrow.svg",
    );
  }

  /// Create an instance with unknown book
  factory CollectionData.unknown() {
    return CollectionData(
      name: "Unknown Book",
      backImage: "assets/images/my_business/back_arrow.svg",
    );
  }

  /// Create an instance with a specific book name
  factory CollectionData.withBookName(String bookName) {
    return CollectionData(
      name: bookName,
      backImage: "assets/images/my_business/back_arrow.svg",
    );
  }

  /// Update the name while keeping other properties
  void updateName(String newName) {
    name = newName;
  }

  /// Check if currently in loading state
  bool get isLoading => name == "Loading...";

  /// Check if book name is unknown
  bool get isUnknown => name == "Unknown Book";

  @override
  String toString() {
    return 'CollectionData(name: $name, backImage: $backImage)';
  }
}
