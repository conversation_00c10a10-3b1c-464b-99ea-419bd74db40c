import 'package:flutter/material.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/audio_wave_visualizer_widget.dart';
import 'dart:html' as html;
import 'dart:typed_data';
import 'dart:async';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/utils/logger.dart';

/// Web-specific implementation of WebAudioRecorderWidget
class WebAudioRecorderWidgetImpl extends StatefulWidget {
  /// Callback when recording is complete
  final Function(Uint8List audioData, String fileName)? onRecordingComplete;

  /// Text editing controller to update with transcribed text
  final TextEditingController? chatController;

  /// Callback when recording is cancelled
  final VoidCallback? onCancel;

  /// Callback when loading state changes
  final Function(bool isLoading)? onLoadingChanged;

  const WebAudioRecorderWidgetImpl({
    super.key,
    this.onRecordingComplete,
    this.chatController,
    this.onCancel,
    this.onLoadingChanged,
  });

  @override
  State<WebAudioRecorderWidgetImpl> createState() =>
      _WebAudioRecorderWidgetImplState();
}

class _WebAudioRecorderWidgetImplState
    extends State<WebAudioRecorderWidgetImpl> {
  bool _isRecording = false;
  html.MediaRecorder? _recorder;
  html.AudioElement? _audioPlayer;
  List<html.Blob> _audioChunks = [];
  String? _audioUrl;
  int _seconds = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _initAudioPlayer();
    // Start recording immediately
    _startRecording();
  }

  void _initAudioPlayer() {
    _audioPlayer = html.AudioElement();
  }

  Future<void> _startRecording() async {
    try {
      // Reset previous recording data
      _audioChunks = [];
      if (_audioUrl != null) {
        html.Url.revokeObjectUrl(_audioUrl!);
        _audioUrl = null;
      }

      // Request microphone access
      final stream = await html.window.navigator.mediaDevices?.getUserMedia({
        'audio': true,
      });

      if (stream != null) {
        // Create recorder with options for timeslice (collect data every 1 second)
        _recorder = html.MediaRecorder(stream);

        // Add listener for regular data chunks
        _recorder!.addEventListener('dataavailable', (event) {
          final data = (event as html.BlobEvent).data;
          if (data != null && data.size > 0) {
            _audioChunks.add(data);
            Logger.info(
                'Audio chunk added: ${data.size} bytes, total chunks: ${_audioChunks.length}');
          }
        });

        // Start recording with timeslice to get regular data chunks (every 1000ms)
        _recorder!.start(1000);

        setState(() {
          _isRecording = true;
          _seconds = 0;
        });

        // Start timer to update duration
        _startTimer();

        Logger.info('Recording started with timeslice');
      } else {
        throw Exception('Failed to get microphone access');
      }
    } catch (e) {
      Logger.error('Error starting recording: $e');
      // Show error in UI if context is still mounted
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting recording: $e')),
        );
      }
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isRecording) {
        setState(() {
          _seconds++;
        });
      } else {
        timer.cancel();
      }
    });
  }

  // Stop recording and return a Future that completes when data is available
  Future<void> _stopRecording() async {
    if (_recorder != null && _isRecording) {
      // Create a completer to wait for dataavailable event
      final completer = Completer<void>();

      // Add a one-time listener for dataavailable event
      void onDataAvailable(event) {
        final data = (event as html.BlobEvent).data;
        if (data != null) {
          _audioChunks.add(data);
          Logger.info('Final audio chunk added on stop: ${data.size} bytes');
        }
        completer.complete();
      }

      // Add the listener
      _recorder!.addEventListener('dataavailable', onDataAvailable);

      // Request data and stop recording
      _recorder!.requestData(); // Force a dataavailable event
      _recorder!.stop();

      // Stop all tracks in the stream
      final stream = _recorder!.stream;
      stream?.getTracks().forEach((track) => track.stop());

      setState(() {
        _isRecording = false;
      });

      if (_timer != null) {
        _timer!.cancel();
      }

      // Wait for the dataavailable event to complete
      await completer.future;

      // Remove the listener to avoid memory leaks
      _recorder!.removeEventListener('dataavailable', onDataAvailable);

      Logger.info('Recording stopped with ${_audioChunks.length} chunks');
    }
  }

  /// Call the transcription API using the new service
  Future<void> _callTranscriptionApi(
      Uint8List audioData, String fileName) async {
    try {
      Logger.info('Starting transcription API call for file: $fileName');

      // Get the multimedia service instance
      final multimediaService = MultimediaService();

      // Determine language hint based on current locale
      String languageHint = 'en'; // Default to English
      try {
        final locale = Localizations.localeOf(context);
        languageHint = locale.languageCode;
      } catch (e) {
        Logger.info('Could not determine locale, using English as default');
      }

      // Call the transcription service through multimedia service
      final transcriptionResult = await multimediaService.transcribeAudio(
        audioData: audioData,
        fileName: fileName,
        languageHint: languageHint,
        enableCommandProcessing: true,
        enableTranslation: false,
      );

      Logger.info(
          'Transcription API response: ${transcriptionResult['success']}');

      // Parse the response
      if (transcriptionResult['success'] == true) {
        final transcript = transcriptionResult['transcription'];

        // Update the chat controller with the transcribed text
        if (widget.chatController != null &&
            transcript != null &&
            transcript.isNotEmpty) {
          // Ensure we're on the main thread for UI updates
          if (mounted) {
            setState(() {
              // Set recording state to false
              _isRecording = false;
            });

            // Update the text controller
            widget.chatController!.text = transcript;
            Logger.info(
                'Updated chat controller with transcribed text: $transcript');

            // Notify parent that recording is complete
            if (widget.onCancel != null) {
              widget.onCancel!();
            }
          }
        } else {
          Logger.error('Transcription returned empty or null text');

          // Show error message to user
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Transcription returned empty text. Please try again.'),
                duration: Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        final errorMessage =
            transcriptionResult['message'] ?? 'Unknown transcription error';
        Logger.error('Transcription failed: $errorMessage');

        // Show error message to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Transcription failed: $errorMessage'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      Logger.error('Error during transcription: $e');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during transcription. Please try again.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      // Hide the loading state regardless of success or failure
      // Ensure this runs even if there's an exception
      // Notify about loading state change
      if (widget.onLoadingChanged != null) {
        widget.onLoadingChanged!(false);
      }
    }
  }

  Future<Uint8List> _convertBlobToUint8List(html.Blob blob) async {
    final completer = Completer<Uint8List>();
    final reader = html.FileReader();

    reader.onLoad.listen((event) {
      final result = reader.result as Uint8List;
      completer.complete(result);
    });

    reader.readAsArrayBuffer(blob);
    return completer.future;
  }

  String _formatDuration() {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(_seconds ~/ 60);
    final seconds = twoDigits(_seconds % 60);
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    if (_isRecording) {
      _stopRecording();
    }

    if (_audioUrl != null) {
      html.Url.revokeObjectUrl(_audioUrl!);
    }

    if (_timer != null) {
      _timer!.cancel();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Full recording UI with wave visualizer, timer, and buttons
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        // AudioWaveVisualizerWidget(
        //   isActive: _isRecording,
        //   height: 20,
        //   color: Color(0xFF0058FF),
        //   onRecordingComplete: (filePath) {
        //     Logger.info('Recording complete: $filePath');
        //   },
        // ),
        // Row with cancel button, timer, and confirm button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Cancel button
            IconButton(
              icon: Icon(Icons.close, color: Colors.black),
              onPressed: () {
                Logger.info('Cancel button pressed');

                // Just stop recording - no processing will happen
                _stopRecording();

                // Clear any audio chunks to prevent accidental processing
                _audioChunks = [];

                // Notify parent about cancellation
                if (widget.onCancel != null) {
                  widget.onCancel!();
                }
              },
            ),

            // Timer display in the center
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.mic, color: Colors.red, size: 24),
                SizedBox(width: 8),
                Text(
                  _formatDuration(),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            // Confirm button
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: Icon(Icons.check, color: Colors.white),
                onPressed: () async {
                  // Prevent multiple clicks
                  if (!_isRecording) {
                    Logger.info('Ignoring confirm click - not recording');
                    return;
                  }

                  Logger.info(
                      "Stopping recording and starting transcription...");

                  // Set loading state immediately
                  // Notify about loading state change
                  if (widget.onLoadingChanged != null) {
                    widget.onLoadingChanged!(true);
                  }

                  try {
                    // First stop the recording and wait for it to complete
                    await _stopRecording();

                    // Skip if there are no audio chunks
                    if (_audioChunks.isEmpty) {
                      Logger.info('No audio chunks to process');
                      if (widget.onLoadingChanged != null) {
                        widget.onLoadingChanged!(false);
                      }
                      return;
                    }

                    Logger.info(
                        'Processing ${_audioChunks.length} audio chunks');

                    // Create a blob from all chunks
                    final blob = html.Blob(_audioChunks, 'audio/wav');

                    // Create a URL for the blob
                    _audioUrl = html.Url.createObjectUrl(blob);

                    // Set the URL as the audio player source
                    if (_audioPlayer != null) {
                      _audioPlayer!.src = _audioUrl!;
                    }

                    // Generate a timestamp-based filename
                    final fileName =
                        'recording_${DateTime.now().millisecondsSinceEpoch}.wav';

                    // Convert blob to Uint8List
                    final audioData = await _convertBlobToUint8List(blob);

                    // Call the transcription API directly
                    await _callTranscriptionApi(audioData, fileName);
                  } catch (e) {
                    Logger.error('Error processing recording: $e');
                    // Hide loading state on error
                    if (widget.onLoadingChanged != null) {
                      widget.onLoadingChanged!(false);
                    }
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
