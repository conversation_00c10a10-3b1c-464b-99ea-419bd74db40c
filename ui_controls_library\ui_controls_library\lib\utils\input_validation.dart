/// Utility class for input validation that can be used across multiple input widgets
class InputValidation {
  /// Validates input based on field validations
  /// Returns null if valid, or an error message string if invalid
  static String? validateInput(String? value, dynamic field) {
    // Don't show required error for empty fields by default
    // We'll only validate non-empty values
    if (value == null || value.isEmpty) {
      // We won't show required errors here - they'll only show when the form is submitted
      // or when validateRequiredField() is called explicitly
      return null;
    }

    // Handle both InputField objects and Map configurations
    String? uiControl;
    String? displayName;
    String? dataType;
    List<dynamic>? validations;

    if (field is Map<String, dynamic>) {
      uiControl = field['uiControl'] as String?;
      displayName = field['displayName'] as String? ?? 'Field';
      dataType = field['dataType'] as String?;
      validations = field['validations'] as List<dynamic>?;
    } else {
      // Assume it's an InputField object with these properties
      try {
        uiControl = field.uiControl as String?;
        displayName = field.displayName as String? ?? 'Field';
        dataType = field.dataType as String?;
        validations = field.validations as List<dynamic>?;
      } catch (e) {
        // If we can't access the properties, skip validation
        return null;
      }
    }

    // Special validation for date picker
    if (uiControl == 'oj-input-date-picker') {
      return _validateDateInput(value);
    }

    // Check validations if they exist
    if (validations != null && validations.isNotEmpty) {
      for (final validation in validations) {
        if (validation == null || validation is! Map<String, dynamic>) continue;

        // Check for min length validation
        final minLengthError = _validateMinLength(value, validation, displayName);
        if (minLengthError != null) return minLengthError;

        // Check for max length validation
        final maxLengthError = _validateMaxLength(value, validation, displayName);
        if (maxLengthError != null) return maxLengthError;

        // Check for pattern validation
        final patternError = _validatePattern(value, validation, displayName);
        if (patternError != null) return patternError;

        // Check for numeric range validation
        if (dataType == 'Number' || dataType == 'Integer') {
          final numericError = _validateNumericRange(value, validation, displayName);
          if (numericError != null) return numericError;
        }
      }
    }

    return null;
  }

  /// Validates required fields explicitly
  /// This can be called when form is submitted to show required field errors
  static String? validateRequiredField(String? value, dynamic field) {
    bool required = false;
    String displayName = 'Field';

    if (field is Map<String, dynamic>) {
      required = field['required'] as bool? ?? false;
      displayName = field['displayName'] as String? ?? 'Field';
    } else {
      try {
        required = field.required as bool? ?? false;
        displayName = field.displayName as String? ?? 'Field';
      } catch (e) {
        // If we can't access the properties, skip validation
        return null;
      }
    }
    
    if (required && (value == null || value.isEmpty)) {
      return '$displayName is required';
    }
    
    // If not empty, run normal validation
    return validateInput(value, field);
  }

  /// Validates date input format
  static String? _validateDateInput(String value) {
    // Validate date format YYYY-MM-DD
    final dateRegex = RegExp(r'^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$');
    if (!dateRegex.hasMatch(value)) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }

    // Further validate the date is real (e.g., not 2023-02-31)
    try {
      final parts = value.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final day = int.parse(parts[2]);

      final date = DateTime(year, month, day);

      // Check if the date is valid by comparing components
      // This catches invalid dates like 2023-02-31
      if (date.month != month || date.day != day || date.year != year) {
        return 'Please enter a valid date';
      }
    } catch (e) {
      return 'Please enter a valid date';
    }

    return null;
  }

  /// Validates minimum length requirement
  static String? _validateMinLength(String value, Map<String, dynamic> validation, String displayName) {
    if (validation.containsKey('minLength')) {
      final minLength = validation['minLength'];
      if (minLength is int && value.length < minLength) {
        return '$displayName must be at least $minLength characters';
      }
    }
    return null;
  }

  /// Validates maximum length requirement
  static String? _validateMaxLength(String value, Map<String, dynamic> validation, String displayName) {
    if (validation.containsKey('maxLength')) {
      final maxLength = validation['maxLength'];
      if (maxLength is int && value.length > maxLength) {
        return '$displayName must be at most $maxLength characters';
      }
    }
    return null;
  }

  /// Validates pattern requirement
  static String? _validatePattern(String value, Map<String, dynamic> validation, String displayName) {
    if (validation.containsKey('pattern')) {
      final pattern = validation['pattern'];
      if (pattern is String) {
        try {
          final regex = RegExp(pattern);
          if (!regex.hasMatch(value)) {
            return validation['message'] ?? '$displayName format is invalid';
          }
        } catch (e) {
          // Invalid regex pattern - silently ignore
        }
      }
    }
    return null;
  }

  /// Validates numeric range requirements
  static String? _validateNumericRange(String value, Map<String, dynamic> validation, String displayName) {
    try {
      final numValue = num.parse(value);

      // Check minimum value
      if (validation.containsKey('minimum')) {
        final minimum = validation['minimum'];
        if (minimum is num && numValue < minimum) {
          return '$displayName must be at least $minimum';
        }
      }

      // Check maximum value
      if (validation.containsKey('maximum')) {
        final maximum = validation['maximum'];
        if (maximum is num && numValue > maximum) {
          return '$displayName must be at most $maximum';
        }
      }
    } catch (e) {
      return '$displayName must be a valid number';
    }

    return null;
  }

  /// Validates email format
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }

    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates phone number format
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }

    // Basic phone number validation (can be customized based on requirements)
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validates URL format
  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }

    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http') && !uri.scheme.startsWith('https'))) {
        return 'Please enter a valid URL starting with http:// or https://';
      }
    } catch (e) {
      return 'Please enter a valid URL';
    }

    return null;
  }
}
