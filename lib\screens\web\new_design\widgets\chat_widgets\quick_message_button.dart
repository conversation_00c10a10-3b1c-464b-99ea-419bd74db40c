import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/spacing.dart';

class QuickMessageButton extends StatefulWidget {
  final String text;
  final String imagePath;
  final VoidCallback onTap;
  final bool isSelected;
  final LayerLink? layerLink;

  const QuickMessageButton({
    super.key,
    required this.text,
    required this.imagePath,
    required this.onTap,
    required this.isSelected,
    this.layerLink,
  });

  @override
  State<QuickMessageButton> createState() => QuickMessageButtonState();
}

class QuickMessageButtonState extends State<QuickMessageButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    Widget button = MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        onTap: widget.onTap,
        child: Container(
          // No fixed width constraints - allow width to adapt to content
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              color: widget.isSelected
                  ? Color(0xff0058FF) // Blue color for selected state
                  : isHovered
                      ? Color(0xff0058FF)
                      : Color(0xffD0D0D0),
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(4),
            // boxShadow: (isHovered)
            //     ? [
            //         BoxShadow(
            //           color: Color(0xff9FB8F7)
            //               .withValues(alpha: 77), // 0.3 * 255 ≈ 77
            //           blurRadius: 3,
            //           spreadRadius: 1,
            //         )
            //       ]
            //     : [],
          ),
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 7),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Image
              SvgPicture.asset(widget.imagePath,
                  width: AppSpacing.sm,
                  height: AppSpacing.sm,
                  colorFilter: widget.isSelected || isHovered
                      ? ColorFilter.mode(
                          Color(0xff0058FF),
                          BlendMode
                              .srcIn)
                      : ColorFilter.mode(Colors.black,
                          BlendMode.srcIn)
                  ),
              SizedBox(width: 4),
              // Text
              Text(
                widget.text,
                style: TextStyle(
                  fontSize: AppSpacing.size14,
                  fontFamily: 'TiemposText',
                  color: Colors.black,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // Wrap with CompositedTransformTarget if layerLink is provided
    if (widget.layerLink != null) {
      return CompositedTransformTarget(
        link: widget.layerLink!,
        child: button,
      );
    }

    return button;
  }
}
