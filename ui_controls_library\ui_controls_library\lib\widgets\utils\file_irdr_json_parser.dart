import 'package:flutter/material.dart';

/// Utility class for parsing JSON configuration for FileIrdrWidget
class FileIrdrJsonParser {
  /// Parse colors from JSON values
  static Color? parseColor(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      return _parseNamedColor(colorValue.toLowerCase());
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  /// Parse named colors
  static Color? _parseNamedColor(String colorName) {
    switch (colorName) {
      case 'red': return Colors.red;
      case 'blue': return const Color(0xFF0058FF);
      case 'green': return Colors.green;
      case 'yellow': return Colors.yellow;
      case 'orange': return Colors.orange;
      case 'purple': return Colors.purple;
      case 'pink': return Colors.pink;
      case 'brown': return Colors.brown;
      case 'grey':
      case 'gray': return Colors.grey;
      case 'black': return Colors.black;
      case 'white': return Colors.white;
      case 'amber': return Colors.amber;
      case 'cyan': return Colors.cyan;
      case 'indigo': return Colors.indigo;
      case 'lime': return Colors.lime;
      case 'teal': return Colors.teal;
      default: return null;
    }
  }

  /// Parse text alignment from JSON
  static TextAlign parseTextAlign(dynamic alignValue) {
    if (alignValue == null) return TextAlign.start;

    if (alignValue is String) {
      switch (alignValue.toLowerCase()) {
        case 'center': return TextAlign.center;
        case 'end':
        case 'right': return TextAlign.end;
        case 'start':
        case 'left': return TextAlign.start;
        case 'justify': return TextAlign.justify;
        default: return TextAlign.start;
      }
    }

    return TextAlign.start;
  }

  /// Parse font weight from JSON
  static FontWeight parseFontWeight(dynamic weightValue) {
    if (weightValue == null) return FontWeight.normal;

    if (weightValue is String) {
      switch (weightValue.toLowerCase()) {
        case 'bold': return FontWeight.bold;
        case 'normal': return FontWeight.normal;
        case 'light': return FontWeight.w300;
        default: return FontWeight.normal;
      }
    } else if (weightValue is int) {
      switch (weightValue) {
        case 100: return FontWeight.w100;
        case 200: return FontWeight.w200;
        case 300: return FontWeight.w300;
        case 400: return FontWeight.w400;
        case 500: return FontWeight.w500;
        case 600: return FontWeight.w600;
        case 700: return FontWeight.w700;
        case 800: return FontWeight.w800;
        case 900: return FontWeight.w900;
        default: return FontWeight.normal;
      }
    }

    return FontWeight.normal;
  }

  /// Parse icon data from JSON
  static IconData? parseIconData(dynamic iconValue) {
    if (iconValue == null) return null;

    if (iconValue is String) {
      return _parseNamedIcon(iconValue.toLowerCase());
    }

    return null;
  }

  /// Parse named icons
  static IconData? _parseNamedIcon(String iconName) {
    switch (iconName) {
      case 'document_scanner': return Icons.document_scanner;
      case 'upload_file': return Icons.upload_file;
      case 'file_upload': return Icons.file_upload;
      case 'attach_file': return Icons.attach_file;
      case 'cloud_upload': return Icons.cloud_upload;
      case 'add': return Icons.add;
      case 'add_circle': return Icons.add_circle;
      case 'add_circle_outline': return Icons.add_circle_outline;
      case 'folder': return Icons.folder;
      case 'folder_open': return Icons.folder_open;
      case 'description': return Icons.description;
      case 'insert_drive_file': return Icons.insert_drive_file;
      case 'picture_as_pdf': return Icons.picture_as_pdf;
      case 'image': return Icons.image;
      case 'photo': return Icons.photo;
      case 'video_file': return Icons.video_file;
      case 'audio_file': return Icons.audio_file;
      case 'text_snippet': return Icons.text_snippet;
      case 'table_chart': return Icons.table_chart;
      case 'slideshow': return Icons.slideshow;
      case 'folder_zip': return Icons.folder_zip;
      case 'camera': return Icons.camera_alt;
      case 'scanner': return Icons.scanner;
      case 'qr_code': return Icons.qr_code;
      case 'qr_code_scanner': return Icons.qr_code_scanner;
      case 'face': return Icons.face;
      case 'receipt': return Icons.receipt;
      case 'credit_card': return Icons.credit_card;
      case 'badge': return Icons.badge;
      default: return null;
    }
  }

  /// Parse edge insets from JSON
  static EdgeInsetsGeometry parseEdgeInsets(dynamic paddingValue) {
    if (paddingValue == null) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    if (paddingValue is Map) {
      final left = (paddingValue['left'] as num?)?.toDouble() ?? 12.0;
      final top = (paddingValue['top'] as num?)?.toDouble() ?? 8.0;
      final right = (paddingValue['right'] as num?)?.toDouble() ?? 12.0;
      final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 8.0;

      return EdgeInsets.fromLTRB(left, top, right, bottom);
    }

    if (paddingValue is num) {
      final value = paddingValue.toDouble();
      return EdgeInsets.all(value);
    }

    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
  }

  /// Parse list of strings from JSON
  static List<String>? parseStringList(dynamic listValue) {
    if (listValue == null) return null;

    if (listValue is List) {
      return listValue.map((e) => e.toString()).toList();
    }

    if (listValue is String) {
      return [listValue];
    }

    return null;
  }

  /// Parse list of colors from JSON
  static List<Color>? parseColorList(dynamic listValue) {
    if (listValue == null) return null;

    if (listValue is List) {
      return listValue
          .map((e) => parseColor(e))
          .where((color) => color != null)
          .cast<Color>()
          .toList();
    }

    return null;
  }

  /// Parse map from JSON
  static Map<String, String>? parseStringMap(dynamic mapValue) {
    if (mapValue == null) return null;

    if (mapValue is Map) {
      return Map<String, String>.from(
        mapValue.map((key, value) => MapEntry(key.toString(), value.toString())),
      );
    }

    return null;
  }

  /// Parse boolean with default value
  static bool parseBool(dynamic value, {bool defaultValue = false}) {
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return defaultValue;
  }

  /// Parse double with default value
  static double parseDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  /// Parse int with default value
  static int parseInt(dynamic value, {int defaultValue = 0}) {
    if (value is num) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }
}
