// import 'package:flutter/material.dart';

// class Process<PERSON>reeViewer extends StatelessWidget {
//   final List<Map<String, dynamic>> processFlow;

//   const ProcessTreeViewer({super.key, required this.processFlow});

//   @override
//   Widget build(BuildContext context) {
//     final nodeMap = {for (var node in processFlow) node['id']: node};
//     final builtNodes = <String>{};
//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       child: _buildNodeTree('GO1.PW1', nodeMap.cast<String, dynamic>(), builtNodes),
//     );
//   }

//   Widget _buildNodeTree(
//     String nodeId,
//     Map<String, dynamic> nodeMap,
//     Set<String> builtNodes,
//   ) {
//     if (!nodeMap.containsKey(nodeId)) {
//       if (nodeId == 'Terminal') {
//         return _buildTerminalNode();
//       }
//       return const SizedBox.shrink();
//     }

//     final node = nodeMap[nodeId]!;
//     builtNodes.add(nodeId);
//     final children = <Widget>[];

//     // Get child nodes based on route type
//     if (node['route_type'] == 'Alternate') {
//       for (final condition in node['conditions'] ?? []) {
//         final childId = condition['route_to'];
//         children.add(_buildChildNode(childId, nodeMap, builtNodes));
//       }
//     } else if (node['route_type'] == 'Sequential') {
//       for (final route in node['routes'] ?? []) {
//         children.add(_buildChildNode(route, nodeMap, builtNodes));
//       }
//     } else if (node['route_type'] == 'Parallel') {
//       for (final parallelRoute in node['parallel_routes'] ?? []) {
//         children.add(_buildChildNode(parallelRoute['route_to'], nodeMap, builtNodes));
//       }
//     }

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         _buildNodeCard(node['lo_name']),
//         if (children.isNotEmpty)
//           Padding(
//             padding: const EdgeInsets.only(top: 16.0),
//             child: Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: children,
//             ),
//           ),
//       ],
//     );
//   }

//   Widget _buildChildNode(
//     String childId,
//     Map<String, dynamic> nodeMap,
//     Set<String> builtNodes,
//   ) {
//     return Column(
//       children: [
//         // Connector line
//         Container(height: 16, width: 1, color: Colors.grey),
//         builtNodes.contains(childId)
//             ? _buildLinkedNode(childId, nodeMap)
//             : _buildNodeTree(childId, nodeMap, builtNodes),
//       ],
//     );
//   }

//   Widget _buildNodeCard(String title) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       decoration: BoxDecoration(
//         color: Colors.blue[50],
//         borderRadius: BorderRadius.circular(8),
//         border: Border.all(color: Colors.blue),
//       ),
//       child: Text(
//         title,
//         style: const TextStyle(fontWeight: FontWeight.bold),
//       ),
//     );
//   }

//   Widget _buildLinkedNode(String childId, Map<String, dynamic> nodeMap) {
//     return Column(
//       children: [
//         const Icon(Icons.link, size: 20, color: Colors.green),
//         const SizedBox(height: 4),
//         Container(
//           padding: const EdgeInsets.all(8),
//           decoration: BoxDecoration(
//             border: Border.all(color: Colors.grey),
//             borderRadius: BorderRadius.circular(4),
//           ),
//           child: Text(
//             nodeMap[childId]?['lo_name'] ?? childId,
//             style: TextStyle(
//               color: Colors.grey[700],
//               fontStyle: FontStyle.italic,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildTerminalNode() {
//     return Column(
//       children: [
//         Container(height: 16, width: 1, color: Colors.grey),
//         const Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(Icons.stop_circle, size: 20, color: Colors.red),
//             SizedBox(width: 4),
//             Text('Terminal', style: TextStyle(fontWeight: FontWeight.bold)),
//           ],
//         ),
//       ],
//     );
//   }
// }
