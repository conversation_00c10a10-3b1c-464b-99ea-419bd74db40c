import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/models/chat_message.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/multimedia_widgets.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/ocr_text_side_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/file_upload_response_preview.dart';
import 'package:nsl/services/multimedia_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/resizable_panel.dart';
import 'package:nsl/services/file_upload_service.dart';

class TempWebChat extends StatefulWidget {
  const TempWebChat({super.key});

  @override
  State<TempWebChat> createState() => TempWebChatState();
}

class TempWebChatState extends State<TempWebChat> {
  final TextEditingController chatController = TextEditingController();
  bool isLoading = false; // General chat API calls loading state
  bool isFileUploading = false; // File upload/OCR processing loading state
  bool isSpeechToTextLoading =
      false; // Speech-to-text/audio recording loading state
  bool isAudioLoading =
      false; // Loading state for audio recording/transcription (deprecated - use isSpeechToTextLoading)
  bool hasTextInChatField = false; // Track if there's text in the chat field
  bool isTemp = true;

  // Helper method to access the provider's messages list
  List<ChatMessage> get messages =>
      Provider.of<WebHomeProvider>(context, listen: false).messages;

  // OCR side panel state
  bool showOcrPanel = false;
  String ocrText = '';
  String ocrFileName = '';

  // File upload state for the new UI
  bool isFileUploaded = false;
  bool isFileProcessing = false;
  String uploadedFileName = '';
  String uploadedFileText = '';
  FileUploadOcrResponse? fileUploadOcrResponse;

  // Pending file for temp mode (file selected but not processed yet)
  dynamic pendingFile;

  // Multimedia service
  final MultimediaService _multimediaService = MultimediaService();

  // ScrollController for chat messages
  final ScrollController _chatScrollController = ScrollController();

  // Audio player
  final AudioPlayer audioPlayer = AudioPlayer();

  // Audio playback state
  String? currentPlayingMessageId;
  bool isPlaying = false;
  bool isPaused = false;
  Duration currentPosition = Duration.zero;
  Duration totalDuration = Duration.zero;
  String? currentAudioFilePath;

  // Recording state
  bool isRecording = false;

  // Speech recognition state
  String _recognizedText = "";

  // Side panel resizing
  double sidePanelWidth = 480.0; // Default width
  double minSidePanelWidth = 250.0; // Minimum width
  double maxSidePanelWidth = 600.0; // Maximum width
  bool isResizing = false;

  @override
  void initState() {
    super.initState();

    // Add listener to chat controller to detect text changes
    chatController.addListener(() {
      final hasText = chatController.text.trim().isNotEmpty;
      if (hasText != hasTextInChatField) {
        setState(() {
          hasTextInChatField = hasText;
        });
      }
    });

    // Initialize multimedia service
    _multimediaService.initialize();

    // Set up multimedia service callbacks
    _multimediaService.onStateChanged = () {
      if (mounted) {
        setState(() {
          // Update UI state based on multimedia service state
          isPlaying = _multimediaService.isPlaying;
          isPaused = _multimediaService.isPaused;
          isRecording = _multimediaService.isRecording;
          currentPlayingMessageId = _multimediaService.currentPlayingMessageId;
          currentPosition = _multimediaService.currentPosition;
          totalDuration = _multimediaService.totalDuration;

          // Update speech-to-text loading state based on recording state
          isSpeechToTextLoading = _multimediaService.isRecording;
        });
      }
    };

    // Set up additional multimedia service listeners for text recognition
    _setupMultimediaServiceListeners();

    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      if (mounted) {
        // Add file upload and OCR messages to chat
        setState(() {
          ocrFileName = fileName;
          ocrText = extractedText;
          showOcrPanel = true;
          chatController.text = extractedText;
          fileUploadOcrResponse = ocrResponseModel;
        });

        // Scroll to bottom after the UI updates
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    };
  }

  @override
  void dispose() {
    // Clean up the controllers when the widget is disposed
    chatController.dispose();
    _chatScrollController.dispose();

    // Dispose audio player
    audioPlayer.dispose();

    // Dispose recording service
    _multimediaService.dispose();

    super.dispose();
  }

  // Set up multimedia service listeners for text recognition
  void _setupMultimediaServiceListeners() {
    if (!mounted) return;

    try {
      // Set up a listener for recognized text
      _multimediaService.onTextRecognized = (text) {
        if (mounted) {
          setState(() {
            // Update recognized text in the chat field
            _recognizedText = text;
            chatController.text = text;

            // Log the recognized text
            Logger.info('Text recognized from audio: $text');
          });
        }
      };
    } catch (e) {
      Logger.error('Error setting up multimedia service listeners: $e');
    }
  }

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    if (_chatScrollController.hasClients) {
      _chatScrollController.animateTo(
        _chatScrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  // Add file upload and OCR messages to chat
  void addFileUploadAndOcrMessages(String fileName, String extractedText,
      FileUploadOcrResponse ocrResponseModel) {
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // 1. Add user message for file upload
    provider.addMessage(ChatMessage(
      content: 'Uploaded file: $fileName',
      isUser: true,
    ));

    // 2. Create system response with file preview and text sections
    String systemContent = '';

    // Add original text section if available
    if (ocrResponseModel.data?.originalText != null &&
        ocrResponseModel.data!.originalText!.isNotEmpty) {
      systemContent +=
          'Original Text:\n${ocrResponseModel.data!.originalText!}';
    }

    // Add corrected text section if available and different from original
    if (ocrResponseModel.data?.correctedText != null &&
        ocrResponseModel.data!.correctedText!.isNotEmpty &&
        ocrResponseModel.data!.correctedText !=
            ocrResponseModel.data?.originalText) {
      if (systemContent.isNotEmpty) systemContent += '\n\n';
      systemContent +=
          'Corrected Text:\n${ocrResponseModel.data!.correctedText!}';
    }

    // Create system message with file data
    ChatMessage systemMessage = ChatMessage(
      content: systemContent,
      isUser: false,
      fileData: ocrResponseModel,
    );

    provider.addMessage(systemMessage);

    setState(() {
      ocrFileName = fileName;
      ocrText = extractedText;
      showOcrPanel = true;
      chatController.text = extractedText;

      // Reset file upload state
      isFileUploaded = false;
      isFileProcessing = false;
      uploadedFileName = '';
      uploadedFileText = '';
      fileUploadOcrResponse = null;
    });

    // Store the extracted text in the provider for the next API call
    provider.lastUserMessageForApi = extractedText;

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Handle file selection from the ChatField component
  void _handleFileSelection(String fileName, String filePath) {
    // Show file upload loading indicator
    setState(() {
      isFileUploading = true;
    });

    // Use the multimedia service to process the file
    _multimediaService.onFileProcessed =
        (fileName, extractedText, ocrResponseModel) {
      // Add the file upload and OCR messages to chat
      addFileUploadAndOcrMessages(fileName, extractedText, ocrResponseModel);

      setState(() {
        isFileUploading = false;
      });
    };

    // Process the file
    _multimediaService.processFile(context);
  }

  // Toggle OCR panel
  void toggleOcrPanel() {
    setState(() {
      showOcrPanel = !showOcrPanel;
    });
  }

  // Close OCR panel
  void closeOcrPanel() {
    setState(() {
      showOcrPanel = false;
    });
  }

  // Convert text to speech and play audio
  Future<void> _convertTextToSpeech(String text, {String? messageId}) async {
    if (text.isEmpty) return;

    // Show loading indicator
    setState(() {
      isLoading = true;
    });

    try {
      // Call the multimedia service to convert text to speech
      await _multimediaService.convertTextToSpeech(
        text,
        messageId: messageId,
        context: context,
      );
    } finally {
      // Hide loading indicator
      setState(() {
        isLoading = false;
      });
    }
  }

  // Toggle recording (start/stop)
  Future<void> _toggleRecording() async {
    await _multimediaService.toggleRecording(context);
  }

  // Handle sending a message
  void _sendMessage() async {
    // Guard against duplicate execution - check if any loading state is active
    if (isLoading || isFileUploading || isSpeechToTextLoading) {
      Logger.info(
          '_sendMessage called but loading state is active, ignoring duplicate call');
      return;
    }

    String text = chatController.text.trim();
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Set loading state immediately to prevent duplicate calls
    setState(() {
      isLoading = true;
    });

    // Check if we have a pending file to process (temp mode)
    if (pendingFile != null) {
      await _processPendingFileWithMessage(text);
      return;
    }

    // Original logic for when there's already processed file data
    if (text.isEmpty) {
      if (fileUploadOcrResponse != null &&
          fileUploadOcrResponse?.data?.originalText != null &&
          fileUploadOcrResponse?.data?.correctedText != null) {
        text = "";
        final customText = RichText(
          text: TextSpan(
            style: const TextStyle(
              height: 1.5,
              fontFamily: 'TiemposText',
              fontSize: 17,
              fontWeight: FontWeight.w400,
              color: Colors.black,
            ),
            children: [
              TextSpan(
                text: 'Original Text:\n',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              TextSpan(
                text: fileUploadOcrResponse!.data!.originalText!,
              ),
              TextSpan(
                text: '\nCorrected Text:\n',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              TextSpan(
                text: fileUploadOcrResponse!.data!.correctedText!,
              ),
            ],
          ),
        );
        provider.addMessage(ChatMessage(
          content: text,
          isUser: false,
          fileData: fileUploadOcrResponse,
          customText: customText,
        ));
      } else {
        return;
      }
    } else {
      provider.addMessage(ChatMessage(
        content: text,
        isUser: false,
      ));
    }

    // Clear the chat input field
    chatController.clear();

    // Reset file upload state and clear loading
    setState(() {
      isLoading = false; // Clear loading state
      isFileUploaded = false;
      isFileProcessing = false;
      uploadedFileName = '';
      uploadedFileText = '';
      fileUploadOcrResponse = null;
      showOcrPanel = false;
      pendingFile = null; // Clear pending file
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  // Process pending file with user message (temp mode)
  Future<void> _processPendingFileWithMessage(String userMessage) async {
    if (pendingFile == null) return;

    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // Show file upload loading state
    setState(() {
      isFileUploading = true;
      isFileProcessing = true;
    });

    try {
      // Create the file upload service
      final fileUploadService = FileUploadService();

      // Add user message first (combining file upload + user text)
      String userContent = userMessage.isNotEmpty
          ? userMessage
          : 'Uploaded file: ${pendingFile.name}';

      provider.addMessage(ChatMessage(
        content: userContent,
        isUser: true,
      ));

      // Process the file for OCR
      final uploadResult = await fileUploadService.uploadFile(pendingFile);

      if (!uploadResult['success']) {
        // Handle upload error
        provider.addMessage(ChatMessage(
          content: 'Error processing file: ${uploadResult['message']}',
          isUser: false,
        ));
        return;
      }

      final ocrResponse = FileUploadOcrResponse.fromJson(uploadResult['data']);

      // Create system content with original and corrected text
      String systemContent = '';

      // if (ocrResponse?.data?.originalText != null &&
      //     ocrResponse.data!.originalText!.isNotEmpty) {
      //   systemContent += 'Original Text:\n${ocrResponse.data!.originalText!}';
      // }

      if (ocrResponse.data?.correctedText != null &&
          ocrResponse.data!.correctedText!.isNotEmpty) {
        // if (systemContent.isNotEmpty) systemContent += '\n\n';
        systemContent += ocrResponse.data!.correctedText!;
      }

      // Create system message with file data
      provider.addMessage(ChatMessage(
        content: systemContent,
        isUser: false,
        fileData: ocrResponse,
      ));

      // Update OCR panel state
      setState(() {
        ocrFileName = pendingFile.name;
        ocrText = ocrResponse.data?.originalText ?? '';
        showOcrPanel = true;
      });
    } catch (e) {
      // Handle error
      provider.addMessage(ChatMessage(
        content: 'Error processing file: $e',
        isUser: false,
      ));
      Logger.error('Error processing pending file: $e');
    } finally {
      // Clear the chat input field and reset state
      chatController.clear();

      setState(() {
        isFileUploading = false;
        isFileUploaded = false;
        isFileProcessing = false;
        isLoading = false;
        uploadedFileName = '';
        uploadedFileText = '';
        fileUploadOcrResponse = null;
        pendingFile = null; // Clear pending file
      });
      // Scroll to bottom after the UI updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }

  // Cancel the current request
  void _cancelRequest() async {
    // Get the provider before any async operations
    final provider = Provider.of<WebHomeProvider>(context, listen: false);

    // If recording, stop it
    if (isRecording) {
      await _multimediaService.stopSpeechRecognition();
      Logger.info('Speech recognition stopped by cancel request');
    }

    // Check if widget is still mounted after async operations
    if (!mounted) return;

    // Update state using the provider and local state
    provider.isLoading = false;

    // Add a message to indicate the request was cancelled
    provider.addMessage(ChatMessage(
      content: 'Request cancelled by user.',
      isUser: false,
    ));

    // Update local recording and loading states
    setState(() {
      isLoading = false;
      isFileUploading = false;
      isSpeechToTextLoading = false;
      isRecording = false;
    });

    // Scroll to bottom after the UI updates
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _scrollToBottom();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body:
          //  NSLKnowledgeLoaderWrapper(
          //   isLoading: isLoading,
          //   child:
          Row(
        children: [
          // Main chat area
          Expanded(
            child: Column(
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Multimedia Chat Demo',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                      Spacer(),
                      if (showOcrPanel)
                        OcrPanelToggleButton(
                          isShown: showOcrPanel,
                          onToggle: toggleOcrPanel,
                        ),
                    ],
                  ),
                ),

                // Chat messages area
                Expanded(
                  child: Container(
                    color: Colors.white,
                    child: Consumer<WebHomeProvider>(
                      builder: (context, provider, child) {
                        final messages = provider.messages;

                        if (messages.isEmpty) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.chat_bubble_outline,
                                  size: 64,
                                  color: Colors.grey.shade400,
                                ),
                                SizedBox(height: AppSpacing.md),
                                Text(
                                  'Start a conversation with multimedia features',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                    fontFamily: 'TiemposText',
                                  ),
                                ),
                                SizedBox(height: AppSpacing.sm),
                                Text(
                                  'Upload files, record audio, or type a message',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        return ListView.builder(
                          controller: _chatScrollController,
                          padding: EdgeInsets.all(AppSpacing.md),
                          itemCount: messages.length,
                          itemBuilder: (context, index) {
                            final message = messages[index];
                            return _buildMessageBubble(message, index);
                          },
                        );
                      },
                    ),
                  ),
                ),

                // Chat input field
                Container(
                  padding: EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: ChatField(
                    isTemp: isTemp,
                    isGeneralLoading: isLoading,
                    isFileLoading: isFileUploading,
                    isSpeechLoading: isSpeechToTextLoading,
                    parentState: this,
                    onSendMessage: _sendMessage,
                    onCancelRequest: _cancelRequest,
                    onFileSelected: _handleFileSelection,
                    onToggleRecording: _toggleRecording,
                    controller: chatController,
                    multimediaService: _multimediaService,
                    initialUploadedFileName: uploadedFileName,
                    initialUploadedFileText: uploadedFileText,
                    initialIsFileUploaded: isFileUploaded,
                    initialIsFileProcessing: isFileProcessing,
                    onFileUploadTap: toggleOcrPanel,
                    onFileCloseTap: closeOcrPanel,
                  ),
                ),
              ],
            ),
          ),

          // OCR side panel
          if (showOcrPanel)
            ResizablePanel(
              width: sidePanelWidth,
              minWidth: minSidePanelWidth,
              maxWidth: maxSidePanelWidth,
              onResize: (newWidth) {
                setState(() {
                  sidePanelWidth = newWidth;
                });
              },
              child: OcrTextSidePanel(
                fileName: ocrFileName,
                ocrText: ocrText,
              ),
            ),
        ],
      ),
      // ),
    );
  }

  // Build message bubble widget matching the original styling
  Widget _buildMessageBubble(ChatMessage message, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.isUser)
          // User message bubble - matching original styling
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              margin: EdgeInsets.only(bottom: AppSpacing.xs),
              padding: EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: Color(0xffF5F5F5),
                borderRadius: BorderRadius.circular(AppSpacing.xs),
                border: Border.all(color: Color(0xffD0D0D0), width: 1),
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  height: MediaQuery.of(context).size.width > 1600 ? 1.5 : 2,
                  fontFamily: 'TiemposText',
                  fontSize: MediaQuery.of(context).size.width > 1600 ? 17 : 15,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
            ),
          )
        else
          // AI/System message bubble - matching original styling
          Container(
            padding: EdgeInsets.only(
              top: AppSpacing.xs,
              bottom: AppSpacing.xs,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: _MessageContentWithHover(
                    message: message,
                    index: index,
                    audioPlayer: _multimediaService.audioPlayer,
                    currentPlayingMessageId:
                        _multimediaService.currentPlayingMessageId,
                    isPlaying: _multimediaService.isPlaying,
                    isPaused: _multimediaService.isPaused,
                    currentPosition: _multimediaService.currentPosition,
                    totalDuration: _multimediaService.totalDuration,
                    onTextToSpeech: _convertTextToSpeech,
                    onStopAudio: () => _multimediaService.stopAudio(),
                    showCopyOverlay: _showCopyOverlay,
                    isLastItem: index == messages.length - 1,
                    parentState: this,
                    customText: message.customText,
                  ),
                ),
              ],
            ),
          ),
        SizedBox(height: AppSpacing.xxs),
      ],
    );
  }

  // Show copy overlay
  void _showCopyOverlay(BuildContext context) {
    // Create overlay entry
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height / 2 - 50,
        left: MediaQuery.of(context).size.width / 2 - 75,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Copied to clipboard',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );

    // Add the overlay to the overlay
    Overlay.of(context).insert(overlayEntry);

    // Remove the overlay after 2 seconds
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}

// Widget that wraps the entire message content with hover functionality
class _MessageContentWithHover extends StatefulWidget {
  final ChatMessage message;
  final int index;
  final AudioPlayer audioPlayer;
  final String? currentPlayingMessageId;
  final bool isPlaying;
  final bool isPaused;
  final Duration currentPosition;
  final Duration totalDuration;
  final Function(String, {String? messageId}) onTextToSpeech;
  final VoidCallback onStopAudio;
  final Function(BuildContext) showCopyOverlay;
  final bool? isLastItem;
  final TempWebChatState parentState;
  final Widget? customText;

  const _MessageContentWithHover({
    required this.message,
    required this.index,
    required this.audioPlayer,
    required this.currentPlayingMessageId,
    required this.isPlaying,
    required this.isPaused,
    required this.currentPosition,
    required this.totalDuration,
    required this.onTextToSpeech,
    required this.onStopAudio,
    required this.showCopyOverlay,
    required this.isLastItem,
    required this.parentState,
    this.customText,
  });

  @override
  State<_MessageContentWithHover> createState() =>
      _MessageContentWithHoverState();
}

class _MessageContentWithHoverState extends State<_MessageContentWithHover> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Always show buttons if audio is playing for this message
    final String messageId = 'msg_${widget.index}';
    final bool alwaysShow =
        widget.currentPlayingMessageId == messageId && widget.isPlaying ||
            (widget.isLastItem ?? false);

    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Display file preview if this is a response to a file upload
          if (!widget.message.isUser && widget.message.fileData != null)
            FileUploadResponsePreview(
              fileName:
                  widget.message.fileData!.data?.fileName ?? 'Uploaded File',
              onTap: () {
                widget.parentState.ocrText =
                    widget.message.fileData!.data?.originalText ?? '';
                widget.parentState.ocrFileName =
                    widget.message.fileData!.data?.fileName ?? '';
                widget.parentState.fileUploadOcrResponse =
                    widget.message.fileData;
                widget.parentState.toggleOcrPanel();
              },
            ),

          // Display text content if available
          if (widget.message.content.isNotEmpty && widget.customText == null)
            Text(
              widget.message.content,
              style: TextStyle(
                height: MediaQuery.of(context).size.width > 1600 ? 1.5 : 2,
                fontFamily: 'TiemposText',
                fontSize: MediaQuery.of(context).size.width > 1600 ? 17 : 15,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),

          if (widget.customText != null) widget.customText!,
          SizedBox(height: 8),
        ],
      ),
    );
  }
}
