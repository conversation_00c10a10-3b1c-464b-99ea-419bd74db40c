import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:rich_readmore/rich_readmore.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/workflow/workflow_lo_response_model.dart';
import '../../../../../theme/spacing.dart';

/// A reusable workflow LO details panel widget that displays detailed information about workflow LO data.
///
/// This widget shows workflow LO information including all sections from parsed_data in a structured format
/// with navigation shortcuts and interactive elements.
class WorkflowLoDetailsPanel extends StatefulWidget {
  /// The workflow LO response data to display
  final WorkFlowLoResponseModel workflowLoData;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  const WorkflowLoDetailsPanel({
    super.key,
    required this.workflowLoData,
    this.onClose,
    this.chatController,
    this.onSendMessage,
  });

  @override
  State<WorkflowLoDetailsPanel> createState() => _WorkflowLoDetailsPanelState();
}

class _WorkflowLoDetailsPanelState extends State<WorkflowLoDetailsPanel> {
  // Track expanded state for each section
  Set<String> expandedSectionIds = <String>{};

  // Track active section for navigation highlighting
  String? activeSectionId;

  // ScrollController for the content area
  late ScrollController _scrollController;

  // Variables for fixing scrolling issue (similar to role_details_panel)
  double _bottomSpacerHeight = 500;

  // Global keys for sections - moved to class level to persist across builds
  late Map<String, GlobalKey> sectionKeys;

  // VisibilityInfo objects for each section
  Map<String, VisibilityInfo> sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();

    // Initialize section keys
    sectionKeys = {
      'localObjectives': GlobalKey(),
      'loInputStack': GlobalKey(),
      'loInputItems': GlobalKey(),
      'loOutputStack': GlobalKey(),
      'loOutputItems': GlobalKey(),
      'loEntityValidations': GlobalKey(),
      'loUiStack': GlobalKey(),
      'loUiEntityAttributeStack': GlobalKey(),
      'loDataMappingStack': GlobalKey(),
      'loDataMappings': GlobalKey(),
      'loNestedFunctions': GlobalKey(),
      'executionPathways': GlobalKey(),
      'executionPathwayConditions': GlobalKey(),
      'loNestedFunctionInputStacks': GlobalKey(),
      'loNestedFunctionOutputItems': GlobalKey(),
      'loNestedFunctionOutputStacks': GlobalKey(),
    };

    // Initialize the first section as active
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        activeSectionId = 'localObjectives';
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Define the sections for navigation with their abbreviations
    final Map<String, String> sectionMap = {
      'agentStack': 'AS',
      'inputStack': 'IS',
      'outputStack': 'OS',
      'databaseStack': 'DS',
      'uiStack': 'US',
      'aiStack': 'AI',
      // 'localObjectives': 'LO',
      // 'loInputStack': 'IS',
      // 'loInputItems': 'II',
      // 'loOutputStack': 'OS',
      // 'loOutputItems': 'OI',
      // 'loEntityValidations': 'EV',
      // 'loUiStack': 'US',
      // 'loUiEntityAttributeStack': 'UA',
      // 'loDataMappingStack': 'DM',
      // 'loDataMappings': 'DG',
      // 'loNestedFunctions': 'NF',
      // 'executionPathways': 'EP',
      // 'executionPathwayConditions': 'EC',
      // 'loNestedFunctionInputStacks': 'NI',
      // 'loNestedFunctionOutputItems': 'NO',
      // 'loNestedFunctionOutputStacks': 'NS',
    };

    // Enhanced scroll function that fixes the bottom sections scrolling issue
    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final key = sectionKeys[sectionId];

        if (key?.currentContext != null) {
          // Update active section immediately
          setState(() {
            activeSectionId = sectionId;
          });

          // Update bottom spacer height for better scrolling
          setState(() {
            final sectionKeysList = sectionMap.keys.toList();
            final sectionIndex = sectionKeysList.indexOf(sectionId);

            // Calculate required bottom padding based on section position
            if (sectionIndex >= (sectionKeysList.length - 6)) {
              // Last 6 sections need more space
              _bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              _bottomSpacerHeight = 500;
            }
          });

          // Scroll to the section
          Future.delayed(Duration(milliseconds: 50), () {
            if (_scrollController.hasClients) {
              Scrollable.ensureVisible(
                key!.currentContext!,
                alignment: 0.0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xff9B9B9B).withValues(alpha: 0.14),
            blurRadius: 20,
            offset: Offset(-3, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30),
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        radius: 10,
                        child: Icon(
                          Icons.account_tree_outlined,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xs),
                      Expanded(
                        child: Text(
                          widget.workflowLoData.parsedLos?.name ??
                              'Workflow LO Details',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText",
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                const SizedBox(
                  width: AppSpacing.xxs,
                ),
                IconButton(
                  icon: SvgPicture.asset(
                    'assets/images/chat/toggle_open_close.svg',
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade700,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: widget.onClose,
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          // Content area with navigation labels and sections
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: AppSpacing.xxs),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left column for navigation labels
                  Container(
                    width: 30,
                    padding: EdgeInsets.only(top: 8),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          for (final entry in sectionMap.entries)
                            Padding(
                              padding: EdgeInsets.only(left: 10, top: 8),
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  onTap: () => scrollToSection(entry.key),
                                  child: Text(
                                    entry.value,
                                    style: TextStyle(
                                      fontFamily: "TiemposText",
                                      fontWeight: activeSectionId == entry.key
                                          ? FontWeight.bold
                                          : FontWeight.w400,
                                      fontSize: 10,
                                      color: activeSectionId == entry.key
                                          ? Colors.blue.shade700
                                          : Colors.black,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Right column for content
                  Expanded(
                    child: Scrollbar(
                      controller: _scrollController,
                      thumbVisibility: true,
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildAgentStackSection(context, sectionKeys),
                            _buildInputStackSection(context, sectionKeys),
                            _buildOutputStackSection(context, sectionKeys),
                            _buildDatabaseStackSection(context, sectionKeys),
                            _buildUIStackSection(context, sectionKeys),
                            _buildAIStackSection(context, sectionKeys),
                            // _buildLocalObjectivesSection(context, sectionKeys),
                            // _buildLoInputStackSection(context, sectionKeys),
                            // _buildLoInputItemsSection(context, sectionKeys),
                            // _buildLoOutputStackSection(context, sectionKeys),
                            // _buildLoOutputItemsSection(context, sectionKeys),
                            // _buildLoEntityValidationsSection(
                            //     context, sectionKeys),
                            // _buildLoUiStackSection(context, sectionKeys),
                            // _buildLoUiEntityAttributeStackSection(
                            //     context, sectionKeys),
                            // _buildLoDataMappingStackSection(
                            //     context, sectionKeys),
                            // _buildLoDataMappingsSection(context, sectionKeys),
                            // _buildLoNestedFunctionsSection(
                            //     context, sectionKeys),
                            // _buildExecutionPathwaysSection(
                            //     context, sectionKeys),
                            // _buildExecutionPathwayConditionsSection(
                            //     context, sectionKeys),
                            // _buildLoNestedFunctionInputStacksSection(
                            //     context, sectionKeys),
                            // _buildLoNestedFunctionOutputItemsSection(
                            //     context, sectionKeys),
                            // _buildLoNestedFunctionOutputStacksSection(
                            //     context, sectionKeys),

                            // Smart bottom padding to fix scrolling for bottom sections
                            SizedBox(height: _bottomSpacerHeight),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Method to update active section based on visibility
  void _updateActiveSection() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != activeSectionId) {
      if (mounted) {
        setState(() {
          activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  /// Builds a generic section widget with VisibilityDetector
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        sectionVisibilityInfo[sectionId] = info;
        _updateActiveSection();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$title:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 8),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
          ],
        ),
      ),
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Text(
      'No $sectionName data available.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the agentstack section
  Widget _buildAgentStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'agentStack',
      'Agent Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildAgentStackContent(context),
    );
  }

  /// Builds the agentstack content
  Widget _buildAgentStackContent(BuildContext context) {
    final localObjectives = widget.workflowLoData.parsedData!.localObjectives!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [],
    );
  }

  /// Builds the inputstack section
  Widget _buildInputStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'inputStack',
      'Input Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildInputStackContent(context),
    );
  }

  /// Builds the inputstack content
  Widget _buildInputStackContent(BuildContext context) {
    final parsedData = widget.workflowLoData.parsedData!;

    return Card(
      margin: const EdgeInsets.only(top: 16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4.0),
        side: BorderSide(color: Colors.grey.shade300, width: 1),
      ),
      elevation: 0,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          title: RichReadMoreText.fromString(
            text: parsedData.loInputStack?.naturalLanguage ?? '',
            textStyle: TextStyle(
                fontSize: 16,
                fontFamily: "TiemposText",
                fontWeight: FontWeight.bold),
            settings: LengthModeSettings(
              trimLength: 200,
              trimCollapsedText: 'Show more',
              trimExpandedText: 'Show less',
              onPressReadMore: () {},
              onPressReadLess: () {},
              lessStyle: TextStyle(
                  color: AppColors.primaryBlue,
                  fontSize: 10,
                  fontFamily: "TiemposText"),
              moreStyle: TextStyle(
                  color: AppColors.primaryBlue,
                  fontSize: 10,
                  fontFamily: "TiemposText"),
            ),
          ),
          initiallyExpanded: false,
          tilePadding:
              const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          childrenPadding:
              const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
          trailing: const Icon(Icons.keyboard_arrow_down),
          expandedCrossAxisAlignment: CrossAxisAlignment.start,
          children: (parsedData.loNestedFunctions ?? []).map(
            (e) {
              String temp = '';
              temp = e.naturalLanguage != null &&
                      e.naturalLanguage!.contains("Function")
                  ? e.naturalLanguage!.replaceAll("Function", "System")
                  : e.naturalLanguage!;
              return Text(
                temp,
                style: TextStyle(
                    fontSize: 16,
                    fontFamily: "TiemposText",
                    fontWeight: FontWeight.normal),
              );
            },
          ).toList(),
        ),
      ),
    );
  }

  /// Builds the outputstack section
  Widget _buildOutputStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'outputStack',
      'Output Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildOutputStackContent(context),
    );
  }

  /// Builds the outputstack content
  Widget _buildOutputStackContent(BuildContext context) {
    final parsedData = widget.workflowLoData.parsedData!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          parsedData.loOutputStack?.naturalLanguage ?? '',
          style: TextStyle(
              fontSize: 16,
              fontFamily: "TiemposText",
              fontWeight: FontWeight.normal),
        )
      ],
    );
  }

  /// Builds the databasestack section
  Widget _buildDatabaseStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'databaseStack',
      'Database Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildDatabaseStackContent(context),
    );
  }

  /// Builds the databasestack content
  Widget _buildDatabaseStackContent(BuildContext context) {
    final localObjectives = widget.workflowLoData.parsedData!.localObjectives!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [],
    );
  }

  /// Builds the uistack section
  Widget _buildUIStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'uiStack',
      'UI Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildUIStackContent(context),
    );
  }

  /// Builds the uistack content
  Widget _buildUIStackContent(BuildContext context) {
    final parsedData = widget.workflowLoData.parsedData!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          parsedData?.loUiStack?.naturalLanguage ?? "",
          style: TextStyle(
              fontSize: 16,
              fontFamily: "TiemposText",
              fontWeight: FontWeight.normal),
        )
      ],
    );
  }

  /// Builds the aistack section
  Widget _buildAIStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'aiStack',
      'Ai Stack',
      widget.workflowLoData.parsedData?.loNestedFunctions != null,
      () => _buildAIStackContent(context),
    );
  }

  /// Builds the aistack content
  Widget _buildAIStackContent(BuildContext context) {
    final localObjectives = widget.workflowLoData.parsedData!.localObjectives!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [],
    );
  }

  /// Builds the local objectives section
  Widget _buildLocalObjectivesSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'localObjectives',
      'Local Objectives',
      widget.workflowLoData.parsedData?.localObjectives != null,
      () => _buildLocalObjectivesContent(context),
    );
  }

  /// Builds the local objectives content
  Widget _buildLocalObjectivesContent(BuildContext context) {
    final localObjectives = widget.workflowLoData.parsedData!.localObjectives!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('Name', localObjectives.name ?? 'N/A'),
        _buildDetailItem('Version', localObjectives.version ?? 'N/A'),
        _buildDetailItem('Status', localObjectives.status ?? 'N/A'),
        _buildDetailItem(
            'Function Type', localObjectives.functionType ?? 'N/A'),
        _buildDetailItem(
            'Execution Rights', localObjectives.executionRights ?? 'N/A'),
      ],
    );
  }

  /// Builds a detail item
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• $label: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontFamily: "TiemposText",
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the LO input stack section
  Widget _buildLoInputStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loInputStack',
      'LO Input Stack',
      widget.workflowLoData.parsedData?.loInputStack != null,
      () => _buildLoInputStackContent(context),
    );
  }

  /// Builds the LO input stack content
  Widget _buildLoInputStackContent(BuildContext context) {
    final inputStack = widget.workflowLoData.parsedData!.loInputStack!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('Description', inputStack.description ?? 'N/A'),
      ],
    );
  }

  /// Builds the LO input items section
  Widget _buildLoInputItemsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loInputItems',
      'LO Input Items',
      widget.workflowLoData.parsedData?.loInputItems != null &&
          widget.workflowLoData.parsedData!.loInputItems!.isNotEmpty,
      () => _buildLoInputItemsContent(context),
    );
  }

  /// Builds the LO input items content with tabs
  Widget _buildLoInputItemsContent(BuildContext context) {
    final inputItems = widget.workflowLoData.parsedData!.loInputItems!;

    return DefaultTabController(
      length: inputItems.length,
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: inputItems.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(
                  text: 'Input Item ${index + 1}',
                  iconMargin: EdgeInsets.zero,
                  //  textAlign:  TextAlign.start,
                );
              }).toList(),
            ),
          ),
          SizedBox(
            height: 200,
            child: TabBarView(
              children: inputItems.map((item) {
                return SingleChildScrollView(
                  //  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('Slot ID', item.slotId ?? 'N/A'),
                      _buildDetailItem(
                          'Required', item.required?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Data Type', item.dataType?.toString() ?? 'N/A'),
                      _buildDetailItem('Dependent Attribute',
                          item.dependentAttribute?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Source Type', item.sourceType?.toString() ?? 'N/A'),
                      _buildDetailItem('Source Description',
                          item.sourceDescription ?? 'N/A'),
                      _buildDetailItem(
                          'Read Only', item.readOnly?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // LO Output Stack section
  Widget _buildLoOutputStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loOutputStack',
      'LO Output Stack',
      widget.workflowLoData.parsedData?.loOutputStack != null,
      () => _buildLoOutputStackContent(context),
    );
  }

  /// Builds the LO output stack content
  Widget _buildLoOutputStackContent(BuildContext context) {
    final outputStack = widget.workflowLoData.parsedData!.loOutputStack!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('Description', outputStack.description ?? 'N/A'),
      ],
    );
  }

  Widget _buildLoOutputItemsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loOutputItems',
      'LO Output Items',
      widget.workflowLoData.parsedData?.loOutputItems != null &&
          widget.workflowLoData.parsedData!.loOutputItems!.isNotEmpty,
      () => _buildLoOutputItemsContent(context),
    );
  }

  /// Builds the LO output items content with tabs
  Widget _buildLoOutputItemsContent(BuildContext context) {
    final outputItems = widget.workflowLoData.parsedData!.loOutputItems!;

    return DefaultTabController(
      length: outputItems.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: outputItems.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Output Item ${index + 1}');
              }).toList(),
            ),
          ),

          // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: outputItems.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Output Item ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 60,
            child: TabBarView(
              children: outputItems.map((item) {
                return SingleChildScrollView(
                  // padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('Slot ID', item.slotId ?? 'N/A'),
                      _buildDetailItem('Type', item.type?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoEntityValidationsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loEntityValidations',
      'LO Entity Validations',
      widget.workflowLoData.parsedData?.loEntityValidations != null &&
          widget.workflowLoData.parsedData!.loEntityValidations!.isNotEmpty,
      () => _buildLoEntityValidationsContent(context),
    );
  }

  /// Builds the LO entity validations content with tabs
  Widget _buildLoEntityValidationsContent(BuildContext context) {
    final validations = widget.workflowLoData.parsedData!.loEntityValidations!;

    return DefaultTabController(
      length: validations.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: validations.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ),
          // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: validations.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Validation ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 130,
            child: TabBarView(
              children: validations.map((validation) {
                return SingleChildScrollView(
                  //  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('Entity ID',
                          validation.entityId?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Attribute ID', validation.attributeId ?? 'N/A'),
                      _buildDetailItem('Validation Condition',
                          validation.validationCondition ?? 'N/A'),
                      _buildDetailItem(
                          'Error Message', validation.errorMessage ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoUiStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loUiStack',
      'LO UI Stack',
      widget.workflowLoData.parsedData?.loUiStack != null,
      () => _buildLoUiStackContent(context),
    );
  }

  /// Builds the LO UI stack content
  Widget _buildLoUiStackContent(BuildContext context) {
    final uiStack = widget.workflowLoData.parsedData!.loUiStack!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('UI Form', uiStack.loId ?? 'N/A'),
      ],
    );
  }

  Widget _buildLoUiEntityAttributeStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loUiEntityAttributeStack',
      'LO UI Entity Attribute Stack',
      widget.workflowLoData.parsedData?.loUiEntityAttributeStack != null &&
          widget
              .workflowLoData.parsedData!.loUiEntityAttributeStack!.isNotEmpty,
      () => _buildLoUiEntityAttributeStackContent(context),
    );
  }

  /// Builds the LO UI entity attribute stack content with tabs
  Widget _buildLoUiEntityAttributeStackContent(BuildContext context) {
    final attributeStacks =
        widget.workflowLoData.parsedData!.loUiEntityAttributeStack!;

    return DefaultTabController(
      length: attributeStacks.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: attributeStacks.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ),

          // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: attributeStacks.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Attribute ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 150,
            child: TabBarView(
              children: attributeStacks.map((stack) {
                return SingleChildScrollView(
                  // padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem(
                          'Entity ID', stack.entityId?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Attribute ID', stack.attributeId ?? 'N/A'),
                      _buildDetailItem('UI Form', stack.uiForm ?? 'N/A'),
                      _buildDetailItem('Helper Tip', stack.helperTip ?? 'N/A'),
                      _buildDetailItem(
                          'Read Only', stack.readOnly?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoDataMappingStackSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loDataMappingStack',
      'LO Data Mapping Stack',
      widget.workflowLoData.parsedData?.loDataMappingStack != null,
      () => _buildLoDataMappingStackContent(context),
    );
  }

  /// Builds the LO data mapping stack content
  Widget _buildLoDataMappingStackContent(BuildContext context) {
    final mappingStack = widget.workflowLoData.parsedData!.loDataMappingStack!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailItem('Description', mappingStack.description ?? 'N/A'),
      ],
    );
  }

  Widget _buildLoDataMappingsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loDataMappings',
      'LO Data Mappings',
      widget.workflowLoData.parsedData?.loDataMappings != null &&
          widget.workflowLoData.parsedData!.loDataMappings!.isNotEmpty,
      () => _buildLoDataMappingsContent(context),
    );
  }

  /// Builds the LO data mappings content with tabs
  Widget _buildLoDataMappingsContent(BuildContext context) {
    final mappings = widget.workflowLoData.parsedData!.loDataMappings!;

    return DefaultTabController(
      length: mappings.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: mappings.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ), // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: mappings.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Mapping ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 150,
            child: TabBarView(
              children: mappings.map((mapping) {
                return SingleChildScrollView(
                  //  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('Source Entity',
                          mapping.sourceEntity?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Source Attribute', mapping.sourceAttribute ?? 'N/A'),
                      _buildDetailItem(
                          'Target Entity', mapping.targetEntity ?? 'N/A'),
                      _buildDetailItem(
                          'Target Attribute', mapping.targetAttribute ?? 'N/A'),
                      _buildDetailItem(
                          'Mapping Type', mapping.mappingType ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoNestedFunctionsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loNestedFunctions',
      'LO Nested Functions',
      widget.workflowLoData.parsedData?.loNestedFunctions != null &&
          widget.workflowLoData.parsedData!.loNestedFunctions!.isNotEmpty,
      () => _buildLoNestedFunctionsContent(context),
    );
  }

  /// Builds the LO nested functions content with tabs
  Widget _buildLoNestedFunctionsContent(BuildContext context) {
    final functions = widget.workflowLoData.parsedData!.loNestedFunctions!;

    return DefaultTabController(
      length: functions.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: functions.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ), // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: functions.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Function ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 150,
            child: TabBarView(
              children: functions.map((function) {
                return SingleChildScrollView(
                  // padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem(
                          'Function Name', function.functionName ?? 'N/A'),
                      _buildDetailItem(
                          'Function Type', function.functionType ?? 'N/A'),
                      _buildDetailItem(
                          'Returns', function.returns?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Outputs To', function.outputsTo ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildExecutionPathwaysSection(
  //     BuildContext context, Map<String, GlobalKey> sectionKeys) {
  //   return _buildSection(
  //     context,
  //     sectionKeys,
  //     'executionPathways',
  //     'Execution Pathways',
  //     widget.workflowLoData.parsedData?.executionPathways != null &&
  //         widget.workflowLoData.parsedData!.executionPathways!.isNotEmpty,
  //     () => _buildExecutionPathwaysContent(context),
  //   );
  // }

  // /// Builds the execution pathways content with tabs
  // Widget _buildExecutionPathwaysContent(BuildContext context) {
  //   final pathways = widget.workflowLoData.parsedData!.executionPathways!;

  //   return DefaultTabController(
  //     length: pathways.length,
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Transform.translate(
  //           offset: Offset(0, -10),
  //           child: TabBar(
  //             padding: EdgeInsets.zero,
  //             tabAlignment: TabAlignment.start,
  //             labelPadding: EdgeInsets.only(right: 16),
  //             dividerColor: Colors.transparent,
  //             isScrollable: true,
  //             labelColor: Colors.black,
  //             labelStyle: TextStyle(
  //               fontWeight: FontWeight.bold,
  //               fontSize: 10,
  //               fontFamily: "TiemposText",
  //             ),
  //             indicatorPadding: EdgeInsets.symmetric(vertical: 10),
  //             unselectedLabelColor: Colors.black,
  //             unselectedLabelStyle: TextStyle(
  //               fontWeight: FontWeight.w400,
  //               fontSize: 10,
  //               fontFamily: "TiemposText",
  //             ),
  //             indicatorColor: Colors.grey.shade600,
  //             indicatorWeight: 0.1,
  //             tabs: pathways.asMap().entries.map((entry) {
  //               int index = entry.key;
  //               return Tab(text: 'Validation ${index + 1}');
  //             }).toList(),
  //           ),
  //         ), // TabBar(
  //         //   isScrollable: true,
  //         //   labelColor: Colors.blue.shade700,
  //         //   unselectedLabelColor: Colors.grey.shade600,
  //         //   indicatorColor: Colors.blue.shade700,
  //         //   tabs: pathways.asMap().entries.map((entry) {
  //         //     int index = entry.key;
  //         //     return Tab(text: 'Pathway ${index + 1}');
  //         //   }).toList(),
  //         // ),
  //         SizedBox(
  //           height: 150,
  //           child: TabBarView(
  //             children: pathways.map((pathway) {
  //               return SingleChildScrollView(
  //                 //  padding: EdgeInsets.all(8),
  //                 child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     _buildDetailItem(
  //                         'Pathway Type', pathway.pathwayType ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Created At', pathway.createdAt ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Created By', pathway.createdBy ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Updated At', pathway.updatedAt ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Updated By', pathway.updatedBy ?? 'N/A'),
  //                   ],
  //                 ),
  //               );
  //             }).toList(),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildExecutionPathwayConditionsSection(
  //     BuildContext context, Map<String, GlobalKey> sectionKeys) {
  //   return _buildSection(
  //     context,
  //     sectionKeys,
  //     'executionPathwayConditions',
  //     'Execution Pathway Conditions',
  //     widget.workflowLoData.parsedData?.executionPathwayConditions != null &&
  //         widget.workflowLoData.parsedData!.executionPathwayConditions!
  //             .isNotEmpty,
  //     () => _buildExecutionPathwayConditionsContent(context),
  //   );
  // }

  // /// Builds the execution pathway conditions content with tabs
  // Widget _buildExecutionPathwayConditionsContent(BuildContext context) {
  //   final conditions =
  //       widget.workflowLoData.parsedData!.executionPathwayConditions!;

  //   return DefaultTabController(
  //     length: conditions.length,
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Transform.translate(
  //           offset: Offset(0, -10),
  //           child: TabBar(
  //             padding: EdgeInsets.zero,
  //             tabAlignment: TabAlignment.start,
  //             labelPadding: EdgeInsets.only(right: 16),
  //             dividerColor: Colors.transparent,
  //             isScrollable: true,
  //             labelColor: Colors.black,
  //             labelStyle: TextStyle(
  //               fontWeight: FontWeight.bold,
  //               fontSize: 10,
  //               fontFamily: "TiemposText",
  //             ),
  //             indicatorPadding: EdgeInsets.symmetric(vertical: 10),
  //             unselectedLabelColor: Colors.black,
  //             unselectedLabelStyle: TextStyle(
  //               fontWeight: FontWeight.w400,
  //               fontSize: 10,
  //               fontFamily: "TiemposText",
  //             ),
  //             indicatorColor: Colors.grey.shade600,
  //             indicatorWeight: 0.1,
  //             tabs: conditions.asMap().entries.map((entry) {
  //               int index = entry.key;
  //               return Tab(text: 'Validation ${index + 1}');
  //             }).toList(),
  //           ),
  //         ), // TabBar(
  //         //   isScrollable: true,
  //         //   labelColor: Colors.blue.shade700,
  //         //   unselectedLabelColor: Colors.grey.shade600,
  //         //   indicatorColor: Colors.blue.shade700,
  //         //   tabs: conditions.asMap().entries.map((entry) {
  //         //     int index = entry.key;
  //         //     return Tab(text: 'Condition ${index + 1}');
  //         //   }).toList(),
  //         // ),
  //         SizedBox(
  //           height: 200,
  //           child: TabBarView(
  //             children: conditions.map((condition) {
  //               return SingleChildScrollView(
  //                 //  padding: EdgeInsets.all(8),
  //                 child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     _buildDetailItem('Condition Type',
  //                         condition['condition_type'] ?? 'N/A'),
  //                     _buildDetailItem('Condition Entity',
  //                         condition['condition_entity'] ?? 'N/A'),
  //                     _buildDetailItem('Condition Attribute',
  //                         condition['condition_attribute'] ?? 'N/A'),
  //                     _buildDetailItem('Condition Operator',
  //                         condition['condition_operator'] ?? 'N/A'),
  //                     _buildDetailItem('Condition Value',
  //                         condition['condition_value'] ?? 'N/A'),
  //                     _buildDetailItem('Multiple Conditions',
  //                         condition['multiple_conditions'] ?? 'N/A'),
  //                     _buildDetailItem('Multiple Conditions Operator',
  //                         condition['multiple_conditions_operator'] ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Created At', condition['created_at'] ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Created By', condition['created_by'] ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Updated At', condition['updated_at'] ?? 'N/A'),
  //                     _buildDetailItem(
  //                         'Updated By', condition['updated_by'] ?? 'N/A'),
  //                   ],
  //                 ),
  //               );
  //             }).toList(),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildLoNestedFunctionInputStacksSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loNestedFunctionInputStacks',
      'LO Nested Function Input Stacks',
      widget.workflowLoData.parsedData?.loNestedFunctionInputStacks != null &&
          widget.workflowLoData.parsedData!.loNestedFunctionInputStacks!
              .isNotEmpty,
      () => _buildLoNestedFunctionInputStacksContent(context),
    );
  }

  /// Builds the LO nested function input stacks content with tabs
  Widget _buildLoNestedFunctionInputStacksContent(BuildContext context) {
    final inputStacks =
        widget.workflowLoData.parsedData!.loNestedFunctionInputStacks!;

    return DefaultTabController(
      length: inputStacks.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: inputStacks.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ), // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: inputStacks.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Input Stack ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 250,
            child: TabBarView(
              children: inputStacks.map((stack) {
                return SingleChildScrollView(
                  //  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem(
                          'Function Name', stack.functionName ?? 'N/A'),
                      _buildDetailItem('Stack Name', stack.stackName ?? 'N/A'),
                      _buildDetailItem(
                          'Stack Description', stack.stackDescription ?? 'N/A'),
                      _buildDetailItem(
                          'Stack Type', stack.stackType?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Is Active', stack.isActive?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoNestedFunctionOutputItemsSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loNestedFunctionOutputItems',
      'LO Nested Function Output Items',
      widget.workflowLoData.parsedData?.loNestedFunctionOutputItems != null &&
          widget.workflowLoData.parsedData!.loNestedFunctionOutputItems!
              .isNotEmpty,
      () => _buildLoNestedFunctionOutputItemsContent(context),
    );
  }

  /// Builds the LO nested function output items content with tabs
  Widget _buildLoNestedFunctionOutputItemsContent(BuildContext context) {
    final outputItems =
        widget.workflowLoData.parsedData!.loNestedFunctionOutputItems!;

    return DefaultTabController(
      length: outputItems.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: outputItems.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ), // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: outputItems.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Output Item ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 200,
            child: TabBarView(
              children: outputItems.map((item) {
                return SingleChildScrollView(
                  //  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem(
                          'Function Name', item.functionName ?? 'N/A'),
                      _buildDetailItem('Output Name', item.outputName ?? 'N/A'),
                      _buildDetailItem('Output Description',
                          item.outputDescription ?? 'N/A'),
                      _buildDetailItem(
                          'Data Type', item.dataType?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Is Required', item.isRequired?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoNestedFunctionOutputStacksSection(
      BuildContext context, Map<String, GlobalKey> sectionKeys) {
    return _buildSection(
      context,
      sectionKeys,
      'loNestedFunctionOutputStacks',
      'LO Nested Function Output Stacks',
      widget.workflowLoData.parsedData?.loNestedFunctionOutputStacks != null &&
          widget.workflowLoData.parsedData!.loNestedFunctionOutputStacks!
              .isNotEmpty,
      () => _buildLoNestedFunctionOutputStacksContent(context),
    );
  }

  /// Builds the LO nested function output stacks content with tabs
  Widget _buildLoNestedFunctionOutputStacksContent(BuildContext context) {
    final outputStacks =
        widget.workflowLoData.parsedData!.loNestedFunctionOutputStacks!;

    return DefaultTabController(
      length: outputStacks.length,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Transform.translate(
            offset: Offset(0, -10),
            child: TabBar(
              padding: EdgeInsets.zero,
              tabAlignment: TabAlignment.start,
              labelPadding: EdgeInsets.only(right: 16),
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelColor: Colors.black,
              labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorPadding: EdgeInsets.symmetric(vertical: 10),
              unselectedLabelColor: Colors.black,
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
              indicatorColor: Colors.grey.shade600,
              indicatorWeight: 0.1,
              tabs: outputStacks.asMap().entries.map((entry) {
                int index = entry.key;
                return Tab(text: 'Validation ${index + 1}');
              }).toList(),
            ),
          ), // TabBar(
          //   isScrollable: true,
          //   labelColor: Colors.blue.shade700,
          //   unselectedLabelColor: Colors.grey.shade600,
          //   indicatorColor: Colors.blue.shade700,
          //   tabs: outputStacks.asMap().entries.map((entry) {
          //     int index = entry.key;
          //     return Tab(text: 'Output Stack ${index + 1}');
          //   }).toList(),
          // ),
          SizedBox(
            height: 200,
            child: TabBarView(
              children: outputStacks.map((stack) {
                return SingleChildScrollView(
                  //   padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem(
                          'Function Name', stack.functionName ?? 'N/A'),
                      _buildDetailItem('Stack Name', stack.stackName ?? 'N/A'),
                      _buildDetailItem(
                          'Stack Description', stack.stackDescription ?? 'N/A'),
                      _buildDetailItem(
                          'Stack Type', stack.stackType?.toString() ?? 'N/A'),
                      _buildDetailItem(
                          'Is Active', stack.isActive?.toString() ?? 'N/A'),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
