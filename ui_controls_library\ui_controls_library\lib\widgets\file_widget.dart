import 'dart:io';
import 'dart:convert';
// ignore: deprecated_member_use
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

// Define BorderStyle extension for dashed border
extension BorderStyleExtension on BorderStyle {
  static const BorderStyle dashed = BorderStyle.solid;
}

// We're now using the actual file_picker package

/// A configurable file upload widget that handles file selection and display.
class FileWidget extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callback
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;
  final Function(PlatformFile)? onViewFile;
  final Function(PlatformFile)? onOpenFile;
  final Function()? onCancelUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidget({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    this.onViewFile,
    this.onOpenFile,
    this.onCancelUpload,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // File-specific JSON configuration
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  /// Creates a FileWidget from a JSON map
  factory FileWidget.fromJson(Map<String, dynamic> json) {
    // Parse JSON callbacks using utility
    final callbackData = FileWidgetJsonParser.parseJsonCallbacks(json);
    var jsonCallbacks = callbackData['jsonCallbacks'] as Map<String, dynamic>?;
    var useJsonCallbacks = callbackData['useJsonCallbacks'] as bool;

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse file-specific configuration
    Map<String, dynamic>? fileHandlingConfig;
    bool useJsonFileHandling = json['useJsonFileHandling'] as bool? ?? false;

    if (json['fileHandlingConfig'] != null) {
      if (json['fileHandlingConfig'] is Map) {
        fileHandlingConfig = Map<String, dynamic>.from(
          json['fileHandlingConfig'] as Map,
        );
        useJsonFileHandling = true;
      } else if (json['fileHandlingConfig'] is String) {
        try {
          fileHandlingConfig =
              jsonDecode(json['fileHandlingConfig'] as String)
                  as Map<String, dynamic>;
          useJsonFileHandling = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON using utility parsers
    return FileWidget(
      isRequired: FileWidgetJsonParser.parseBool(json['isRequired']),
      allowMultiple: FileWidgetJsonParser.parseBool(json['allowMultiple']),
      allowedExtensions: FileWidgetJsonParser.parseStringList(
        json['allowedExtensions'],
      ),
      fileType: FileWidgetJsonParser.parseFileType(json['fileType']),
      maxFileSizeBytes: FileWidgetJsonParser.parseInt(json['maxFileSizeBytes']),
      maxFiles: FileWidgetJsonParser.parseInt(json['maxFiles']),
      textColor:
          FileWidgetJsonParser.parseColor(json['textColor']) ?? Colors.black,
      backgroundColor:
          FileWidgetJsonParser.parseColor(json['backgroundColor']) ??
          Colors.white,
      borderColor:
          FileWidgetJsonParser.parseColor(json['borderColor']) ??
          const Color(0xFFE0E0E0),
      borderWidth: FileWidgetJsonParser.parseDouble(
        json['borderWidth'],
        defaultValue: 1.0,
      ),
      borderRadius: FileWidgetJsonParser.parseDouble(
        json['borderRadius'],
        defaultValue: 4.0,
      ),
      hasBorder: FileWidgetJsonParser.parseBool(
        json['hasBorder'],
        defaultValue: true,
      ),
      fontSize: FileWidgetJsonParser.parseDouble(
        json['fontSize'],
        defaultValue: 16.0,
      ),
      fontWeight: FileWidgetJsonParser.parseFontWeight(json['fontWeight']),
      isCompact: FileWidgetJsonParser.parseBool(json['isCompact']),
      hasShadow: FileWidgetJsonParser.parseBool(json['hasShadow']),
      elevation: FileWidgetJsonParser.parseDouble(
        json['elevation'],
        defaultValue: 2.0,
      ),
      isDarkTheme: FileWidgetJsonParser.parseBool(json['isDarkTheme']),
      textAlign: FileWidgetJsonParser.parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      buttonText: json['buttonText'] as String? ?? 'Choose File',
      showIcon: FileWidgetJsonParser.parseBool(
        json['showIcon'],
        defaultValue: true,
      ),
      icon:
          FileWidgetJsonParser.parseIconData(json['icon']) ?? Icons.upload_file,
      isReadOnly: FileWidgetJsonParser.parseBool(json['isReadOnly']),
      isDisabled: FileWidgetJsonParser.parseBool(json['isDisabled']),
      showFileName: FileWidgetJsonParser.parseBool(
        json['showFileName'],
        defaultValue: true,
      ),
      showFileSize: FileWidgetJsonParser.parseBool(
        json['showFileSize'],
        defaultValue: true,
      ),
      showFileType: FileWidgetJsonParser.parseBool(
        json['showFileType'],
        defaultValue: true,
      ),
      showClearButton: FileWidgetJsonParser.parseBool(
        json['showClearButton'],
        defaultValue: true,
      ),
      showPreview: FileWidgetJsonParser.parseBool(json['showPreview']),
      uploadImmediately: FileWidgetJsonParser.parseBool(
        json['uploadImmediately'],
      ),
      showProgressBar: FileWidgetJsonParser.parseBool(json['showProgressBar']),
      allowDragDrop: FileWidgetJsonParser.parseBool(json['allowDragDrop']),
      width: FileWidgetJsonParser.parseDouble(
        json['width'],
        defaultValue: double.infinity,
      ),
      height: FileWidgetJsonParser.parseDouble(json['height']),
      padding: FileWidgetJsonParser.parseEdgeInsets(json['padding']),
      margin: FileWidgetJsonParser.parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: FileWidgetJsonParser.parseColor(json['hoverColor']),
      focusColor: FileWidgetJsonParser.parseColor(json['focusColor']),
      enableFeedback: FileWidgetJsonParser.parseBool(
        json['enableFeedback'],
        defaultValue: true,
      ),
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // File-specific JSON configuration
      useJsonFileHandling: useJsonFileHandling,
      fileHandlingConfig: fileHandlingConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  Map<String, dynamic> toJson() {
    return {};
  }

  @override
  State<FileWidget> createState() => _FileWidgetState();
}

// Enum for file upload states
enum FileUploadState {
  defaultState, // Shows "Upload File" button
  uploading, // Shows progress bar with "Uploading (1)" text
  uploaded, // Shows green progress bar with "uploaded 100%" text
  completed, // Shows only file name with action icons
}

class _FileWidgetState extends State<FileWidget> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isValid = true;
  bool _isDragging = false;
  double _uploadProgress = 0.0;
  bool _isUploading = false;
  FileUploadState _uploadState = FileUploadState.defaultState;
  bool _isHovered = false;
  bool _hasFocus = false;
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  final hoverIconColor = Color(0xFF0058FF);
  final defaultIconColor = Color(0xFFCCCCCC);
  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store file handling configuration from JSON
  Map<String, dynamic>? _fileHandlingConfig;

  @override
  void initState() {
    super.initState();
    _errorText = widget.errorText;
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};
  }

  void _executeJsonCallback(String callbackType, [dynamic data]) {
    // Stub implementation
  }

  void _applyJsonValidation() {
    // Stub implementation
  }

  void _applyJsonStyling() {
    // Stub implementation
  }

  void _applyJsonFormatting() {
    // Stub implementation
  }

  void _applyFileHandlingConfig() {
    // Stub implementation
  }

  bool _validateFiles() {
    return true;
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      final result = await FilePicker.platform.pickFiles(
        type: widget.fileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions:
            widget.fileType == FileType.custom
                ? widget.allowedExtensions
                : null,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFiles = result.files;
          _errorText = null;
        });

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        // Start upload automatically
        _uploadFiles();
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
        _isValid = false;
      });
    }
  }

  void _clearFiles() {
    setState(() {
      _selectedFiles = [];
      _errorText = widget.errorText;
      _isValid = true;
      _uploadProgress = 0.0;
      _isUploading = false;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }
  }

  void _cancelUpload() {
    if (!_isUploading) return;

    setState(() {
      _isUploading = false;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.defaultState;
    });

    if (widget.onCancelUpload != null) {
      widget.onCancelUpload!();
    }
  }

  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty || widget.isDisabled || widget.isReadOnly) {
      return;
    }

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadState = FileUploadState.uploading;
    });

    try {
      // Simulate upload progress
      for (int i = 0; i <= 20; i++) {
        if (!_isUploading) return; // Exit if cancelled

        await Future.delayed(const Duration(milliseconds: 100));

        if (!_isUploading) return; // Check again after delay

        final progress = i / 20;
        setState(() {
          _uploadProgress = progress;
        });
      }

      // Show uploaded state (100% complete)
      setState(() {
        _uploadState = FileUploadState.uploaded;
      });

      // Show uploaded state briefly before finishing
      await Future.delayed(const Duration(milliseconds: 1500));

      if (!_isUploading) return; // Check if cancelled during delay

      setState(() {
        _isUploading = false;
        _uploadState = FileUploadState.completed;
      });

      if (widget.onUpload != null) {
        widget.onUpload!(_selectedFiles);
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
        _errorText = 'Upload failed: $e';
        _isValid = false;
      });
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else if (screenWidth >= 768) {
      return 12.0; // Small
    } else {
      return 10.0; // Extra Small (fallback for very small screens)
    }
  }

  Widget _buildDragDropArea(Widget child) {
    return child; // Simplified for now
  }

  /// Gets the MIME type for a file based on its extension
  String _getMimeType(String? extension) {
    if (extension == null) return 'application/octet-stream';

    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'svg':
        return 'image/svg+xml';
      case 'mp4':
        return 'video/mp4';
      case 'avi':
        return 'video/x-msvideo';
      case 'mov':
        return 'video/quicktime';
      case 'wmv':
        return 'video/x-ms-wmv';
      case 'mp3':
        return 'audio/mpeg';
      case 'wav':
        return 'audio/wav';
      case 'flac':
        return 'audio/flac';
      case 'aac':
        return 'audio/aac';
      case 'zip':
        return 'application/zip';
      case 'rar':
        return 'application/vnd.rar';
      case '7z':
        return 'application/x-7z-compressed';
      case 'txt':
        return 'text/plain';
      case 'html':
        return 'text/html';
      case 'css':
        return 'text/css';
      case 'js':
        return 'application/javascript';
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      default:
        return 'application/octet-stream';
    }
  }

  /// Opens the file in a new window/tab (for web) or with default app (for mobile/desktop)
  Future<void> _openFileInNewWindow(PlatformFile file) async {
    try {
      // Show loading message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening file: ${file.name}'),
          duration: const Duration(seconds: 2),
        ),
      );

      if (kIsWeb) {
        // Handle web platform
        if (file.bytes != null) {
          // Create blob URL from file bytes with proper MIME type
          final mimeType = _getMimeType(file.extension);
          final blob = html.Blob([file.bytes!], mimeType);
          final url = html.Url.createObjectUrlFromBlob(blob);

          // Open in new tab/window
          html.window.open(url, '_blank');

          // Clean up the blob URL after a delay
          Future.delayed(const Duration(seconds: 5), () {
            html.Url.revokeObjectUrl(url);
          });

          print('Successfully opened file in new tab: ${file.name}');
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Cannot open file: ${file.name} (no file data available)',
              ),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        // Handle mobile/desktop platforms
        if (file.path != null) {
          // Create URI from file path
          final Uri fileUri = Uri.file(file.path!);

          // Check if the URL can be launched
          if (await canLaunchUrl(fileUri)) {
            // Launch the file with the default app
            await launchUrl(
              fileUri,
              mode:
                  LaunchMode
                      .externalApplication, // Opens with default system app
            );
            print(
              'Successfully opened file: ${file.name} at path: ${file.path}',
            );
          } else {
            // If direct file launch fails, try with file:// scheme
            final Uri fileSchemeUri = Uri.parse('file://${file.path}');
            if (await canLaunchUrl(fileSchemeUri)) {
              await launchUrl(
                fileSchemeUri,
                mode: LaunchMode.externalApplication,
              );
              print(
                'Successfully opened file with file:// scheme: ${file.name}',
              );
            } else {
              // Show error if file cannot be opened
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Cannot open file: ${file.name}. No default app found.',
                  ),
                  duration: const Duration(seconds: 3),
                  backgroundColor: Colors.orange,
                ),
              );
              print('Cannot launch file: ${file.path}');
            }
          }
        } else {
          // File doesn't have a local path
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Cannot open file: ${file.name} (no local path)'),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
          print('File has no local path: ${file.name}');
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening file: $e'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
      print('Error opening file: $e');
    }
  }

  /// Shows file details and preview
  void _viewFile(PlatformFile file) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.description, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'File Details',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Name', file.name),
                const SizedBox(height: 8),
                _buildDetailRow('Size', _formatFileSize(file.size)),
                const SizedBox(height: 8),
                if (file.extension != null)
                  _buildDetailRow('Type', file.extension!.toUpperCase()),
                if (file.extension != null) const SizedBox(height: 8),
                if (file.path != null) _buildDetailRow('Path', file.path!),
                if (file.path != null) const SizedBox(height: 8),
                _buildDetailRow(
                  'Last Modified',
                  DateTime.now().toString().split('.')[0],
                ),
                const SizedBox(height: 16),

                // File type icon
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getFileIcon(file.extension),
                      size: 48,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _openFileInNewWindow(file);
              },
              icon: Icon(Icons.open_in_new, size: 16),
              label: Text('Open'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Builds a detail row for the file info dialog
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: Colors.black87),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  /// Gets appropriate color for file type icon
  Color _getFileIconColor(String? extension) {
    if (extension == null) return const Color(0xFF666666);

    switch (extension.toLowerCase()) {
      // Document Types - Blue tones
      case 'pdf':
        return const Color(0xFFD32F2F); // Red for PDF
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2); // Blue for Word
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C); // Green for Excel
      case 'ppt':
      case 'pptx':
        return const Color(0xFFFF5722); // Orange for PowerPoint
      case 'txt':
      case 'rtf':
        return const Color(0xFF757575); // Gray for text files

      // Image Types - Purple/Pink tones
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
      case 'webp':
      case 'ico':
      case 'tiff':
      case 'tif':
        return const Color(0xFF9C27B0); // Purple for images
      case 'psd':
        return const Color(0xFF00BCD4); // Cyan for Photoshop
      case 'ai':
        return const Color(0xFFFF9800); // Orange for Illustrator

      // Video Types - Red tones
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
      case 'mkv':
      case '3gp':
      case 'm4v':
        return const Color(0xFFE91E63); // Pink for videos

      // Audio Types - Green tones
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'ogg':
      case 'wma':
      case 'm4a':
      case 'opus':
        return const Color(0xFF4CAF50); // Green for audio

      // Archive Types - Brown tones
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
      case 'bz2':
      case 'xz':
        return const Color(0xFF795548); // Brown for archives

      // Code/Web Types - Various colors
      case 'html':
      case 'htm':
        return const Color(0xFFFF5722); // Orange for HTML
      case 'css':
        return const Color(0xFF2196F3); // Blue for CSS
      case 'js':
      case 'jsx':
        return const Color(0xFFFFC107); // Yellow for JavaScript
      case 'ts':
      case 'tsx':
        return const Color(0xFF3F51B5); // Indigo for TypeScript
      case 'json':
        return const Color(0xFF607D8B); // Blue Gray for JSON
      case 'xml':
      case 'yaml':
      case 'yml':
        return const Color(0xFF9E9E9E); // Gray for markup
      case 'php':
        return const Color(0xFF673AB7); // Deep Purple for PHP
      case 'py':
        return const Color(0xFF3F51B5); // Indigo for Python
      case 'java':
        return const Color(0xFFFF5722); // Orange for Java
      case 'cpp':
      case 'c':
      case 'h':
        return const Color(0xFF607D8B); // Blue Gray for C/C++
      case 'cs':
        return const Color(0xFF9C27B0); // Purple for C#
      case 'dart':
        return const Color(0xFF00BCD4); // Cyan for Dart
      case 'sql':
        return const Color(0xFF795548); // Brown for SQL

      // Font Types
      case 'ttf':
      case 'otf':
      case 'woff':
      case 'woff2':
      case 'eot':
        return const Color(0xFF9C27B0); // Purple for fonts

      // Email Types
      case 'eml':
      case 'msg':
        return const Color(0xFF2196F3); // Blue for email

      // Calendar Types
      case 'ics':
        return const Color(0xFF4CAF50); // Green for calendar

      // Database Types
      case 'db':
      case 'sqlite':
      case 'mdb':
        return const Color(0xFF795548); // Brown for database

      // Executable Types
      case 'exe':
      case 'msi':
      case 'dmg':
      case 'pkg':
      case 'deb':
      case 'rpm':
      case 'apk':
        return const Color(0xFFFF5722); // Orange for executables

      // Configuration Types
      case 'ini':
      case 'cfg':
      case 'conf':
        return const Color(0xFF607D8B); // Blue Gray for config

      // Log Types
      case 'log':
        return const Color(0xFF9E9E9E); // Gray for logs

      // Backup Types
      case 'bak':
      case 'backup':
        return const Color(0xFF795548); // Brown for backups

      // Certificate Types
      case 'crt':
      case 'cer':
      case 'pem':
      case 'key':
        return const Color(0xFF4CAF50); // Green for certificates

      // Default fallback
      default:
        return const Color(0xFF666666); // Default gray
    }
  }

  /// Gets appropriate icon for file type with comprehensive support
  IconData _getFileIcon(String? extension) {
    if (extension == null) return Icons.insert_drive_file;

    switch (extension.toLowerCase()) {
      // Document Types
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'rtf':
        return Icons.article;
      case 'odt':
        return Icons.description;
      case 'ods':
        return Icons.table_chart;
      case 'odp':
        return Icons.slideshow;

      // Image Types
      case 'jpg':
      case 'jpeg':
        return Icons.image;
      case 'png':
        return Icons.image;
      case 'gif':
        return Icons.gif;
      case 'bmp':
        return Icons.image;
      case 'svg':
        return Icons.image;
      case 'webp':
        return Icons.image;
      case 'ico':
        return Icons.image;
      case 'tiff':
      case 'tif':
        return Icons.image;
      case 'psd':
        return Icons.image;
      case 'ai':
        return Icons.image;

      // Video Types
      case 'mp4':
        return Icons.video_file;
      case 'avi':
        return Icons.video_file;
      case 'mov':
        return Icons.video_file;
      case 'wmv':
        return Icons.video_file;
      case 'flv':
        return Icons.video_file;
      case 'webm':
        return Icons.video_file;
      case 'mkv':
        return Icons.video_file;
      case '3gp':
        return Icons.video_file;
      case 'm4v':
        return Icons.video_file;

      // Audio Types
      case 'mp3':
        return Icons.audio_file;
      case 'wav':
        return Icons.audio_file;
      case 'flac':
        return Icons.audio_file;
      case 'aac':
        return Icons.audio_file;
      case 'ogg':
        return Icons.audio_file;
      case 'wma':
        return Icons.audio_file;
      case 'm4a':
        return Icons.audio_file;
      case 'opus':
        return Icons.audio_file;

      // Archive Types
      case 'zip':
        return Icons.folder_zip;
      case 'rar':
        return Icons.folder_zip;
      case '7z':
        return Icons.folder_zip;
      case 'tar':
        return Icons.folder_zip;
      case 'gz':
        return Icons.folder_zip;
      case 'bz2':
        return Icons.folder_zip;
      case 'xz':
        return Icons.folder_zip;

      // Code/Web Types
      case 'html':
      case 'htm':
        return Icons.web;
      case 'css':
        return Icons.style;
      case 'js':
      case 'jsx':
        return Icons.code;
      case 'ts':
      case 'tsx':
        return Icons.code;
      case 'json':
        return Icons.data_object;
      case 'xml':
        return Icons.code;
      case 'yaml':
      case 'yml':
        return Icons.code;
      case 'php':
        return Icons.code;
      case 'py':
        return Icons.code;
      case 'java':
        return Icons.code;
      case 'cpp':
      case 'c':
      case 'h':
        return Icons.code;
      case 'cs':
        return Icons.code;
      case 'rb':
        return Icons.code;
      case 'go':
        return Icons.code;
      case 'rs':
        return Icons.code;
      case 'swift':
        return Icons.code;
      case 'kt':
        return Icons.code;
      case 'dart':
        return Icons.code;
      case 'sql':
        return Icons.storage;

      // Font Types
      case 'ttf':
      case 'otf':
      case 'woff':
      case 'woff2':
      case 'eot':
        return Icons.font_download;

      // Email Types
      case 'eml':
      case 'msg':
        return Icons.email;

      // Calendar Types
      case 'ics':
        return Icons.event;

      // Database Types
      case 'db':
      case 'sqlite':
      case 'mdb':
        return Icons.storage;

      // Executable Types
      case 'exe':
      case 'msi':
      case 'dmg':
      case 'pkg':
      case 'deb':
      case 'rpm':
      case 'apk':
        return Icons.launch;

      // Configuration Types
      case 'ini':
      case 'cfg':
      case 'conf':
        return Icons.settings;

      // Log Types
      case 'log':
        return Icons.list_alt;

      // Backup Types
      case 'bak':
      case 'backup':
        return Icons.backup;

      // Certificate Types
      case 'crt':
      case 'cer':
      case 'pem':
      case 'key':
        return Icons.security;

      // Default fallback
      default:
        return Icons.insert_drive_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_uploadState) {
      case FileUploadState.defaultState:
        content = _buildDefaultState();
        break;
      case FileUploadState.uploading:
        content = _buildUploadingState();
        break;
      case FileUploadState.uploaded:
        content = _buildUploadedState();
        break;
      case FileUploadState.completed:
        content = _buildCompletedState();
        break;
    }

    final dragDropContent = _buildDragDropArea(content);

    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: dragDropContent,
            )
            : dragDropContent;

    return Container(
      width: widget.width,
      margin: widget.margin,
      child: shadowWidget,
    );
  }

  Widget _buildDefaultState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: _getResponsiveHeight(context),
          child: ElevatedButton.icon(
            onPressed:
                widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
            icon:
                widget.showIcon && widget.icon != null
                    ? Icon(
                      widget.icon,
                      color: Colors.white,
                      // defaultIconColor,
                      size: getResponsiveIconSize(context),
                    )
                    : const SizedBox.shrink(),

            label: Text(
              'Upload ',
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: FontWeight.w400,
                color: Colors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0058FF),
              // foregroundColor: Colors.white,
              padding: _getResponsivePadding(context),
              //const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.0),
              ),
              elevation: 0,
            ),
          ),
        ),
        if (_errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            _errorText!,
            style: TextStyle(
              color: Colors.red,
              fontSize: _getResponsiveFontSize(context) * 0.8,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildUploadingState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   'Upload File',
        //   style: TextStyle(
        //     fontSize: _getResponsiveFontSize(context),
        //     fontWeight: FontWeight.w500,
        //     color: Colors.black87,
        //   ),
        //),
        //const SizedBox(height: 8),
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            //  autofocus: widget.autofocus,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              // padding: _getResponsivePadding(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                // Colors.grey.shade50,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
                // border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          // crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 12.0, left: 12.0),
                              child: Text(
                                'Uploading (${_selectedFiles.length})',
                                style: TextStyle(
                                  fontSize:
                                      _getResponsiveFontSize(context) * 0.9,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF333333),
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: _cancelUpload,
                              child: Center(
                                child: Container(
                                  padding: const EdgeInsets.only(
                                    right: 12.0,
                                    top: 5.0,
                                  ),
                                  child: Icon(
                                    Icons.close,
                                    size: getResponsiveIconSize(context),
                                    color: defaultIconColor,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: _uploadProgress,
                          backgroundColor: Colors.grey.shade300,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.blue,
                          ),
                          minHeight: 4,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   'Upload File',
        //   style: TextStyle(
        //     fontSize: _getResponsiveFontSize(context),
        //     fontWeight: FontWeight.w500,
        //     color: Colors.black87,
        //   ),
        // ),
        // const SizedBox(height: 8),
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            //  autofocus: widget.autofocus,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              //  padding: _getResponsivePadding(context),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                // Colors.green.shade50,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                // border: Border.all(color: Colors.green.shade300),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                            top: 12.0,
                            left: 12.0,
                            right: 12.0,
                          ),
                          child: Text(
                            'Uploaded 100%',
                            style: TextStyle(
                              fontSize: _getResponsiveFontSize(context) * 0.9,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF999999),
                            ),
                          ),
                        ),
                        const SizedBox(height: 3),
                        LinearProgressIndicator(
                          value: 1.0,
                          backgroundColor: Colors.green.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.green.shade600,
                          ),
                          minHeight: 4,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompletedState() {
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    if (_selectedFiles.isEmpty) {
      return _buildDefaultState();
    }

    final file = _selectedFiles.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text(
        //   'Upload File',
        //   style: TextStyle(
        //     fontSize: _getResponsiveFontSize(context),
        //     fontWeight: FontWeight.w500,
        //     color: Colors.black87,
        //   ),
        // ),
        // const SizedBox(height: 8),
        MouseRegion(
          onEnter: (_) => _onHoverChange(true),
          onExit: (_) => _onHoverChange(false),
          cursor: SystemMouseCursors.click,
          child: Focus(
            focusNode: widget.focusNode,
            //  autofocus: widget.autofocus,
            onFocusChange: _onFocusChange,
            child: Container(
              height: _getResponsiveHeight(context),
              padding: _getResponsivePadding(context),
              // padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              decoration: BoxDecoration(
                color: effectiveBackgroundColor,
                // color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border:
                    widget.hasBorder
                        ? Border.all(
                          color:
                              _isHovered
                                  ? (widget.hoverColor ??
                                      const Color(0xFF0058FF))
                                  : _hasFocus
                                  ? (widget.focusColor ??
                                      const Color(0xFF0058FF))
                                  : widget.borderColor,
                          width: widget.borderWidth,
                        )
                        : null,
                // border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                children: [
                  // File type icon
                  Icon(
                    _getFileIcon(file.extension),
                    size: getResponsiveIconSize(context),
                    color: _getFileIconColor(file.extension),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      file.name,
                      style: TextStyle(
                        fontSize: _getResponsiveFontSize(context) * 0.9,
                        color: Color(0xFF333333),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Navigate icon (opens file in new window)
                  GestureDetector(
                    onTap: () => _openFileInNewWindow(file),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.open_in_new,
                        size: getResponsiveIconSize(context),
                        color: _isHovered ? hoverIconColor : defaultIconColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Eye icon (views the file)
                  GestureDetector(
                    onTap: () => _viewFile(file),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.visibility,
                        size: getResponsiveIconSize(context),
                        color: _isHovered ? hoverIconColor : defaultIconColor,
                        // Colors.grey.shade600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Delete icon (deletes file and returns to default state)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFiles.clear();
                        _uploadState = FileUploadState.defaultState;
                        _uploadProgress = 0.0;
                        _isUploading = false;
                      });
                      if (widget.onClear != null) {
                        widget.onClear!();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.delete_outline,
                        size: getResponsiveIconSize(context),
                        color: _isHovered ? hoverIconColor : defaultIconColor,
                        // Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 3.0,
      ); // Large// Large
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ); // Medium// Medium
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double getResponsiveIconSize(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else if (screenWidth >= 768) {
      return 12.0; // Small
    } else {
      return 10.0; // Extra Small (fallback for very small screens)
    }
  }
}
