{"nsl_hierarchy": {"system_info": {"title": "NSL Complete Hierarchy System", "framework": "GO-LO-NP-Stack-SubNSL BET Structure", "total_organizational_levels": 4, "bet_composition": "GOs + LOs + NP Functions + Input/Output Stacks + Subordinate NSL"}, "M4": {"level_name": "Executive", "node": {"id": "1", "title": "CEO Operations", "type": "Executive Node", "total_bets": 1356, "bet_breakdown": {"gos": 45, "los": 234, "np_functions": 567, "input_output_stacks": 345, "subordinate_nsl": 165}, "children": ["1.1", "1.2", "1.3"], "financial_summary": {"revenue": "$7,000,000", "cost": "$4,256,725", "margin": "39.2%"}}}, "M3": {"level_name": "Departments", "nodes": [{"id": "1.1", "title": "Technology", "type": "Department Node", "parent": "1", "total_bets": 892, "bet_breakdown": {"gos": 28, "los": 145, "np_functions": 372, "input_output_stacks": 235, "subordinate_nsl": 112}, "children": ["1.1.1", "1.1.2", "1.1.3", "1.1.4"], "financial_summary": {"revenue": "$4,200,000", "cost": "$2,400,000", "margin": "42.8%"}, "workflows": [{"go_id": "GO-001", "workflow_name": "Software Development Lifecycle", "local_objectives": [{"lo_id": "LO-001-001", "name": "Requirements Analysis", "np_functions": [{"np_id": "NP-001-001-001", "name": "Gather Business Requirements", "input_stacks": ["INPUT-001-001-001"], "output_stacks": ["OUTPUT-001-001-001"], "subordinate_nsl": ["SUB-001-001-001"]}]}]}]}, {"id": "1.2", "title": "Operations", "type": "Department Node", "parent": "1", "total_bets": 234, "bet_breakdown": {"gos": 12, "los": 56, "np_functions": 98, "input_output_stacks": 52, "subordinate_nsl": 16}, "children": ["1.2.1", "1.2.2", "1.2.3"], "financial_summary": {"revenue": "$1,800,000", "cost": "$1,100,000", "margin": "38.9%"}}, {"id": "1.3", "title": "Finance", "type": "Department Node", "parent": "1", "total_bets": 89, "bet_breakdown": {"gos": 5, "los": 33, "np_functions": 35, "input_output_stacks": 12, "subordinate_nsl": 4}, "children": ["1.3.1", "1.3.2"], "financial_summary": {"revenue": "Internal", "cost": "$168,000", "margin": "Support Function"}}]}, "M2": {"level_name": "Teams", "nodes": [{"id": "1.1.1", "title": "Engineering", "type": "Team Node", "parent": "1.1", "total_bets": 456, "bet_breakdown": {"gos": 18, "los": 89, "np_functions": 189, "input_output_stacks": 115, "subordinate_nsl": 45}, "children": ["1.1.1.1", "*******", "*******", "*******"], "financial_summary": {"revenue": "$2,800,000", "cost": "$1,600,000", "margin": "42.9%"}, "workflows": [{"go_id": "GO-001-001", "workflow_name": "Frontend Development Process", "local_objectives": [{"lo_id": "LO-001-001-001", "name": "UI Component Development", "solution_space": "React component architecture and implementation", "outcome_focus": "Reusable, performant UI components", "np_functions": [{"np_id": "NP-001-001-001-001", "name": "Design Component Structure", "function_type": "create", "agent_type": "Human", "agent_cost": 85.0, "input_stacks": [{"stack_id": "INPUT-001-001-001-001", "entities": [{"entity_name": "Design Requirements", "attributes": [{"name": "component_type", "ui_type": "dropdown", "cost": 1.5}, {"name": "functionality_spec", "ui_type": "textarea", "cost": 2.0}]}]}], "output_stacks": [{"stack_id": "OUTPUT-001-001-001-001", "entities": [{"entity_name": "Component Blueprint", "attributes": [{"name": "component_structure", "ui_type": "text", "cost": 3.0}, {"name": "prop_definitions", "ui_type": "textarea", "cost": 2.5}]}]}], "subordinate_nsl": [{"sub_id": "SUB-001-001-001-001-001", "function_type": "validation", "trigger_condition": "On design completion", "description": "Validate component adheres to design system"}]}]}]}]}, {"id": "1.1.2", "title": "QA & Testing", "type": "Team Node", "parent": "1.1", "total_bets": 145, "bet_breakdown": {"gos": 6, "los": 34, "np_functions": 68, "input_output_stacks": 28, "subordinate_nsl": 9}, "children": ["*******", "*******", "*******", "*******"], "financial_summary": {"revenue": "$850,000", "cost": "$520,000", "margin": "38.8%"}}, {"id": "1.1.3", "title": "Infrastructure", "type": "Team Node", "parent": "1.1", "total_bets": 89, "bet_breakdown": {"gos": 3, "los": 18, "np_functions": 45, "input_output_stacks": 18, "subordinate_nsl": 5}, "children": ["*******", "*******", "*******"], "financial_summary": {"revenue": "$380,000", "cost": "$210,000", "margin": "44.7%"}}, {"id": "1.1.4", "title": "Analytics", "type": "Team Node", "parent": "1.1", "total_bets": 23, "bet_breakdown": {"gos": 1, "los": 4, "np_functions": 12, "input_output_stacks": 5, "subordinate_nsl": 1}, "children": ["*******", "*******"], "financial_summary": {"revenue": "$170,000", "cost": "$70,000", "margin": "58.8%"}}]}, "M1": {"level_name": "Individual Employees", "nodes": [{"id": "1.1.1.1", "title": "<PERSON> - Senior Developer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-001", "total_bets": 67, "bet_breakdown": {"gos": 5, "los": 15, "np_functions": 25, "input_output_stacks": 15, "subordinate_nsl": 7}, "financial_summary": {"annual_salary": "$89,000", "value_output": "$425,000", "efficiency": "90.2%"}, "personal_workflows": [{"go_id": "GO-001-001-001-001", "workflow_name": "Frontend Component Development", "local_objectives": [{"lo_id": "LO-001-001-001-001-001", "name": "React Component Architecture", "solution_space": "Individual component development and optimization", "outcome_focus": "High-quality, reusable React components", "efficiency": 96.7, "np_functions": [{"np_id": "NP-001-001-001-001-001", "name": "Component Development", "function_type": "create", "agent": "<PERSON>", "agent_cost": 45.0, "cycle_time": 4.5, "input_stacks": [{"stack_id": "INPUT-001-001-001-001-001", "entities": [{"entity_name": "Development Requirements", "attributes": [{"name": "component_spec", "ui_type": "textarea", "source": "manual", "cost": 2.0}, {"name": "design_mockup", "ui_type": "file", "source": "digital", "cost": 1.5}]}]}], "output_stacks": [{"stack_id": "OUTPUT-001-001-001-001-001", "entities": [{"entity_name": "React Component", "attributes": [{"name": "component_code", "ui_type": "textarea", "cost": 5.0}, {"name": "prop_types", "ui_type": "text", "cost": 1.0}, {"name": "test_coverage", "ui_type": "number", "cost": 2.0}]}]}], "subordinate_nsl": [{"sub_id": "SUB-001-001-001-001-001-001", "function_type": "validation", "trigger_condition": "On code completion", "description": "ESLint validation and type checking"}, {"sub_id": "SUB-001-001-001-001-001-002", "function_type": "calculation", "trigger_condition": "On test run", "description": "Calculate test coverage percentage"}]}, {"np_id": "NP-001-001-001-001-002", "name": "Component Testing", "function_type": "validate", "agent": "<PERSON>", "agent_cost": 25.0, "cycle_time": 2.0, "input_stacks": [{"stack_id": "INPUT-001-001-001-001-002", "entities": [{"entity_name": "Component Code", "attributes": [{"name": "react_component", "ui_type": "textarea", "cost": 0.5}, {"name": "test_scenarios", "ui_type": "textarea", "cost": 1.5}]}]}], "output_stacks": [{"stack_id": "OUTPUT-001-001-001-001-002", "entities": [{"entity_name": "Test Results", "attributes": [{"name": "test_status", "ui_type": "dropdown", "cost": 1.0}, {"name": "coverage_report", "ui_type": "file", "cost": 2.0}]}]}], "subordinate_nsl": [{"sub_id": "SUB-001-001-001-001-002-001", "function_type": "notification", "trigger_condition": "On test completion", "description": "Notify team of test results"}]}]}, {"lo_id": "LO-001-001-001-001-002", "name": "API Integration Layer", "solution_space": "Service communication implementation", "outcome_focus": "Seamless backend integration", "efficiency": 94.2, "np_functions": [{"np_id": "NP-001-001-001-001-003", "name": "API Client Development", "function_type": "integrate", "agent": "<PERSON>", "agent_cost": 35.0, "cycle_time": 3.0, "input_stacks": [{"stack_id": "INPUT-001-001-001-001-003", "entities": [{"entity_name": "API Specification", "attributes": [{"name": "endpoint_url", "ui_type": "text", "cost": 0.5}, {"name": "request_schema", "ui_type": "textarea", "cost": 2.0}, {"name": "auth_requirements", "ui_type": "dropdown", "cost": 1.5}]}]}], "output_stacks": [{"stack_id": "OUTPUT-001-001-001-001-003", "entities": [{"entity_name": "API Client", "attributes": [{"name": "client_code", "ui_type": "textarea", "cost": 4.0}, {"name": "error_handling", "ui_type": "textarea", "cost": 2.5}]}]}], "subordinate_nsl": [{"sub_id": "SUB-001-001-001-001-003-001", "function_type": "validation", "trigger_condition": "On API call", "description": "Validate request format and authentication"}]}]}]}]}, {"id": "*******", "title": "<PERSON> - Lead Architect", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-002", "total_bets": 89, "bet_breakdown": {"gos": 4, "los": 18, "np_functions": 35, "input_output_stacks": 22, "subordinate_nsl": 10}, "financial_summary": {"annual_salary": "$115,000", "value_output": "$380,000", "efficiency": "87.5%"}}, {"id": "*******", "title": "<PERSON> - Research Engineer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-003", "total_bets": 35, "bet_breakdown": {"gos": 3, "los": 8, "np_functions": 15, "input_output_stacks": 7, "subordinate_nsl": 2}, "financial_summary": {"annual_salary": "$95,000", "value_output": "$85,000", "efficiency": "45.0%"}}, {"id": "*******", "title": "<PERSON> - Platform Engineer", "type": "Individual Employee", "parent": "1.1.1", "employee_id": "EMP-004", "total_bets": 112, "bet_breakdown": {"gos": 4, "los": 22, "np_functions": 45, "input_output_stacks": 28, "subordinate_nsl": 13}, "financial_summary": {"annual_salary": "$105,000", "value_output": "$520,000", "efficiency": "94.1%"}}]}, "bet_aggregation_rules": {"total_bet_calculation": "GOs + LOs + NP_Functions + Input_Output_Stacks + Subordinate_NSL", "rollup_logic": {"M1_to_M2": "Sum all M1 employee BETs within team", "M2_to_M3": "Sum all M2 team BETs within department", "M3_to_M4": "Sum all M3 department BETs within organization"}, "validation": {"M4_total": 1356, "M3_sum": 1215, "calculation_note": "M4 total includes additional executive-level coordination BETs"}}, "performance_metrics": {"M4": {"average_bet_efficiency": "88.5%", "go_completion_rate": "89.2%", "lo_integration_rate": "91.5%"}, "M3": {"technology_efficiency": "91.2%", "operations_efficiency": "84.3%", "finance_efficiency": "76.8%"}, "M2": {"engineering_efficiency": "93.5%", "qa_efficiency": "87.2%", "infrastructure_efficiency": "89.6%", "analytics_efficiency": "92.1%"}, "M1": {"john_efficiency": "90.2%", "sarah_efficiency": "87.5%", "mike_efficiency": "45.0%", "emily_efficiency": "94.1%"}}}}