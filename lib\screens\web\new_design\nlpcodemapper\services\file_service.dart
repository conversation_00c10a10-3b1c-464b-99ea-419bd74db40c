// This file uses conditional exports to provide different implementations
// for different platforms. The correct implementation is chosen automatically
// at compile time.

import 'file_service_io.dart' if (dart.library.html) 'file_service_web.dart';

/// FileService provides platform-agnostic storage capabilities.
/// It uses File API for mobile/desktop platforms and localStorage for web.
class FileService {
  final FileServiceImpl _impl = FileServiceImpl();

  /// Initialize storage
  Future<void> initializeFiles() => _impl.initializeFiles();

  /// Read solutions from storage
  Future<List<Map<String, dynamic>>> readSolutions() => _impl.readSolutions();

  /// Write solutions to storage
  Future<void> writeSolutions(List<Map<String, dynamic>> solutions) => 
      _impl.writeSolutions(solutions);

  /// Read mappings from storage
  Future<List<Map<String, dynamic>>> readMappings() => _impl.readMappings();

  /// Write mappings to storage
  Future<void> writeMappings(List<Map<String, dynamic>> mappings) => 
      _impl.writeMappings(mappings);
}
