import 'base_model.dart';

/// A generic API response model that can be used across all API services
/// This provides a consistent structure for all API responses
class ApiResponse<T> extends BaseModel {
  /// Whether the API call was successful
  final bool success;

  /// The response message (error message if success is false, success message if success is true)
  final String? message;

  /// The main data payload of the response
  final T? data;

  /// Additional metadata or secondary data
  final Map<String, dynamic>? metadata;

  /// HTTP status code (optional)
  final int? statusCode;

  /// Constructor
  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.metadata,
    this.statusCode,
  });

  /// Factory constructor for successful responses
  factory ApiResponse.success({
    T? data,
    String? message,
    Map<String, dynamic>? metadata,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      metadata: metadata,
      statusCode: statusCode,
    );
  }

  /// Factory constructor for error responses
  factory ApiResponse.error({
    required String message,
    T? data,
    Map<String, dynamic>? metadata,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      data: data,
      metadata: metadata,
      statusCode: statusCode,
    );
  }

  /// Factory constructor from JSON
  factory ApiResponse.fromJson(
    Map<String, dynamic> json, {
    T Function(dynamic)? dataFromJson,
  }) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null && dataFromJson != null
          ? dataFromJson(json['data'])
          : json['data'],
      metadata: json['metadata'],
      statusCode: json['statusCode'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data is BaseModel ? (data as BaseModel).toJson() : data,
      'metadata': metadata,
      'statusCode': statusCode,
    };
  }

  @override
  ApiResponse<T> copyWith({
    bool? success,
    String? message,
    T? data,
    Map<String, dynamic>? metadata,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: success ?? this.success,
      message: message ?? this.message,
      data: data ?? this.data,
      metadata: metadata ?? this.metadata,
      statusCode: statusCode ?? this.statusCode,
    );
  }

  /// Check if the response has data
  bool get hasData => data != null;

  /// Check if the response is an error
  bool get isError => !success;

  /// Get data or throw an exception if the response is an error
  T get dataOrThrow {
    if (success && data != null) {
      return data!;
    }
    throw Exception(message ?? 'API call failed');
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data, metadata: $metadata, statusCode: $statusCode)';
  }
}
