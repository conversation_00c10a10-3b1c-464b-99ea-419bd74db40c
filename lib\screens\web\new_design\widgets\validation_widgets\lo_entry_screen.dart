import 'package:flutter/material.dart';
import 'package:nsl/models/workflow/workflow_manual_response_model.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:provider/provider.dart';

class LoEntryScreen extends StatelessWidget {
  final Widget textFieldWidget;
  LoEntryScreen({super.key, required this.textFieldWidget});

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        final Go workflowDetails =
            provider.extractedWorkflowData?.first['workFlowDetails'];
        final List<LocalObjectivesList>? list =
            workflowDetails.localObjectivesList;
        return Row(
          children: [
            (list?.isEmpty ?? true)
                ? Container()
                : Expanded(
                    child: ListView(
                      // physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      padding: EdgeInsets.only(left: AppSpacing.xs),
                      children: list!
                          .map(
                            (e) => Row(
                              children: [
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      // provider.setLocalObjective = e;
                                      provider.setLocalObjectiveIndex =
                                          list.indexOf(e);
                                    },
                                    child: Container(
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                                AppSpacing.xxs),
                                            color:
                                                provider.selectedLocalObjectiveIndex ==
                                                        list.indexOf(e)
                                                    ? AppColors.primaryBlue
                                                        .withValues(alpha: 0.1)
                                                    : Colors.transparent),
                                        padding: EdgeInsets.all(AppSpacing.xs),
                                        margin: EdgeInsets.only(
                                            bottom: AppSpacing.xs),
                                        child: Text(
                                          e.loName ?? '',
                                          style: FontManager.getCustomStyle(),
                                        )),
                                  ),
                                ),
                                if (e.isCompleted ?? false)
                                  Transform.translate(
                                      offset: Offset(0, -5),
                                      child: buildCheckIconComplete()),
                              ],
                            ),
                          )
                          .toList(),
                    ),
                  ),
            Expanded(flex: 4, child: textFieldWidget)
          ],
        );
      },
    );
  }
}

Widget buildCheckIconComplete() {
  return Container(
    decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(AppSpacing.radius100),
        border: Border.all(color: AppColors.green)),
    child: Icon(
      Icons.check,
      size: 12,
      color: AppColors.white,
    ),
  );
}
