import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../models/nsl_hierarchy_model.dart';
import '../../../widgets/resizable_panel.dart';
import 'widgets/nsl_hierarchy_chart.dart';
import 'widgets/nsl_details_panel.dart';

class TreeHierarchyModel extends StatefulWidget {
  const TreeHierarchyModel({super.key});

  @override
  State<TreeHierarchyModel> createState() => _TreeHierarchyModelState();
}

class _TreeHierarchyModelState extends State<TreeHierarchyModel> {
  List<NSLHierarchyData> _nslNodes = [];
  NSLNode? _rootNode;
  NSLNode? _filteredRootNode;
  NSLHierarchyData? _selectedNode;
  bool _showSidePanel = false;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _searchQuery = '';

  // Side panel dimensions
  double _sidePanelWidth = 400.0;
  final double _minSidePanelWidth = 300.0;
  final double _maxSidePanelWidth = 600.0;

  @override
  void initState() {
    super.initState();
    _loadNSLData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.trim();
      _buildFilteredTree();
    });
  }

  Future<void> _loadNSLData() async {
    try {
      // Load JSON data from assets
      final String jsonString = await rootBundle.loadString('assets/data/nsl_hierarchy.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);

      // Parse the NSL hierarchy structure
      _rootNode = NSLHierarchyBuilder.buildHierarchyFromNSLData(jsonData);
      
      if (_rootNode != null) {
        _nslNodes = _rootNode!.originalData.getAllNodes(); // Flatten for search
      }

      // Build filtered tree
      _buildFilteredTree();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading NSL hierarchy data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _buildFilteredTree() {
    if (_searchQuery.isEmpty) {
      _filteredRootNode = _rootNode;
    } else {
      // Find node by title or ID in the flattened list
      final foundNodes = _nslNodes
          .where((node) =>
              node.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              node.id.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();

      if (foundNodes.isNotEmpty) {
        // For nested structure, we need to find the node in the tree and show its subtree
        _filteredRootNode = _findNodeInTree(_rootNode, foundNodes.first.id);
      } else {
        _filteredRootNode = null;
      }
    }
  }

  // Helper method to find a node in the tree and return its subtree
  NSLNode? _findNodeInTree(NSLNode? node, String nodeId) {
    if (node == null) return null;
    
    if (node.id == nodeId) {
      return node;
    }
    
    for (var child in node.children) {
      final found = _findNodeInTree(child, nodeId);
      if (found != null) {
        return found;
      }
    }
    
    return null;
  }

  void _onNodeTap(NSLNode node) {
    setState(() {
      _selectedNode = node.originalData;
      _showSidePanel = true;
    });
  }

  void _hideSidePanel() {
    setState(() {
      _showSidePanel = false;
      _selectedNode = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          // Main content area
          Expanded(
            flex: _showSidePanel ? 3 : 1,
            child: Column(
              children: [
                // Header with search
                _buildHeader(),

                // Tree hierarchy chart
                Expanded(
                  child: _filteredRootNode != null
                      ? NSLHierarchyChart(
                          rootNode: _filteredRootNode!,
                          onNodeTap: _onNodeTap,
                        )
                      : _buildEmptyState(),
                ),
              ],
            ),
          ),

          // Side panel for NSL node details
          if (_showSidePanel && _selectedNode != null)
            ResizablePanel(
              width: _sidePanelWidth,
              minWidth: _minSidePanelWidth,
              maxWidth: _maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                setState(() {
                  _sidePanelWidth = newWidth;
                });
              },
              child: NSLDetailsPanel(
                nslData: _selectedNode!,
                onClose: _hideSidePanel,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Title
          Text(
            'NSL Hierarchy',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),

          const Spacer(),

          // Search field
          Container(
            width: 300,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by title or ID...',
                hintStyle: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.grey.shade500,
                          size: 20,
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty
                ? 'No NSL hierarchy data available'
                : 'No nodes found for "$_searchQuery"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                _searchController.clear();
              },
              child: Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
