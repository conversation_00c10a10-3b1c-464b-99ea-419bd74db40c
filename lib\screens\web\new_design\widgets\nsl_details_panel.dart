import 'package:flutter/material.dart';
import '../../../../models/nsl_hierarchy_model.dart';

class NSLDetailsPanel extends StatelessWidget {
  final NSLHierarchyData nslData;
  final VoidCallback onClose;

  const NSLDetailsPanel({
    super.key,
    required this.nslData,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          Text(
            'NSL Node Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              Icons.close,
              color: Colors.grey.shade600,
              size: 20,
            ),
            onPressed: onClose,
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNodeHeader(),
          const SizedBox(height: 24),
          // _buildSection('Basic Information', [
          //   _buildInfoRow('Node ID', nslData.id),
          //   _buildInfoRow('Type', nslData.type),
          //   _buildInfoRow('Level', '${nslData.level} (${nslData.levelName})'),
          //   _buildInfoRow('Total BETs', nslData.totalBets.toString()),
          // ]),
          // const SizedBox(height: 24),
          _buildBetBreakdownSection(),
          const SizedBox(height: 24),
          _buildFinancialSummarySection(),
          if (nslData.employeeId != null) ...[
            const SizedBox(height: 24),
            _buildSection('Employee Information', [
              _buildInfoRow('Employee ID', nslData.employeeId!),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildNodeHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: NSLNode.getLevelColor(nslData.level),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                nslData.level,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nslData.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  nslData.type,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontFamily: 'TiemposText',
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: NSLNode.getLevelColor(nslData.level).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${nslData.level} - ${nslData.levelName}',
                    style: TextStyle(
                      fontSize: 12,
                      color: NSLNode.getLevelColor(nslData.level),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBetBreakdownSection() {
    return _buildSection('BET Breakdown', [
      _buildInfoRow('GOs (Global Objectives)', nslData.betBreakdown.gos.toString()),
      _buildInfoRow('LOs (Local Objectives)', nslData.betBreakdown.los.toString()),
      _buildInfoRow('NP Functions', nslData.betBreakdown.npFunctions.toString()),
      _buildInfoRow('Input/Output Stacks', nslData.betBreakdown.inputOutputStacks.toString()),
      _buildInfoRow('Subordinate NSL', nslData.betBreakdown.subordinateNsl.toString()),
      const SizedBox(height: 8),
      Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calculate,
              color: Colors.blue.shade700,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Total BETs: ${nslData.totalBets}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade700,
                fontFamily: 'TiemposText',
              ),
            ),
          ],
        ),
      ),
    ]);
  }

  Widget _buildFinancialSummarySection() {
    return _buildSection('Financial Summary', [
      _buildInfoRow('Revenue', nslData.financialSummary.revenue),
      _buildInfoRow('Cost', nslData.financialSummary.cost),
      _buildInfoRow('Margin/Efficiency', nslData.financialSummary.margin),
    ]);
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: 'TiemposText',
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
