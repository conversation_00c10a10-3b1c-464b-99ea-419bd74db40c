import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/screens/web/new_design/widgets/validation_widgets/workflow_table.dart';

/// A reusable workflow LO with workflow structure widget that displays both workflow structure and LO data.
///
/// This widget shows the existing workflow structure on the left and workflow LO data on the right,
/// providing a comprehensive view of the workflow and its local objectives.
class WorkflowLoWithWorkflowStructure extends StatelessWidget {
  /// The manual creation provider containing workflow and LO data
  final ManualCreationProvider provider;

  /// Callback when a workflow node is selected
  final VoidCallback? onNodeSelected;

  const WorkflowLoWithWorkflowStructure({
    super.key,
    required this.provider,
    this.onNodeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Existing workflow structure
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xffD0D0D0), width: 1),
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                  ),
                  child: Text(
                    'Existing Workflow Structure',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
                // Workflow content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: WorkflowTable(
                      provider: provider,
                      onNodeSelected: onNodeSelected,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Right side would be WorkflowLO table if needed
        // For now, we're just showing the workflow structure as requested
      ],
    );
  }
}
