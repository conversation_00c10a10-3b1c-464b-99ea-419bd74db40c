import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../../../models/agent_tree_model.dart';
import '../../../../providers/manual_creation_provider.dart';
import 'chat_widgets/user_profile_card.dart';

class AgentHierarchyChart extends StatefulWidget {
  final ManualCreationProvider provider;
  final Function(AgentNode)? onNodeTap;
  final double horizontalSpacing;
  final double verticalSpacing;
  final double lineThickness;
  final Color lineColor;

  const AgentHierarchyChart({
    super.key,
    required this.provider,
    this.onNodeTap,
    this.horizontalSpacing = 120.0,
    this.verticalSpacing = 80.0,
    this.lineThickness = 2.0,
    this.lineColor = const Color(0xFF4ECDC4),
  });

  @override
  State<AgentHierarchyChart> createState() => _AgentHierarchyChartState();
}

class _AgentHierarchyChartState extends State<AgentHierarchyChart> {
  late AgentNode? _rootNode;
  AgentNode? _selectedNode;
  List<String> _selectedPathIds = [];
  final TransformationController _transformationController = TransformationController();
  final Map<String, double> _subtreeWidths = {};

  static const double _cardWidth = 240.0;
  static const double _cardHeight = 120.0;

  @override
  void initState() {
    super.initState();
    _buildHierarchy();
    
   WidgetsBinding.instance.addPostFrameCallback((_) {
  _transformationController.value = Matrix4.identity()
    ..scale(0.8, 0.8)  
    ..translate(25.0, 25.0);  // Adjust positioning for the larger scale
});
  }

  @override
  void didUpdateWidget(AgentHierarchyChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Rebuild hierarchy when provider data changes
    if (oldWidget.provider != widget.provider) {
      _buildHierarchy();
    }
  }

  void _buildHierarchy() {
    if (widget.provider.validationResult?.parsedRoles != null) {
      final roles = widget.provider.validationResult!.parsedRoles!;
      print('🌳 Building hierarchy with ${roles.length} roles:');
      roles.forEach((key, value) {
        print('🌳 Role: $key -> Name: ${value.name}, Department: ${value.department}, ParentRole: ${value.parentRole}, ReportsTo: ${value.reportsTo}');
      });
      
      _rootNode = AgentHierarchyBuilder.buildHierarchy(roles);
      
      if (_rootNode != null) {
        print('🌳 Root node created: ${_rootNode!.name} with ${_rootNode!.children.length} children');
        _printNodeTree(_rootNode!, 0);
        _calculateAllSubtreeWidths(_rootNode!);
      } else {
        print('🌳 No root node created');
      }
    } else {
      print('🌳 No parsed roles available');
    }
  }

  void _printNodeTree(AgentNode node, int depth) {
    final indent = '  ' * depth;
    print('🌳 $indent${node.name} (${node.role}) - Children: ${node.children.length}');
    for (final child in node.children) {
      _printNodeTree(child, depth + 1);
    }
  }

  void _calculateAllSubtreeWidths(AgentNode node) {
    _getSubtreeWidth(node);
  }

  double _getSubtreeWidth(AgentNode node) {
    if (_subtreeWidths.containsKey(node.id)) {
      return _subtreeWidths[node.id]!;
    }

    double width;
    if (!node.isExpanded || node.children.isEmpty) {
      width = _cardWidth;
    } else {
      double childrenCombinedWidth = 0;
      for (int i = 0; i < node.children.length; i++) {
        childrenCombinedWidth += _getSubtreeWidth(node.children[i]);
      }
      width = childrenCombinedWidth + (node.children.length - 1) * widget.horizontalSpacing;
      if (width < _cardWidth) {
        width = _cardWidth;
      }
    }
    _subtreeWidths[node.id] = width;
    return width;
  }

  @override
  Widget build(BuildContext context) {
    if (_rootNode == null) {
      return Center(
        child: Text(
          'No agent hierarchy data available',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
            fontFamily: 'TiemposText',
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          InteractiveViewer(
            transformationController: _transformationController,
            constrained: false,
         //   boundaryMargin: const EdgeInsets.all(200.0),
            minScale: 0.1,
            maxScale: 2.0,
            child: Container(
              width: 5000,
              height: 3000,
              color:  Colors.white,
             //  Color(0xFFF5F7FA),
              child: _buildOrganizationTree(_rootNode!),
            ),
          ),
          _buildZoomControls(),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 1,
            )
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.add, color: Colors.black54),
              onPressed: () => _zoom(1.2),
              tooltip: 'Zoom In',
            ),
            SizedBox(
              height: 30,
              width: 2,
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(height: 30, width: 1.5, color: Colors.grey.shade400),
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: const Color(0xFFA5D6A7),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.remove, color: Colors.black54),
              onPressed: () => _zoom(0.8),
              tooltip: 'Zoom Out',
            ),
          ],
        ),
      ),
    );
  }

  void _zoom(double zoomFactor) {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    double newScale = currentScale * zoomFactor;
    newScale = newScale.clamp(0.1, 2.0);
    final relativeScaleFactor = newScale / currentScale;
    final Matrix4 newMatrix = _transformationController.value.clone()
      ..scale(relativeScaleFactor, relativeScaleFactor);
    _transformationController.value = newMatrix;
  }

  Widget _buildOrganizationTree(AgentNode rootNode) {
    final nodesInfo = _calculateNodePositions(rootNode);
    final nodeWidgets = _buildAllNodes(rootNode, nodesInfo);
    final expandCollapseButtons = _generateExpandCollapseButtonWidgets(rootNode, nodesInfo);

    return Stack(
      children: [
        CustomPaint(
          painter: AgentConnectionsPainter(
            rootNode: _rootNode!,
            nodePositions: nodesInfo,
            selectedPathIds: _selectedPathIds,
            defaultLineColor: widget.lineColor,
            lineThickness: widget.lineThickness,
            verticalSpacing: widget.verticalSpacing,
            highlightColor: Colors.deepOrangeAccent,
          ),
          child: Container(),
        ),
        ...nodeWidgets,
        ...expandCollapseButtons,
      ],
    );
  }

  List<Widget> _generateExpandCollapseButtonWidgets(AgentNode currentNode, Map<String, AgentNodePosition> nodesInfo) {
    List<Widget> buttons = [];
    
    void dfs(AgentNode node) {
      if (node.children.isNotEmpty) {
        final positionInfo = nodesInfo[node.id];
        if (positionInfo != null) {
          buttons.add(_buildSingleExpandCollapseButton(node, positionInfo));
        }
      }
      for (var child in node.children) {
        dfs(child);
      }
    }

    dfs(currentNode);
    return buttons;
  }

  Widget _buildSingleExpandCollapseButton(AgentNode node, AgentNodePosition positionInfo) {
    final bool isExpanded = node.isExpanded;
    final String buttonText = isExpanded ? 'Collapse' : '+${node.children.length}';
    final IconData buttonIcon = isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down;
    
    final double buttonWidth = isExpanded ? 100 : 60;
    final double buttonHeight = 28;
    final double spacingBelowCard = 8.0;

    final buttonTop = positionInfo.bottomCenter.dy + spacingBelowCard;
    final buttonLeft = positionInfo.bottomCenter.dx - (buttonWidth / 2);

    return Positioned(
      left: buttonLeft,
      top: buttonTop,
      child: GestureDetector(
        onTap: () {
          final newRoot = _toggleNodeExpansionRecursive(_rootNode!, node.id);
          _subtreeWidths.clear();
          _calculateAllSubtreeWidths(newRoot);
          setState(() {
            _rootNode = newRoot;
          });
        },
        child: Container(
          width: buttonWidth,
          height: buttonHeight,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14),
            border: Border.all(color: Colors.grey.shade300, width: 1.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                buttonText,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.blueGrey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isExpanded) const SizedBox(width: 4),
              Icon(
                buttonIcon,
                size: 16,
                color: Colors.blueGrey.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildAllNodes(AgentNode node, Map<String, AgentNodePosition> nodesInfo) {
    final List<Widget> allWidgets = [];
    final position = nodesInfo[node.id];
    
    if (position != null) {
      allWidgets.add(
        Positioned(
          left: position.x,
          top: position.y,
          child: _buildNodeWidget(node),
        ),
      );
      
      if (node.isExpanded) {
        for (final child in node.children) {
          allWidgets.addAll(_buildAllNodes(child, nodesInfo));
        }
      }
    }
    
    return allWidgets;
  }

  Widget _buildNodeWidget(AgentNode node) {
    return AgentCard(
      node: node,
      isSelected: _selectedNode?.id == node.id,
      isHighlightedInPath: _selectedPathIds.contains(node.id),
      onTap: () {
        setState(() {
          if (_selectedNode?.id == node.id) {
            _selectedNode = null;
            _selectedPathIds = [];
          } else {
            _selectedNode = node;
            _selectedPathIds = _getPathToNode(node.id);
          }
        });
        widget.onNodeTap?.call(node);
      },
    );
  }

  AgentNode _toggleNodeExpansionRecursive(AgentNode currentNode, String targetNodeId) {
    if (currentNode.id == targetNodeId) {
      return currentNode.copyWith(isExpanded: !currentNode.isExpanded);
    }

    final List<AgentNode> updatedChildren = [];
    bool childChanged = false;
    for (final child in currentNode.children) {
      final updatedChild = _toggleNodeExpansionRecursive(child, targetNodeId);
      if (updatedChild != child) {
        childChanged = true;
      }
      updatedChildren.add(updatedChild);
    }

    if (childChanged) {
      return currentNode.copyWith(children: updatedChildren);
    }
    return currentNode;
  }

  Map<String, AgentNodePosition> _calculateNodePositions(AgentNode rootNode) {
    final Map<String, AgentNodePosition> positions = {};
    
    const rootX = 0.0;
    const rootY = 0.0;
    
    final rootSubtreeWidth = _subtreeWidths[rootNode.id] ?? _cardWidth;
    final rootCardX = rootX + (rootSubtreeWidth / 2) - (_cardWidth / 2);

    positions[rootNode.id] = AgentNodePosition(
      id: rootNode.id,
      x: rootCardX,
      y: rootY,
      width: _cardWidth,
      height: _cardHeight,
    );
    
    _calculateChildPositions(rootNode, positions, rootCardX, rootY, _cardWidth, _cardHeight);
    
    return positions;
  }

  void _calculateChildPositions(
    AgentNode parent,
    Map<String, AgentNodePosition> positions,
    double parentCardX,
    double parentCardY,
    double parentCardWidth,
    double parentCardHeight,
  ) {
    if (!parent.isExpanded || parent.children.isEmpty) return;
    
    final numberOfChildren = parent.children.length;
    final childY = parentCardY + parentCardHeight + widget.verticalSpacing;

    double totalChildrenBlockWidth = 0;
    for (final child in parent.children) {
      totalChildrenBlockWidth += (_subtreeWidths[child.id] ?? _cardWidth);
    }
    if (numberOfChildren > 1) {
      totalChildrenBlockWidth += (numberOfChildren - 1) * widget.horizontalSpacing;
    }
    
    final startXForChildrenBlock = parentCardX + (parentCardWidth / 2) - (totalChildrenBlockWidth / 2);
    double currentXOffset = startXForChildrenBlock;
    
    for (int i = 0; i < numberOfChildren; i++) {
      final child = parent.children[i];
      final childSubtreeWidth = _subtreeWidths[child.id] ?? _cardWidth;
      
      final childCardX = currentXOffset + (childSubtreeWidth / 2) - (_cardWidth / 2);
      
      positions[child.id] = AgentNodePosition(
        id: child.id,
        x: childCardX,
        y: childY,
        width: _cardWidth,
        height: _cardHeight,
        parentId: parent.id,
      );
      
      _calculateChildPositions(
        child,
        positions,
        childCardX,
        childY,
        _cardWidth,
        _cardHeight,
      );
      
      currentXOffset += childSubtreeWidth + widget.horizontalSpacing;
    }
  }

  List<String> _getPathToNode(String targetNodeId) {
    List<String> currentPath = [];
    bool findPathRecursive(AgentNode currentNode) {
      currentPath.add(currentNode.id);
      if (currentNode.id == targetNodeId) {
        return true;
      }
      for (final child in currentNode.children) {
        if (findPathRecursive(child)) {
          return true;
        }
      }
      currentPath.removeLast();
      return false;
    }

    if (_rootNode != null) {
      findPathRecursive(_rootNode!);
    }
    return currentPath;
  }
}

class AgentNodePosition {
  final String id;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? parentId;
  
  AgentNodePosition({
    required this.id,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.parentId,
  });
  
  Offset get bottomCenter => Offset(x + width / 2, y + height);
  Offset get topCenter => Offset(x + width / 2, y);
}

class AgentCard extends StatelessWidget {
  final AgentNode node;
  final bool isSelected;
  final bool isHighlightedInPath;
  final VoidCallback onTap;

  const AgentCard({
    super.key,
    required this.node,
    required this.isSelected,
    required this.isHighlightedInPath,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      child: Tooltip(
        richMessage: WidgetSpan(
          child: UserProfileCard(
            id: node.id,
            version: '1.0',
            displayName: node.name,
            createdBy: 'System',
            createdDate: _formatDate(DateTime.now()),
            modifiedBy: 'System',
            modifiedDate: _formatDate(DateTime.now()),
            roleTitle: node.role,
            roleDescription: _buildRoleDescription(node),
            width: MediaQuery.of(context).size.width / 3.5,
          ),
        ),
        preferBelow: true,
        verticalOffset: 20,
        padding: EdgeInsets.zero,
        margin: EdgeInsets.zero,
        showDuration: Duration(seconds: 10),
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [],
        ),
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            width: 240,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                    ? Colors.blue
                    : isHighlightedInPath
                        ? Colors.orange.shade300
                        : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and initials
                  Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: node.departmentColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Center(
                          child: Text(
                            _getInitials(node.name),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              node.name,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'TiemposText',
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              node.role,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontFamily: 'TiemposText',
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Department tag
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: node.departmentColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      node.department,
                      style: TextStyle(
                        fontSize: 10,
                        color: node.departmentColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Additional info
                  if (node.organizationalLevel != null)
                    Text(
                      'Level: ${node.organizationalLevel}',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.shade500,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Helper method to build role description for tooltip
  String _buildRoleDescription(AgentNode node) {
    List<String> descriptions = [];
    
    descriptions.add('Department: ${node.department}');
    
    if (node.reportsTo != null && node.reportsTo!.isNotEmpty) {
      descriptions.add('Reports to: ${node.reportsTo}');
    }
    
    if (node.parentRole != null && node.parentRole!.isNotEmpty) {
      descriptions.add('Inherits: ${node.parentRole}');
    }
    
    if (node.organizationalLevel != null) {
      descriptions.add('Level: ${node.organizationalLevel}');
    }
    
    if (node.team != null && node.team!.isNotEmpty) {
      descriptions.add('Team: ${node.team}');
    }
    
    return descriptions.join(', ');
  }

  /// Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _getInitials(String name) {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0].substring(0, math.min(2, parts[0].length)).toUpperCase();
    }
    return 'AG';
  }
}

class AgentConnectionsPainter extends CustomPainter {
  final AgentNode rootNode;
  final Map<String, AgentNodePosition> nodePositions;
  final List<String> selectedPathIds;
  final Color defaultLineColor;
  final double lineThickness;
  final double verticalSpacing;
  final Color highlightColor;

  AgentConnectionsPainter({
    required this.rootNode,
    required this.nodePositions,
    required this.selectedPathIds,
    required this.defaultLineColor,
    this.lineThickness = 2.0,
    this.verticalSpacing = 80.0,
    this.highlightColor = Colors.deepOrangeAccent,
  });

  AgentNode? _findNodeById(AgentNode currentNode, String id) {
    if (currentNode.id == id) return currentNode;
    for (final child in currentNode.children) {
      final found = _findNodeById(child, id);
      if (found != null) return found;
    }
    return null;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = lineThickness
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    
    for (final position in nodePositions.values) {
      if (position.parentId != null) {
        final parentPosition = nodePositions[position.parentId!];
        if (parentPosition != null) {
          final parentNode = _findNodeById(rootNode, position.parentId!);
          
          bool isPathHighlighted = selectedPathIds.contains(position.id) && 
                                 selectedPathIds.contains(position.parentId!);

          paint.color = isPathHighlighted 
              ? highlightColor 
              : (parentNode?.departmentColor ?? defaultLineColor);
          paint.strokeWidth = isPathHighlighted ? lineThickness * 1.8 : lineThickness;
          
          _drawConnection(canvas, paint, parentPosition, position);
        }
      }
    }
  }
  
  void _drawConnection(Canvas canvas, Paint paint, AgentNodePosition parent, AgentNodePosition child) {
    final parentBottomCenter = parent.bottomCenter;
    final childTopCenter = child.topCenter;
    
    final midY = parentBottomCenter.dy + (childTopCenter.dy - parentBottomCenter.dy) / 2;
    
    if (parentBottomCenter.dx == childTopCenter.dx) {
      canvas.drawLine(parentBottomCenter, childTopCenter, paint);
    } else {
      canvas.drawLine(parentBottomCenter, Offset(parentBottomCenter.dx, midY), paint);
      canvas.drawLine(Offset(parentBottomCenter.dx, midY), Offset(childTopCenter.dx, midY), paint);
      canvas.drawLine(Offset(childTopCenter.dx, midY), childTopCenter, paint);
    }
    
    _drawArrowhead(canvas, paint, childTopCenter, math.pi / 2);
  }

  void _drawArrowhead(Canvas canvas, Paint paint, Offset point, double angle) {
    const double arrowSize = 8.0;
    const double arrowAngle = math.pi / 6;

    final Path path = Path();
    path.moveTo(point.dx, point.dy);
    path.lineTo(
      point.dx - arrowSize * math.cos(angle - arrowAngle),
      point.dy - arrowSize * math.sin(angle - arrowAngle),
    );
    path.moveTo(point.dx, point.dy);
    path.lineTo(
      point.dx - arrowSize * math.cos(angle + arrowAngle),
      point.dy - arrowSize * math.sin(angle + arrowAngle),
    );
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(AgentConnectionsPainter oldDelegate) =>
      oldDelegate.nodePositions != nodePositions ||
      oldDelegate.rootNode != rootNode ||
      oldDelegate.selectedPathIds != selectedPathIds ||
      oldDelegate.defaultLineColor != defaultLineColor ||
      oldDelegate.highlightColor != highlightColor ||
      oldDelegate.lineThickness != lineThickness;
}
