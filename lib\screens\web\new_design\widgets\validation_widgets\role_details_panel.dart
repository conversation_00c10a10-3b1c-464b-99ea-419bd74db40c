import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:nsl/models/agent_manual_response_model.dart';
import '../../../../../models/role_info.dart';
import '../../../../../theme/spacing.dart';

/// A reusable role details panel widget that displays detailed information about a selected role.
///
/// This widget shows role information including use cases and permissions in a structured format
/// with navigation shortcuts and interactive elements.
class RoleDetailsPanel extends StatefulWidget {
  /// The role information to display
  final RoleInfo role;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Chat controller for interactive elements
  final TextEditingController? chatController;

  /// Callback when a message is sent
  final VoidCallback? onSendMessage;

  /// Configuration flag to show legacy sections (Use Cases/Permissions) vs new sections (CR/KPI/DA)
  final bool showLegacySections;

  /// List of users to display in the Users section
  final List<User>? users;

  const RoleDetailsPanel({
    super.key,
    required this.role,
    this.onClose,
    this.chatController,
    this.onSendMessage,
    this.showLegacySections = false,
    this.users,
  });

  @override
  State<RoleDetailsPanel> createState() => _RoleDetailsPanelState();
}

class _RoleDetailsPanelState extends State<RoleDetailsPanel> {
  // Track expanded state for each user card
  Set<String> expandedUserIds = <String>{};

  // Track active section for navigation styling
  String? activeSectionId;

  // Global keys for sections - moved to class level to persist across builds
  late Map<String, GlobalKey> roleSectionKeys;

  // ScrollController for the content area
  late ScrollController _scrollController;

  // Bottom spacer height for better scrolling
  double _bottomSpacerHeight = 500;
  VisibilityInfo useCasesVi = VisibilityInfo(key: Key('useCasesVi'));
  VisibilityInfo permissionsVi = VisibilityInfo(key: Key('permissionsVi'));
  VisibilityInfo coreResponsibilitiesVi =
      VisibilityInfo(key: Key('coreResponsibilitiesVi'));
  VisibilityInfo kpisVi = VisibilityInfo(key: Key('kpisVi'));
  VisibilityInfo decisionAuthorityVi =
      VisibilityInfo(key: Key('decisionAuthorityVi'));
  VisibilityInfo usersVi = VisibilityInfo(key: Key('usersVi'));
  // VisibilityInfo objects for each section
  Map<String, VisibilityInfo> sectionVisibilityInfo = {};

  @override
  void initState() {
    super.initState();

    // Initialize scroll controller
    _scrollController = ScrollController();

    // Initialize section keys based on mode
    roleSectionKeys = widget.showLegacySections
        ? {
            'useCases': GlobalKey(),
            'permissions': GlobalKey(),
            'users': GlobalKey(),
          }
        : {
            'coreResponsibilities': GlobalKey(),
            'kpis': GlobalKey(),
            'decisionAuthority': GlobalKey(),
            'users': GlobalKey(),
          };

    // Add scroll listener for active section detection
    _scrollController.addListener(_onScroll);

    // Initialize the first section as active
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        activeSectionId =
            widget.showLegacySections ? 'useCases' : 'coreResponsibilities';
      });
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for active section detection
  void _onScroll() {
    if (widget.showLegacySections) {
      if (useCasesVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'useCases';
        });
      } else if (permissionsVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'permissions';
        });
      }
    } else {
      if (coreResponsibilitiesVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'coreResponsibilities';
        });
      } else if (kpisVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'kpis';
        });
      } else if (decisionAuthorityVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'decisionAuthority';
        });
      } else if (usersVi.visibleFraction > 1) {
        setState(() {
          activeSectionId = 'users';
        });
      }
    }
    // _detectActiveSection();
  }

  @override
  Widget build(BuildContext context) {
    void scrollToRoleSection(String sectionId) {
      if (roleSectionKeys.containsKey(sectionId)) {
        final key = roleSectionKeys[sectionId];

        if (key?.currentContext != null) {
          // Update active section immediately
          setState(() {
            activeSectionId = sectionId;
          });

          // Update bottom spacer height for better scrolling
          setState(() {
            final sectionKeys = widget.showLegacySections
                ? ['useCases', 'permissions', 'users']
                : [
                    'coreResponsibilities',
                    'kpis',
                    'decisionAuthority',
                    'users'
                  ];
            final sectionIndex = sectionKeys.indexOf(sectionId);

            // Calculate required bottom padding based on section position
            if (sectionIndex >= (sectionKeys.length - 2)) {
              // Last 2 sections need more space
              _bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              _bottomSpacerHeight = 500;
            }
          });

          // Scroll to the section
          Future.delayed(Duration(milliseconds: 50), () {
            if (_scrollController.hasClients) {
              Scrollable.ensureVisible(
                key!.currentContext!,
                alignment: 0.0,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xff9B9B9B).withValues(alpha: 0.14),
            blurRadius: 20,
            offset: Offset(-3, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with role title and close button
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(width: 30),
                      CircleAvatar(
                        backgroundColor: Color(0xffE6F7FF),
                        radius: 10,
                        child: Icon(
                          Icons.person_outline,
                          color: Color(0xff1890FF),
                          size: 16,
                        ),
                      ),
                      SizedBox(width: AppSpacing.xxs),
                      Expanded(
                        child: Text(
                          widget.role.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            fontFamily: "TiemposText",
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.chat, color: Colors.black, size: 16),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                  onPressed: () {},
                ),
                const SizedBox(
                  width: AppSpacing.xxs,
                ),
                IconButton(
                  icon: SvgPicture.asset(
                    'assets/images/chat/toggle_open_close.svg',
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade700,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: widget.onClose,
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ),

          // Content area with UC and PR labels
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: AppSpacing.xxs),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left column for navigation labels
                  Container(
                    width: 32,
                    padding: EdgeInsets.only(top: 1),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: widget.showLegacySections
                          ? [
                              // UC label for Use Cases section
                              Padding(
                                padding: EdgeInsets.only(left: 8, top: 16),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () =>
                                        scrollToRoleSection('useCases'),
                                    child: Text(
                                      'UC',
                                      style: TextStyle(
                                          fontWeight:
                                              activeSectionId == 'useCases'
                                                  ? FontWeight.bold
                                                  : FontWeight.w400,
                                          fontSize: 10,
                                          fontFamily: "TiemposText",
                                          color: activeSectionId == 'useCases'
                                              ? Colors.blue.shade700
                                              : Colors.black,
                                          decoration: TextDecoration.underline),
                                    ),
                                  ),
                                ),
                              ),
                              // Space to align PR with Permissions section
                              SizedBox(height: 8),
                              // PR label for Permissions section
                              Padding(
                                padding: EdgeInsets.only(left: 8),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () =>
                                        scrollToRoleSection('permissions'),
                                    child: Text(
                                      'PR',
                                      style: TextStyle(
                                          fontWeight:
                                              activeSectionId == 'permissions'
                                                  ? FontWeight.bold
                                                  : FontWeight.w400,
                                          fontSize: 10,
                                          fontFamily: "TiemposText",
                                          color:
                                              activeSectionId == 'permissions'
                                                  ? Colors.blue.shade700
                                                  : Colors.black,
                                          decoration: TextDecoration.underline),
                                    ),
                                  ),
                                ),
                              ),
                              // Space to align U with Users section
                              SizedBox(height: 10),
                            ]
                          : [
                              // CR label for Core Responsibilities section
                              Padding(
                                padding: EdgeInsets.only(left: 8, top: 16),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToRoleSection(
                                        'coreResponsibilities'),
                                    child: Text(
                                      'CR',
                                      style: TextStyle(
                                          fontWeight: activeSectionId ==
                                                  'coreResponsibilities'
                                              ? FontWeight.bold
                                              : FontWeight.w400,
                                          fontSize: 10,
                                          fontFamily: "TiemposText",
                                          color: activeSectionId ==
                                                  'coreResponsibilities'
                                              ? Colors.blue.shade700
                                              : Colors.black,
                                          decoration: TextDecoration.underline),
                                    ),
                                  ),
                                ),
                              ),
                              // Space to align KPI with KPIs section
                              SizedBox(height: 8),
                              // KPI label for KPIs section
                              Padding(
                                padding: EdgeInsets.only(left: 8),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToRoleSection('kpis'),
                                    child: Text(
                                      'KPI',
                                      style: TextStyle(
                                          fontWeight: activeSectionId == 'kpis'
                                              ? FontWeight.bold
                                              : FontWeight.w400,
                                          fontSize: 10,
                                          fontFamily: "TiemposText",
                                          color: activeSectionId == 'kpis'
                                              ? Colors.blue.shade700
                                              : Colors.black,
                                          decoration: TextDecoration.underline),
                                    ),
                                  ),
                                ),
                              ),
                              // Space to align DA with Decision Authority section
                              SizedBox(height: 8),
                              // DA label for Decision Authority section
                              Padding(
                                padding: EdgeInsets.only(left: 8),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToRoleSection(
                                        'decisionAuthority'),
                                    child: Text(
                                      'DA',
                                      style: TextStyle(
                                        fontWeight: activeSectionId ==
                                                'decisionAuthority'
                                            ? FontWeight.bold
                                            : FontWeight.w400,
                                        fontSize: 10,
                                        fontFamily: "TiemposText",
                                        color: activeSectionId ==
                                                'decisionAuthority'
                                            ? Colors.blue.shade700
                                            : Colors.black,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              // Space to align U with Users section
                              SizedBox(height: 8),
                              // U label for Users section
                              Padding(
                                padding: EdgeInsets.only(left: 8),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToRoleSection('users'),
                                    child: Text(
                                      'SDT',
                                      style: TextStyle(
                                        fontWeight: activeSectionId == 'users'
                                            ? FontWeight.bold
                                            : FontWeight.w400,
                                        fontFamily: "TiemposText",
                                        fontSize: 10,
                                        color: activeSectionId == 'users'
                                            ? Colors.blue.shade700
                                            : Colors.black,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                    ),
                  ),

                  // Right column for content
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ...(widget.showLegacySections
                              ? [
                                  // Legacy Use Cases section
                                  _buildUseCasesSection(
                                      context, roleSectionKeys),

                                  // Legacy Permissions section
                                  _buildPermissionsSection(
                                      context, roleSectionKeys),
                                ]
                              : [
                                  // Core Responsibilities section
                                  _buildCoreResponsibilitiesSection(
                                      context, roleSectionKeys),

                                  // Key Performance Indicators section
                                  _buildKPIsSection(context, roleSectionKeys),

                                  // Decision Authority section
                                  _buildDecisionAuthoritySection(
                                      context, roleSectionKeys),

                                  // Users section
                                  _buildUsersSection(context, roleSectionKeys),
                                ]),

                          // Dynamic bottom spacer
                          SizedBox(height: _bottomSpacerHeight),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // /// Detects the currently active section based on scroll position
  // void _detectActiveSection() {
  //   if (!_scrollController.hasClients) return;

  //   String? newActiveSection;
  //   double minDistance = double.infinity;

  //   // Check each section to find which one is closest to the top of the viewport
  //   for (final entry in roleSectionKeys.entries) {
  //     final key = entry.value;
  //     final context = key.currentContext;
  //     if (context != null) {
  //       try {
  //         final RenderBox renderBox = context.findRenderObject() as RenderBox;
  //         final position = renderBox.localToGlobal(Offset.zero);

  //         // Consider a section active if it's within the top portion of the viewport
  //         if (position.dy <= 100 && position.dy >= 0) {
  //           final distance = position.dy.abs();
  //           if (distance < minDistance) {
  //             minDistance = distance;
  //             newActiveSection = entry.key;
  //           }
  //         }
  //       } catch (e) {
  //         // Handle cases where the render object might not be available
  //         continue;
  //       }
  //     }
  //   }

  //   // Update active section if it changed
  //   if (newActiveSection != null && newActiveSection != activeSectionId) {
  //     setState(() {
  //       activeSectionId = newActiveSection;
  //     });
  //   }
  // }

  /// Handle visibility changes for sections using VisibilityDetector
  // void _onSectionVisibilityChanged(
  //     String sectionId, VisibilityInfo info, VisibilityInfo globalInfo) {
  //   // Only update if the section is significantly visible (more than 30% visible)
  //   globalInfo = info;
  //   if (info.visibleFraction > 1) {
  //     setState(() {
  //       activeSectionId = sectionId;
  //     });
  //   }
  // }

  /// Method to update active section based on visibility
  void _updateActiveSection() {
    String? mostVisibleSection;
    double maxVisibility = 0;

    sectionVisibilityInfo.forEach((sectionId, info) {
      if (info.visibleFraction > maxVisibility) {
        maxVisibility = info.visibleFraction;
        mostVisibleSection = sectionId;
      }
    });

    if (mostVisibleSection != null && mostVisibleSection != activeSectionId) {
      if (mounted) {
        setState(() {
          activeSectionId = mostVisibleSection;
        });
      }
    }
  }

  /// Builds a generic section widget with VisibilityDetector
  Widget _buildSection(
    BuildContext context,
    Map<String, GlobalKey> sectionKeys,
    String sectionId,
    String title,
    bool hasData,
    Widget Function() contentBuilder,
  ) {
    return VisibilityDetector(
      key: Key(sectionId),
      onVisibilityChanged: (VisibilityInfo info) {
        sectionVisibilityInfo[sectionId] = info;
        _updateActiveSection();
      },
      child: Container(
        key: sectionKeys[sectionId],
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$title:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 10,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 8),
            hasData ? contentBuilder() : _buildNoDataWidget(title),
          ],
        ),
      ),
    );
  }

  /// Builds a no data widget
  Widget _buildNoDataWidget(String sectionName) {
    return Text(
      'No $sectionName data available.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the core responsibilities section
  Widget _buildCoreResponsibilitiesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'coreResponsibilities',
      'Core Responsibilities',
      widget.role.coreResponsibilities != null &&
          widget.role.coreResponsibilities!.isNotEmpty,
      () => _buildCoreResponsibilitiesContent(context),
    );
  }

  /// Builds the core responsibilities content
  Widget _buildCoreResponsibilitiesContent(BuildContext context) {
    // Use dedicated coreResponsibilities field
    List<String> responsibilities = widget.role.coreResponsibilities ?? [];

    if (responsibilities.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: responsibilities
            .map((responsibility) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text('- ',
                      //     style: TextStyle(
                      //         fontSize: 14,
                      //         color: Colors.black,
                      //         fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () =>
                              _showChatDialog(context, 'core responsibilities'),
                          child: Text(
                            responsibility,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      );
    }

    return Text(
      'No core responsibilities defined for this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the KPIs section
  Widget _buildKPIsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'kpis',
      'Key Performance Indicators',
      widget.role.kpis != null && widget.role.kpis!.isNotEmpty,
      () => _buildKPIsContent(context),
    );
  }

  /// Builds the KPIs content
  Widget _buildKPIsContent(BuildContext context) {
    // Use dedicated kpis field and convert to strings
    List<dynamic> kpis = (widget.role.kpis ?? []).map((kpi) => kpi).toList();

    if (kpis.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: kpis
            .map((kpi) => Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('• ',
                              style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                  fontFamily: "TiemposText",
                                  fontWeight: FontWeight.bold)),
                          Expanded(
                            child: GestureDetector(
                                onTap: () => _showChatDialog(
                                    context, 'key performance indicators'),
                                child: RichText(
                                    text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: kpi.name,
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: "TiemposText"),
                                    ),
                                    TextSpan(
                                      text: ": ${kpi.description}",
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black,
                                          fontFamily: "TiemposText"),
                                    ),
                                  ],
                                ))),
                          ),
                        ],
                      ),
                      if (kpi.formula != null) ...[
                        Padding(
                          padding: const EdgeInsets.only(left: AppSpacing.xs),
                          child: Text(
                            '- Formula: ${kpi.formula}',
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ],
                      if (kpi.target != null) ...[
                        Padding(
                          padding: const EdgeInsets.only(left: AppSpacing.xs),
                          child: Text(
                            '- Target: ${kpi.target}',
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ],
                      if (kpi.measurementFrequency != null) ...[
                        Padding(
                          padding: const EdgeInsets.only(left: AppSpacing.xs),
                          child: Text(
                            '- Measurement Frequency: ${kpi.measurementFrequency}',
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ],
                    ],
                  ),
                ))
            .toList(),
      );
    }

    return Text(
      'No key performance indicators defined for this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the decision authority section
  Widget _buildDecisionAuthoritySection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'decisionAuthority',
      'Decision Authority',
      widget.role.decisionAuthority != null &&
          widget.role.decisionAuthority!.isNotEmpty,
      () => _buildDecisionAuthorityContent(context),
    );
  }

  /// Builds the decision authority content
  Widget _buildDecisionAuthorityContent(BuildContext context) {
    // Use dedicated decisionAuthority field
    List<String> authorities = widget.role.decisionAuthority ?? [];

    if (authorities.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: authorities
            .map((authority) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Text('• ',
                      //     style: TextStyle(
                      //         fontSize: 14,
                      //         color: Colors.black,
                      //         fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () =>
                              _showChatDialog(context, 'decision authority'),
                          child: Text(
                            authority,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      );
    }

    return Text(
      'No decision authority defined for this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the users section
  Widget _buildUsersSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'users',
      'Users',
      widget.users != null && widget.users!.isNotEmpty,
      () => _buildUsersContent(context),
    );
  }

  /// Builds the users content
  Widget _buildUsersContent(BuildContext context) {
    // Filter users assigned to this role
    List<User> assignedUsers = _getAssignedUsers();

    if (assignedUsers.isNotEmpty) {
      bool isAnyExpanded = expandedUserIds.isNotEmpty;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Grid of user cards
          GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio:
                  isAnyExpanded ? 0.4 : 1.5, // Adjust for card height
            ),
            itemCount: assignedUsers.length,
            itemBuilder: (context, index) {
              final user = assignedUsers[index];
              final userId = _getUserId(user);
              final isExpanded = expandedUserIds.contains(userId);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User card
                  _buildUserCard(context, user, userId, isExpanded),
                  // Expanded details (if expanded)
                  if (isExpanded) ...[
                    SizedBox(height: AppSpacing.sm),
                    _buildExpandedUserDetails(context, user),
                  ],
                ],
              );
            },
          ),
        ],
      );
    }

    return Text(
      'No users assigned to this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Get users assigned to this role
  List<User> _getAssignedUsers() {
    if (widget.users == null || widget.users!.isEmpty) {
      return [];
    }

    return widget.users!.where((user) {
      // Check if user has role assignments
      if (user.roleAssignments != null) {
        final primaryRole = user.roleAssignments!.primaryRole;
        // Match against role title or id
        return primaryRole == widget.role.title ||
            primaryRole == widget.role.id;
      }
      return false;
    }).toList();
  }

  /// Get user ID for tracking expanded state
  String _getUserId(User user) {
    // Use name as fallback ID if no other identifier is available
    return user.name ?? 'unknown_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Toggle user card expansion
  void _toggleUserExpansion(String userId) {
    setState(() {
      if (expandedUserIds.contains(userId)) {
        expandedUserIds.remove(userId);
      } else {
        expandedUserIds.add(userId);
      }
    });
  }

  /// Builds a user card widget
  Widget _buildUserCard(
      BuildContext context, User user, String userId, bool isExpanded) {
    return GestureDetector(
      onTap: () => _toggleUserExpansion(userId),
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.blue.shade200, width: 1),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user name and expand icon
            Row(
              children: [
                Expanded(
                  child: Text(
                    'User - ${user.name ?? 'Unknown User'}:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      fontFamily: "TiemposText",
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_down
                      : Icons.keyboard_arrow_right,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
            SizedBox(height: 8),
            // Primary user details
            _buildPrimaryUserDetails(user),
          ],
        ),
      ),
    );
  }

  /// Builds primary user details for the card
  Widget _buildPrimaryUserDetails(User user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (user.name != null) _buildUserDetailItem('Full Name', user.name!),
        if (user.personalInformation?.email != null)
          _buildUserDetailItem('Email', user.personalInformation!.email!),
        if (user.personalInformation?.phone != null)
          _buildUserDetailItem('Phone', user.personalInformation!.phone!),
        if (user.personalInformation?.employeeId != null)
          _buildUserDetailItem(
              'Employee ID', user.personalInformation!.employeeId!),
      ],
    );
  }

  /// Builds expanded user details
  Widget _buildExpandedUserDetails(BuildContext context, User user) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Authentication section
          if (user.authentication != null) ...[
            _buildExpandedSection('Authentication', [
              if (user.authentication!.username != null)
                'Username: ${user.authentication!.username}',
              if (user.authentication!.passwordPolicy != null)
                'Password Policy: ${user.authentication!.passwordPolicy}',
              if (user.authentication!.multiFactorAuthentication != null)
                'Multi-Factor Authentication: ${user.authentication!.multiFactorAuthentication}',
              if (user.authentication!.lastPasswordChange != null)
                'Last Password Change: ${user.authentication!.lastPasswordChange?.toIso8601String().split("T")[0]}',
            ]),
          ],

          // Role Assignments section
          if (user.roleAssignments != null) ...[
            _buildExpandedSection('Role Assignments', [
              if (user.roleAssignments!.primaryRole != null)
                'Primary Role: ${user.roleAssignments!.primaryRole}',
              if (user.roleAssignments!.secondaryRoles != null &&
                  user.roleAssignments!.secondaryRoles!.isNotEmpty)
                'Secondary Roles: ${user.roleAssignments!.secondaryRoles!.join(", ")}',
            ]),
          ],

          // Department and Team
          if (user.department != null || user.team != null) ...[
            _buildExpandedSection('Organization', [
              if (user.department != null) 'Department: ${user.department}',
              if (user.team != null) 'Team: ${user.team}',
            ]),
          ],

          // Reporting Structure
          if (user.reportingStructure != null) ...[
            _buildExpandedSection('Reporting Structure', [
              if (user.reportingStructure!.reportsTo != null)
                'Reports to: ${user.reportingStructure!.reportsTo}',
              if (user.reportingStructure!.directReports != null &&
                  user.reportingStructure!.directReports!.isNotEmpty)
                'Direct Reports: ${user.reportingStructure!.directReports!.join(", ")}',
            ]),
          ],

          // Access Control
          if (user.accessControl != null) ...[
            _buildExpandedSection('Access Control', [
              if (user.accessControl!.accountStatus != null)
                'Account Status: ${user.accessControl!.accountStatus}',
              if (user.accessControl!.accessLevel != null)
                'Access Level: ${user.accessControl!.accessLevel}',
              if (user.accessControl!.ipRestrictions != null)
                'IP Restrictions: ${user.accessControl!.ipRestrictions}',
              if (user.accessControl!.timeRestrictions != null)
                'Time Restrictions: ${user.accessControl!.timeRestrictions}',
            ]),
          ],

          // System Permissions
          if (user.systemPermissions != null) ...[
            _buildExpandedSection('System Permissions', [
              if (user.systemPermissions!.dataAccessScope != null)
                'Data Access Scope: ${user.systemPermissions!.dataAccessScope}',
              if (user.systemPermissions!.specialPermissions != null &&
                  user.systemPermissions!.specialPermissions!.isNotEmpty)
                'Special Permissions: ${user.systemPermissions!.specialPermissions!.join(", ")}',
            ]),
          ],

          // Activity Tracking
          if (user.activityTracking != null) ...[
            _buildExpandedSection('Activity Tracking', [
              if (user.activityTracking!.accountCreated != null)
                'Account Created: ${user.activityTracking!.accountCreated?.toIso8601String().split("T")[0]}',
              if (user.activityTracking!.lastLogin != null)
                'Last Login: ${user.activityTracking!.lastLogin?.toIso8601String().split("T")[0]}',
              if (user.activityTracking!.lastActivity != null)
                'Last Activity: ${user.activityTracking!.lastActivity?.toIso8601String().split("T")[0]}',
            ]),
          ],
        ],
      ),
    );
  }

  /// Builds a user detail item
  Widget _buildUserDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2),
      child: Text(
        '• $label: $value',
        style: TextStyle(
          fontSize: 11,
          fontFamily: "TiemposText",
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds an expanded section with title and items
  Widget _buildExpandedSection(String title, List<String> items) {
    if (items.isEmpty) return SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• $title:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 11,
              fontFamily: "TiemposText",
              color: Colors.black,
            ),
          ),
          SizedBox(height: 2),
          ...items.map((item) => Padding(
                padding: EdgeInsets.only(left: 8, bottom: 1),
                child: Text(
                  '- $item',
                  style: TextStyle(
                    fontSize: 10,
                    fontFamily: "TiemposText",
                    color: Colors.black87,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  /// Builds the legacy use cases section
  Widget _buildUseCasesSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'useCases',
      'Use cases',
      widget.role.useCases != null && widget.role.useCases!.isNotEmpty,
      () => _buildUseCasesContent(context),
    );
  }

  /// Builds the legacy use cases content
  Widget _buildUseCasesContent(BuildContext context) {
    List<String> useCases = widget.role.useCases ?? [];

    if (useCases.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: useCases
            .map((useCase) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ',
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _showChatDialog(context, 'use cases'),
                          child: Text(
                            useCase,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      );
    }

    return Text(
      'No use cases defined for this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Builds the legacy permissions section
  Widget _buildPermissionsSection(
      BuildContext context, Map<String, GlobalKey> roleSectionKeys) {
    return _buildSection(
      context,
      roleSectionKeys,
      'permissions',
      'Permissions',
      widget.role.permissions != null && widget.role.permissions!.isNotEmpty,
      () => _buildPermissionsContent(context),
    );
  }

  /// Builds the legacy permissions content
  Widget _buildPermissionsContent(BuildContext context) {
    // Get entity permissions
    List<String> entityPermissions = widget.role.permissions?['entities'] ?? [];
    // Get objective permissions
    List<String> objectivePermissions =
        widget.role.permissions?['objectives'] ?? [];

    if (entityPermissions.isNotEmpty || objectivePermissions.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Entity permissions
          if (entityPermissions.isNotEmpty) ...[
            Text(
              'Entity Permissions:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 4),
            ...entityPermissions.map((permission) => Padding(
                  padding: EdgeInsets.only(bottom: 4, left: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ',
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _showChatDialog(context, 'permissions'),
                          child: Text(
                            permission,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
            SizedBox(height: 8),
          ],
          // Objective permissions
          if (objectivePermissions.isNotEmpty) ...[
            Text(
              'Objective Permissions:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: "TiemposText",
              ),
            ),
            SizedBox(height: 4),
            ...objectivePermissions.map((objective) => Padding(
                  padding: EdgeInsets.only(bottom: 4, left: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('• ',
                          style: TextStyle(
                              fontSize: 14,
                              color: Colors.black,
                              fontFamily: "TiemposText")),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => _showChatDialog(context, 'objectives'),
                          child: Text(
                            objective,
                            style: TextStyle(
                                fontSize: 14,
                                color: Colors.black,
                                fontFamily: "TiemposText"),
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      );
    }

    return Text(
      'No permissions defined for this role.',
      style: TextStyle(
        fontFamily: 'TiemposText',
        fontSize: 14,
        fontStyle: FontStyle.italic,
        color: Colors.grey.shade700,
      ),
    );
  }

  /// Shows a chat dialog for commenting on role aspects
  void _showChatDialog(BuildContext context, String aspect) {
    if (widget.chatController == null || widget.onSendMessage == null) return;

    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return Stack(
          children: [
            Positioned(
              right: 30,
              top: 130,
              child: Dialog(
                backgroundColor: Colors.white,
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                insetPadding: EdgeInsets.zero,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppSpacing.md),
                    border: Border.all(
                      color: Color.fromARGB(255, 94, 162, 192),
                      width: 1,
                    ),
                    boxShadow: [],
                  ),
                  constraints: BoxConstraints(
                    maxHeight: 300,
                    minHeight: 200,
                  ),
                  width: MediaQuery.of(context).size.width / 3.8,
                  padding: EdgeInsets.all(AppSpacing.xs),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: TextField(
                          controller: widget.chatController,
                          maxLines: null,
                          decoration: InputDecoration(
                            focusColor: Colors.transparent,
                            hintText:
                                "You want to make a comment on ${widget.role.title} $aspect? Please put your points in details I will help you to integrate it.",
                            hintStyle: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.grey,
                              fontFamily: "TiemposText",
                            ),
                            hoverColor: Colors.transparent,
                            border:
                                OutlineInputBorder(borderSide: BorderSide.none),
                            enabledBorder:
                                OutlineInputBorder(borderSide: BorderSide.none),
                            focusedBorder:
                                OutlineInputBorder(borderSide: BorderSide.none),
                            errorBorder:
                                OutlineInputBorder(borderSide: BorderSide.none),
                            disabledBorder:
                                OutlineInputBorder(borderSide: BorderSide.none),
                          ),
                          onSubmitted: (_) => widget.onSendMessage?.call(),
                        ),
                      ),
                      SizedBox(height: 40),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          _buildHoverButton(Icons.mic_rounded, () {}),
                          _buildHoverButton(Icons.attachment, () {}),
                          _buildHoverButton(Icons.add, () {}),
                          _buildHoverButton(Icons.arrow_upward,
                              widget.onSendMessage ?? () {}),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Builds a hover button for the chat dialog
  Widget _buildHoverButton(IconData icon, VoidCallback onPressed) {
    return Padding(
      padding: EdgeInsets.only(left: 4),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: EdgeInsets.all(8),
          child: Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
        ),
      ),
    );
  }
}
