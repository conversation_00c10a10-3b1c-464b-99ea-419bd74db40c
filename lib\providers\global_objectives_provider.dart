import '../models/books/all_global_objective_model.dart';
import '../services/global_objectives_service.dart';
import 'base_provider.dart';

/// Provider for managing global objectives state and operations
class GlobalObjectivesProvider extends BaseProvider {
  final GlobalObjectivesService _globalObjectivesService = GlobalObjectivesService();

  AllGlobalObjectiveModel? _globalObjectives;
  List<Objective> _objectives = [];
  List<Objective> _filteredObjectives = [];
  String _searchQuery = '';

  /// Get the complete global objectives model
  AllGlobalObjectiveModel? get globalObjectives => _globalObjectives;

  /// Get all objectives
  List<Objective> get objectives => _objectives;

  /// Get filtered objectives based on search query
  List<Objective> get filteredObjectives => _filteredObjectives;

  /// Get current search query
  String get searchQuery => _searchQuery;

  /// Get objectives count
  int get objectivesCount => _objectives.length;

  /// Get filtered objectives count
  int get filteredObjectivesCount => _filteredObjectives.length;

  /// Get total count from API response
  int get totalCount => _globalObjectives?.totalCount ?? 0;

  /// Get filtered count from API response
  int get apiFilteredCount => _globalObjectives?.filteredCount ?? 0;

  /// Fetch all global objectives from the API
  Future<void> fetchGlobalObjectives({
    String? tenantId,
    String? authToken,
  }) async {
    await runWithLoadingAndErrorHandling<void>(
      () async {
        AllGlobalObjectiveModel fetchedObjectives;

        if (tenantId != null && authToken != null) {
          fetchedObjectives = await _globalObjectivesService.getAllGlobalObjectives(
            tenantId: tenantId,
            authToken: authToken,
          );
        } else {
          // Use default values if not provided
          fetchedObjectives = await _globalObjectivesService.getAllGlobalObjectivesWithDefaults();
        }

        _globalObjectives = fetchedObjectives;
        _objectives = fetchedObjectives.objectives ?? [];
        _applySearchFilter();
      },
      context: 'Fetching global objectives',
    );
  }

  /// Search objectives by name or description
  void searchObjectives(String query) {
    _searchQuery = query.toLowerCase().trim();
    _applySearchFilter();
    notifyListeners();
  }

  /// Clear search and show all objectives
  void clearSearch() {
    _searchQuery = '';
    _applySearchFilter();
    notifyListeners();
  }

  /// Apply search filter to objectives
  void _applySearchFilter() {
    if (_searchQuery.isEmpty) {
      _filteredObjectives = List.from(_objectives);
    } else {
      _filteredObjectives = _objectives.where((objective) {
        final objectiveName = objective.name?.toLowerCase() ?? '';
        final objectiveDescription = objective.description?.toLowerCase() ?? '';
        final bookName = objective.bookName?.toLowerCase() ?? '';
        final chapterName = objective.chapterName?.toLowerCase() ?? '';
        return objectiveName.contains(_searchQuery) ||
               objectiveDescription.contains(_searchQuery) ||
               bookName.contains(_searchQuery) ||
               chapterName.contains(_searchQuery);
      }).toList();
    }
  }

  /// Get objective by ID
  Objective? getObjectiveById(String objectiveId) {
    try {
      return _objectives.firstWhere((objective) => objective.goId == objectiveId);
    } catch (e) {
      return null;
    }
  }

  /// Get objectives by book ID
  List<Objective> getObjectivesByBookId(String bookId) {
    return _objectives.where((objective) => objective.bookId == bookId).toList();
  }

  /// Get objectives by status
  List<Objective> getObjectivesByStatus(String status) {
    return _objectives.where((objective) => objective.status == status).toList();
  }

  /// Get objectives by classification
  List<Objective> getObjectivesByClassification(String classification) {
    return _objectives.where((objective) => objective.classification == classification).toList();
  }

  /// Sort objectives by name (ascending or descending)
  void sortObjectivesByName({bool ascending = true}) {
    _objectives.sort((a, b) {
      final nameA = a.name ?? '';
      final nameB = b.name ?? '';
      return ascending ? nameA.compareTo(nameB) : nameB.compareTo(nameA);
    });
    _applySearchFilter();
    notifyListeners();
  }

  /// Sort objectives by creation date (ascending or descending)
  void sortObjectivesByCreatedAt({bool ascending = true}) {
    _objectives.sort((a, b) {
      final dateA = a.createdAt ?? DateTime.now();
      final dateB = b.createdAt ?? DateTime.now();
      return ascending ? dateA.compareTo(dateB) : dateB.compareTo(dateA);
    });
    _applySearchFilter();
    notifyListeners();
  }

  /// Sort objectives by last used date (ascending or descending)
  void sortObjectivesByLastUsed({bool ascending = true}) {
    _objectives.sort((a, b) {
      final dateA = a.lastUsed ?? DateTime.fromMillisecondsSinceEpoch(0);
      final dateB = b.lastUsed ?? DateTime.fromMillisecondsSinceEpoch(0);
      return ascending ? dateA.compareTo(dateB) : dateB.compareTo(dateA);
    });
    _applySearchFilter();
    notifyListeners();
  }

  /// Filter objectives by date range
  List<Objective> getObjectivesByDateRange(DateTime startDate, DateTime endDate) {
    return _objectives.where((objective) {
      final createdAt = objective.createdAt;
      if (createdAt == null) return false;
      return createdAt.isAfter(startDate) && createdAt.isBefore(endDate);
    }).toList();
  }

  /// Get unique book names from objectives
  List<String> getUniqueBookNames() {
    final bookNames = _objectives
        .map((objective) => objective.bookName)
        .where((name) => name != null)
        .cast<String>()
        .toSet()
        .toList();
    bookNames.sort();
    return bookNames;
  }

  /// Get unique statuses from objectives
  List<String> getUniqueStatuses() {
    final statuses = _objectives
        .map((objective) => objective.status)
        .where((status) => status != null)
        .cast<String>()
        .toSet()
        .toList();
    statuses.sort();
    return statuses;
  }

  /// Get unique classifications from objectives
  List<String> getUniqueClassifications() {
    final classifications = _objectives
        .map((objective) => objective.classification)
        .where((classification) => classification != null)
        .cast<String>()
        .toSet()
        .toList();
    classifications.sort();
    return classifications;
  }

  /// Refresh global objectives data
  Future<void> refreshGlobalObjectives({
    String? tenantId,
    String? authToken,
  }) async {
    await fetchGlobalObjectives(tenantId: tenantId, authToken: authToken);
  }

  /// Clear all global objectives data
  void clearGlobalObjectives() {
    _globalObjectives = null;
    _objectives.clear();
    _filteredObjectives.clear();
    _searchQuery = '';
    notifyListeners();
  }
}
